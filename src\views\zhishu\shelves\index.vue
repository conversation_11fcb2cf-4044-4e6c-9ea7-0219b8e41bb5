<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="货区编码" prop="code">
              <el-input v-model="queryParams.code" placeholder="请输入二级货区编码" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['shelves:shelves:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['shelves:shelves:edit']">修改</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['shelves:shelves:remove']">删除</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['shelves:shelves:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="shelvesList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="ID" align="center" prop="id" width="120" :show-overflow-tooltip="true">
          <template #default="{ row }">
            <div class="truncate-cell">  <!-- 文本截断容器 -->
              {{ row.id  }}
            </div>
          </template>
        </el-table-column>

        <el-table-column label="名称" align="center" prop="name" />
        <el-table-column label="二级货区编码" align="center" prop="code" />

        <el-table-column label="一级货区名称" align="center" prop="depotName" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['shelves:shelves:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['shelves:shelves:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改货架信息对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body>
      <el-form ref="shelvesFormRef" :model="form" :rules="rules" label-width="80px">
          <el-form-item label="货区名称" prop="depotId" >
            <el-select v-model="selectedId" :disabled="!dialog.isAdd" value-key="id" placeholder="请选择一级货区" :reserve-keyword="false" :get-option-key="getOptionKey" clearable filterable style="width: 100%" :loading="loading"  @update:model-value="handleDepotChange">
              <el-option v-for="item in depotList" :key="item.id" :label="item.name+''+item.unit" :value="item" :disabled="!dialog.isAdd" />
            </el-select>
          </el-form-item>

        <el-form-item label="名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入货区名称,不得超过10位">
            <template #append>
              <el-select v-model="selectFre" placeholder="" :disabled="!dialog.isAdd" style="width: 60px" @change="handleLocationTypeChange">
                <el-option v-for="item in locationTypes" :key="item.value" :label="item.label" :value="item.value"/>
              </el-select>
            </template>
          </el-input>
        </el-form-item>


        <el-form-item label="货区编码" prop="code">
          <el-input v-model="form.code" placeholder="请输入(字母+数字或纯数字),示例:1-9/AA-ZZ/A0-Z9" :disabled="!dialog.isAdd" />
        </el-form-item>

      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Shelves" lang="ts">
import { listShelves, getShelves, delShelves, addShelves, updateShelves, depotNameList } from '@/api/zhishu/shelves';
import { ShelvesVO, ShelvesQuery, ShelvesForm } from '@/api/zhishu/shelves/types';
import { DepotVO } from '@/api/zhishu/depot/types';
import { FormItemRule } from 'element-plus';
import { ref } from 'vue';

// 类型选项配置
const locationTypes = [
  { value: '库', label: '库' },
  { value: '架', label: '架' },
  { value: '层', label: '层' },
  { value: '位', label: '位' }
];

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const shelvesList = ref<ShelvesVO[]>([]);

const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const shelvesFormRef = ref<ElFormInstance>();

// 修改后 ✅
interface DepotOption {
  id: number
  name: string
}

const selectedId = ref<DepotOption | null>(null) // 严格匹配选项类型
const depotList = ref<DepotVO[]>([])

let selectFre =ref("")

const message=ref("");

const loadData = async () => {
  loading.value  = true
  try {
    const res = await   depotNameList()
    depotList.value=res.rows
  } catch (error) {
  } finally {
    loading.value  = false
  }
}

const dialog = reactive<DialogOption>({
  visible: false,
  title: '',
  isAdd: true
});

const initFormData: ShelvesForm = {
  id: undefined,
  name: undefined,
  sheCapMax: undefined,
  depotId: undefined,
  unit:"库",
  status: undefined,
  code: undefined,
  depotName: undefined
}
const data = reactive<PageData<ShelvesForm, ShelvesQuery>>({
  form: {...initFormData},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    name: undefined,
    unit:undefined,
    sheCapMax: undefined,
    status: undefined,
    code: undefined,
    depotName: undefined,
    params: {
    }
  }

});
// 表单规则对象
const rules = reactive({
  depotId:[
    { required: true,
      message: '请选择一级货区',
      trigger: ['blur', 'change']}
  ],
  name: [
    { required: true, message: '货区名称不能为空', trigger: 'blur' },
    { max: 10, message: '货区名称长度10位以内字符且首字符不为0', trigger: 'blur' }

  ],
  code: [ // 货架编码（大写字母+数字）
    { required: true, message: '请先选择一级货区', trigger: 'blur' },
    {
      pattern: /^(?:[1-9]|[A-Z]{2}|[a-z]{2}|[A-Z][0-9]|[a-z][0-9])$/,
      message: '格式示例:1-9/AA-ZZ/A0-Z9'
    }
  ]
} as Record<string, Array<FormItemRule>>);

const { queryParams, form} = toRefs(data);

const handleLocationTypeChange=async(val)=>{
  form.value.unit=val
}


/** 查询货架信息列表 */
const getList = async () => {
  loading.value = true;
  const res = await listShelves(queryParams.value);
  shelvesList.value = res.rows;
  total.value = res.total;
  loading.value = false;
}

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
}

/** 表单重置 */
const reset = () => {
  form.value = {...initFormData};
  shelvesFormRef.value?.resetFields();
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
}

/** 多选框选中数据 */
const handleSelectionChange = (selection: ShelvesVO[]) => {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = "添加二级货区信息";
  dialog.isAdd  = true; // 设置为新增操作
}

/** 修改按钮操作 */
const handleUpdate = async (row?: ShelvesVO) => {

  reset();
  const _id = row?.id || ids.value[0];
  const res = await getShelves(_id);
  selectFre.value = res.data.unit;

  selectedId.value={
    id:res.data.depotId,
    name:res.data.depotName
  }
  Object.assign(form.value,  res.data);
  dialog.visible  = true;
  dialog.title  = "修改二级货区信息";
  dialog.isAdd  = false; // 设置为修改操作
}

// 修复显示问题的关键配置
const getOptionKey = (item: Depot) => item.id  // 精确匹配逻辑
// 新增处理方法
const handleDepotChange = (val: DepotOption | null) => {
  form.value.depotId  = val?.id || null
  form.value.depotName  = val?.name || ''
}

/** 提交按钮 */
const submitForm = () => {
  shelvesFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateShelves(form.value).finally(() =>  buttonLoading.value = false);
        dialog.visible = false;
      } else {
          form.value.depotId  = selectedId.value?.id  || null  // 解构基础类型值
          await addShelves(form.value).finally(() =>  buttonLoading.value = false);
          proxy?.$modal.msgSuccess("操作成功");
          dialog.visible = false;
      }

      await getList();
    }
  });
}

/** 删除按钮操作 */
const handleDelete = async (row?: ShelvesVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除货架信息编号为"' + _ids + '"的数据项？').finally(() => loading.value = false);
  await delShelves(_ids);
  proxy?.$modal.msgSuccess("删除成功");
  await getList();
}

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download('shelves/shelves/export', {
    ...queryParams.value
  }, `shelves_${new Date().getTime()}.xlsx`)
}
// 自动同步字段
watch(() => form.value.selectedDepot,  (newVal) => {
  if (newVal) {
    form.value.depotId  = newVal.id
    form.value.depotName  = newVal.name
  }
})

onMounted(() => {
  getList();
  loadData();
});
</script>
<style>
/* 文本溢出处理 */
.truncate-cell {
  width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
