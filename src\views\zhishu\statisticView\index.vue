<template>
  <div class="dashboard-container">
    <!-- 统计卡片 -->
    <div class="stats-grid">
      <el-card class="stat-card">
        <div class="card-content">
          <span class="label">库房总数</span>
          <span class="value">{{ warehouses}}</span>
        </div>
      </el-card>
      <el-card class="stat-card">
        <div class="card-content">
          <span class="label">货架总数</span>
          <span class="value">{{shelvesNum}}</span>
        </div>
      </el-card>
    </div>

    <!-- 出入库柱状图 -->
    <!-- <div> -->
      <div class="echarts-box">
        <div id="inData" style=" width: 600px; height: 400px; "></div>
      </div>
    <!-- </div> -->

    <!-- 书籍分类饼图 -->
<!--    <div class="chart-container">-->
<!--      <div ref="pieChart" class="chart-box"></div>-->
<!--    </div>-->
  </div>
</template>




<script setup lang="ts">

import { statistic } from '@/api/zhishu/statistic/type';
import { listNum } from '@/api/zhishu/statistic';
import { data } from 'autoprefixer';
import { object } from 'vue-types';
import * as echarts from 'echarts';

const  stats=ref<statistic>({
  warehousesNum :0,
  shelvesNum :0,
  warehousesName:undefined,
  inQuantity:0,
  outQuantity:0,
  bookCategory:undefined
})

const warehouses=ref(0);
const shelvesNum=ref(0);
const inData=ref();

// 获取库房和货架总数
const  fetchData =async()=>{
  const res=await  listNum();
  warehouses.value=res.warehousesNum
  shelvesNum.value=res.shelvesNum
  return res
}
// const  inWarehose =async ()=>{
//    let mychart=echarts.init(inData.value)
//   const option = {
//    legend: {},
//   tooltip: {},
//   dataset: {
//     dimensions: ['product', '2015', '2016', '2017'],
//     source: [
//       { product: 'Matcha Latte', '2015': 43.3, '2016': 85.8, '2017': 93.7 },
//       { product: 'Milk Tea', '2015': 83.1, '2016': 73.4, '2017': 55.1 },
//       { product: 'Cheese Cocoa', '2015': 86.4, '2016': 65.2, '2017': 82.5 },
//       { product: 'Walnut Brownie', '2015': 72.4, '2016': 53.9, '2017': 39.1 }
//     ]
//   },
//   xAxis: { type: 'category' },
//   yAxis: {},
//   // Declare several bar series, each will be mapped
//   // to a column of dataset.source by default.
//   series: [{ type: 'bar' }, { type: 'bar' }, { type: 'bar' }]
// };

// mychart.setOption(option);
// }



onMounted(()=>{
  fetchData()
  // inWarehose()
})
</script>

<style lang="scss" scoped>
.dashboard-container {
  padding: 20px;
  background: #f5f7fa;

  .stats-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
    margin-bottom: 24px;

    .stat-card {
      .card-content {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 20px;

        .label {
          font-size: 14px;
          color: #909399;
          margin-bottom: 8px;
        }

        .value {
          font-size: 24px;
          font-weight: 500;
          color: #303133;
        }
      }
    }
  }

  .chart-container {
    background: #fff;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.05);

    .chart-box {
      width: 100%;
      height: 400px;
    }
  }
}
</style>








