<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter"
      :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="一级货区编号" prop="code" label-width="100px">
              <el-input v-model="queryParams.code" placeholder="请输入一级货区编号" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="一级货区名称" prop="name" label-width="100">
              <el-input v-model="queryParams.name" placeholder="请输入名称" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleDepotAdd"
              v-hasPermi="['depot:depot:add']">创建一级货区</el-button>
          </el-col>
          <!--   导出模板按钮-->
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Histogram" @click="handleDownload">仓库建立规则</el-button>
          </el-col>
<!--          <el-col :span="1.5">-->
<!--            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()"-->
<!--              v-hasPermi="['depot:depot:edit']">修改</el-button>-->
<!--          </el-col>-->
<!--          <el-col :span="1.5">-->
<!--            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()"-->
<!--              v-hasPermi="['depot:depot:remove']">删除</el-button>-->
<!--          </el-col>-->
<!--          <el-col :span="1.5">-->
<!--            <el-button type="warning" plain icon="Download" @click="handleExport"-->
<!--              v-hasPermi="['depot:depot:export']">导出</el-button>-->
<!--          </el-col>-->
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>
      <el-table
        :data="depotList"
        style="width: 100%"
        row-key="id"
        lazy
        :load="load"
        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
      >

        <el-table-column type="selection" width="55"/>
        <el-table-column label="货区名称" align="left" prop="name" width="240">
          <template #default="{ row }">
             {{row.name}}

            <el-tooltip placement="top" :open-delay="500">
              <template #content>
                <span v-if="row.level==1">二级货区数量：{{ row.sheNumber }}</span>
                <span v-else-if="row.level==2">三级货区数量：{{ row.sheNumber }}</span>
                <span v-else-if="row.level==3">书品类别数量：{{ row.categoryNumber}}</span>
              </template>
              <el-tag type="danger" effect="dark" round size="small">
                {{ row.level == 1 ? "一" : row.level == 2 ? "二" : "三" }}
                <span v-if="row.level == 1">({{ row.sheNumber }})</span>
                <span v-else-if="row.level == 2">({{ row.sheNumber }})</span>
              </el-tag>
            </el-tooltip>


<!--            <el-tag type="danger" effect="dark" round size="small">-->
<!--              {{row.level==1?"一":row.level==2?"二":"三"}}-->
<!--              <span v-if="row.level==1">({{row.sheNumber}})</span>-->
<!--              <span v-else-if="row.level==2">({{row.sheNumber}})</span>-->
<!--            </el-tag>-->
<!--            <el-icon><CaretRight /></el-icon>-->
<!--            <el-tag type="warning" effect="dark" size="small">-->
<!--              {{row.level==1?"数量":row.level==2?"三级":"书品"}} ({{row.sheNumber}})-->
<!--            </el-tag>-->

          </template>
        </el-table-column>
        <el-table-column label="货区编号" align="center" prop="code" />
        <el-table-column label="书品类别" align="center" prop="categoryNumber" />
        <el-table-column label="库存数量" align="center" prop="inventory" />
<!--        <el-table-column label="数量" align="center" prop="sheNumber">-->
<!--          <template #default="{ row }">-->
<!--            <el-tag type="warning" effect="dark" size="small">-->
<!--              {{row.level==1?"二级":row.level==2?"三级":"书品"}}-->
<!--            </el-tag>-->
<!--            {{row.sheNumber}}-->
<!--          </template>-->
<!--        </el-table-column>-->


        <el-table-column label="用户" align="center">
          <template #default="{ row }">
            <!-- 修正1：使用动态参数拼接 -->
            <el-link type="primary" :href="`/user?id=${row.userId}`" target="_blank">
              {{ row.userName }}
            </el-link>
          </template>
        </el-table-column>
<!--        <el-table-column label="货区名称" align="left" prop="id" />-->


        <!--        <el-table-column label="二级货区数量" align="center" prop="sheQuantityMax" />-->
<!--        <el-table-column label="二级货区已用数量" align="center" prop="sheNumber" />-->
<!--        <el-table-column label="一级货区地址" align="center" prop="address" />-->
<!--        <el-table-column label="管理员" align="center" prop="manager" />-->
        <el-table-column label="货区状态" align="center">
          <template #default="{ row }">
            <el-tag :type="row.status === '0' ? 'success' : 'danger'" :effect="row.status === '0' ? 'dark' : 'light'"
                    size="large">
              {{ row.status === '0' ? '正常' : '异常(未选择运费模版)' }}
            </el-tag>
          </template>
        </el-table-column>
<!--        <el-table-column label="备注" align="center" prop="remark" />-->
        <el-table-column label="运费模版选择" align="center" prop="templateName">
          <template #default="scope">
            <span v-if="scope.row.templateName===null">未选择</span>
            <span v-else>{{scope.row.templateName}}</span>
            <el-tooltip content="运费模板" placement="top" v-if="scope.row.level===1">
              <el-button link type="primary" icon="Document" @click="handleFreightTemplate(scope.row)"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>


        <el-table-column label="操作" align="left" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="新增" placement="top" v-if="scope.row.level!==3">
              <el-button link type="primary" icon="Plus" @click="handleAdd(scope.row)" :disabled="scope.row.status==='1'"></el-button>
            </el-tooltip>
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)"
                         v-hasPermi="['depot:depot:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)"
                         v-hasPermi="['depot:depot:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>
      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>







 <!-- 添加或修改一级货区信息设置对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body>
      <el-form ref="depotFormRef" :model="form" :rules="rulesDepotFrom" label-width="80px">
        <el-form-item label="名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入名称">
            <template #append>
                <el-select v-model="selected" placeholder="" style="width: 60px" @change="handleLDepotTypeChange">
                    <el-option v-for="item in locationTypes" :key="item.value" :label="item.label" :value="item.value"/>
                </el-select>
            </template>
          </el-input>
        </el-form-item>

        <el-form-item label="货区数量" prop="sheQuantityMax" :rules="SheRules">
          <el-input v-model="form.sheQuantityMax" placeholder="输入最大货区数量(纯数字,不得以0开头,不得超过255)"/>
        </el-form-item>
        <el-form-item label="货区编码" prop="code" >
          <el-input v-model="form.code" placeholder="请输入货区编码,格式示例: AA/A1/1A/,不能以0开头"  :disabled="!dialog.isAdd"/>
        </el-form-item>

        <el-form-item label="货区状态" prop="status" v-if="!dialog.isAdd" >
          <el-radio-group v-model="form.status" :disabled="form.templateId==null">
            <el-radio :label="0" border size="large" value="0">正常</el-radio>
            <el-radio :label="1" border size="large" value="1">停止</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="货区地址" prop="address">
          <el-input v-model="form.address" placeholder="请输入货区地址，例如 沈阳市和平区XXX街XX号" />
        </el-form-item>
        <el-form-item label="管理员" prop="manager">
          <el-input v-model="form.manager" placeholder="请输入管理员,不得超过8位" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" placeholder="请输入备注" />
        </el-form-item>

      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitDepotForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>



    <!-- 添加或修改二级货区信息对话框 -->
    <el-dialog :title="dialogShelves.title" v-model="dialogShelves.visible" width="500px" append-to-body>
      <el-form ref="shelvesFormRef" :model="shelvesFormData" :rules="rulesShelvesFrom" label-width="80px">
        <el-form-item label="货区名称" prop="depotId" >
          <el-select v-model="selectedId" :disabled="!dialogShelves.isAdd" value-key="id" placeholder="请选择一级货区" :reserve-keyword="false"  clearable filterable style="width: 100%" :loading="loading"  @update:model-value="handleDepotChange">
            <el-option v-for="item in depotList" :key="item.id" :label="item.name" :value="item" :disabled="!dialogShelves.isAdd" />
          </el-select>
        </el-form-item>

        <el-form-item label="名称" prop="name">
          <el-input v-model="shelvesFormData.name" placeholder="请输入货区名称,不得超过10位">
            <template #append>
              <el-select v-model="selectFre" placeholder="" :disabled="!dialogShelves.isAdd" style="width: 60px" @change="handleLocationTypeChange">
                <el-option v-for="item in locationTypes" :key="item.value" :label="item.label" :value="item.value"/>
              </el-select>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item label="货区编码" prop="code">
          <el-input v-model="shelvesFormData.code" placeholder="请输入(字母+数字或纯数字),示例:1-9/AA-ZZ/A0-Z9" :disabled="!dialogShelves.isAdd" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitShelvesForm">确 定</el-button>
          <el-button @click="cancelShe">取 消</el-button>
        </div>
      </template>
    </el-dialog>


    <!-- 添加或修改三级货区管理对话框 -->
    <el-dialog :title="dialogFreight.title" v-model="dialogFreight.visible" width="500px" append-to-body>
      <el-form ref="freightFormRef" :model="freightFormData"  label-width="80px">
        <el-form-item label="货区名称" prop="freightId">
          <el-select v-model="FreId" :disabled="!dialogFreight.isAdd"  value-key="id" placeholder="请选择二级货区" :reserve-keyword="false"  clearable filterable style="width: 100%" :loading="loading" >
            <el-option v-for="item in shelveList" :key="item.id" :label="item.name+''+item.unit" :value="item" @click="changeMessage" :disabled="!dialog.isAdd"/>
          </el-select>
        </el-form-item>
        <el-form-item label="名称" prop="name">
          <el-input v-model="freightFormData.name" placeholder="请输入名称">
            <template #append>
              <el-select v-model="fSelect" placeholder="" :disabled="!dialogFreight.isAdd" style="width: 60px" @change="handleLFreTypeChange">
                <el-option v-for="item in locationTypes" :key="item.value" :label="item.label" :value="item.value"/>
              </el-select>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item label="货区编码" prop="code" :rules="codeRules">
          <el-input v-model="freightFormData.code" :placeholder="message"   :disabled="!dialogFreight.isAdd  || !FreId" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitFreightForm">确 定</el-button>
          <el-button @click="cancelFre">取 消</el-button>
        </div>
      </template>
    </el-dialog>


<!--    运费模版选择-->
    <el-dialog title="运费模板选择" v-model="freightDialog.visible" width="500px" append-to-body>
      <el-form-item label="模版选择" prop="id" >
        <el-select v-model="templateId"  value-key="id" placeholder="请选择模版" :reserve-keyword="false" clearable filterable style="width: 100%" :loading="loading"  @update:model-value="handleDepotChange">
          <el-option v-for="item in templateList" :key="item.id" :label="item.templateName" :value="item"  />
        </el-select>
      </el-form-item>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submit1">确 定</el-button>
          <el-button @click="cancelFreightTemplate">取 消</el-button>
        </div>
      </template>
    </el-dialog>


  </div>


</template>

<script setup name="Depot" lang="ts">
import {
  listDepot,
  getDepot,
  delDepot,
  addDepot,
  updateDepot,
  freList,
  shelveInfoList
} from '@/api/zhishu/depot';
import { DepotVO, DepotQuery, DepotForm } from '@/api/zhishu/depot/types';
import { TDistrictVo } from '@/api/zhishu/district/types';
import { ref } from 'vue';
import { templateNameList } from '@/api/zhishu/logistics';
import { ShelvesForm, ShelvesVO } from '@/api/zhishu/shelves/types';
import { addShelves, delShelves, depotNameList, getShelves, listShelves, updateShelves } from '@/api/zhishu/shelves';
import { addFreight, delFreight, getFreight, shelveListData, updateFreight } from '@/api/zhishu/freight';
import { FreightForm } from '@/api/zhishu/freight/types';
import { FormItemRule } from 'element-plus';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const depotList = ref<DepotVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
let curruntRow = null;

const queryFormRef = ref<ElFormInstance>();
const depotFormRef = ref<ElFormInstance>();
const shelvesFormRef = ref<ElFormInstance>();
const freightFormRef = ref<ElFormInstance>();
const temName=ref("未选择");

// 一级货区
const dialog = reactive<DialogOption>({
  visible: false,
  title: '',
  isAdd: true // 默认是新增操作
});
interface DepotOption1 {
  id: number
  templateName: string
}
const templateId = ref<DepotOption1 | null>(null) // 严格匹配选项类型
const templateList = ref<DepotVO[]>([])


// 二级货区
const dialogShelves = reactive<DialogOption>({
  visible: false,
  title: '',
  isAdd: true
});
interface DepotOption {
  id: number
  name: string
}
const selectedId = ref<DepotOption | null>(null) // 严格匹配选项类型
let selectFre =ref("")


// 三级货区
interface ShelveOption {
  id: number
  name: string
  code:string
}
const FreId = shallowRef<ShelveOption | null>(null)
let fSelect=ref("")
const shelveList = ref<ShelvesVO[]>([])
const dialogFreight = reactive<DialogOption>({
  visible: false,
  title: '',
  isAdd: true // 默认是新增操作
});

// const selectable = (row: DepotVO) => ![1, 31].includes(row.id)

// 加载二级和三级数据
const load = (row: DepotVO, treeNode: unknown, resolve: (data: any[]) => void) => {
  const _id = row.id;
  if(row.level===1){
    setTimeout(() => {
      loadSheData();
      const res = shelveInfoList(_id).then(response  => {
        const dataArray=response.data;  // 获取核心数据
        resolve(dataArray)
      })
    }, 500)
  }else if(row.level==2){
    setTimeout(() => {
      const res = freList(_id).then(response  => {
        const dataArray=response.data;  // 获取核心数据
        resolve(dataArray)
      })
    }, 500)
  }
};



// 二级货区数据
const loadSheData = async () => {
  loading.value  = true
  try {
    const res = await   shelveListData()
    shelveList.value=res.rows
  } catch (error) {
  } finally {
    loading.value  = false
  }
}



//获取运费模版名称和id
const loadData = async () => {
  loading.value  = true
  try {
    const res = await   templateNameList()
    templateList.value=res.rows
    // return res.rows  || []
  } catch (error) {
  } finally {
    loading.value  = false
  }
}
const handleDepotChange = (val: DepotOption | null) => {
  form.value.id  = val?.id || null
  form.value.depotName  = val?.templateName || ''
}


// 运费模板对话框状态
const freightDialog = reactive({
  visible: false,
  title: '运费模板设置'
});

// 运费模板表单数据
const freightForm = reactive({
  templateName: '',
  deliveryArea: [],
  pricingMethod: 'weight',
  deliveryMethod: 'express',
  deliveryNote: ''
});

// 类型选项配置
const locationTypes = [
  { value: '库', label: '库' },
  { value: '区', label: '区' },
  { value: '架', label: '架' },
  { value: '层', label: '层' },
  { value: '位', label: '位' }
];

// 格式化节点数据
const formatNode = (level: number, isLeaf = false) => (item: TDistrictVo) => ({
  value: item.id,
  label: item.name,
  leaf: isLeaf,
  level: level
});

/** 取消运费模板 */
const cancelFreightTemplate = () => {
  freightDialog.visible = false;
};

let selected=ref<string>("");

// 一级货区表单规则
const rulesDepotFrom = reactive({
  name: [
    { required: true, message: '货区名称不能为空', trigger: 'blur' },
    { max: 10, message: '货区名称长度不能超过10位', trigger: 'blur' }

  ],
  manager: [
    { required: true, message: '管理员不能为空', trigger: 'blur' },
    { max: 8, message: '管理员姓名长度不能超过8位', trigger: 'blur' }
  ],
  code:[
    { required: true, message: '获取编码不能为空', trigger: 'blur' },
    {
      pattern: /^(?:[A-Z]{2}|[a-z]{2}|[A-Z][0-9]|[a-z][0-9]|[1-9][a-zA-Z])$/i,
      message: '格式示例: AA/aa/A1/a1/1A/1a,不能以0开头',
      trigger: 'blur'
    }
  ]
});

// 二级货区表单规则
const rulesShelvesFrom = reactive({
  name: [
    { required: true, message: '货区名称不能为空', trigger: 'blur' },
    { max: 10, message: '货区名称长度10位以内字符且首字符不为0', trigger: 'blur' }

  ],
  code: [ // 货架编码（大写字母+数字）
    { required: true, message: '请先选择一级货区', trigger: 'blur' },
    {
      pattern: /^(?:[1-9]|[A-Z]{2}|[a-z]{2}|[A-Z][0-9]|[a-z][0-9])$/,
      message: '格式示例:1-9/AA-ZZ/A0-Z9'
    }
  ]
} as Record<string, Array<FormItemRule>>);


// 三级货区表单规则
const codeRules = computed(() => {
  const mes = FreId.value.code || '';
  const baseRule = {
    required: true,
    message: '请选择二级货区,并不能为空',
    trigger: 'blur'
  };

  if (mes.length===2) {
    return [
      baseRule,
      {
        pattern: /^[0-9a-z]+$/, // 一位数 0-9
        message: '请输入0-9之间的数字或a-z小写字母',
        trigger: 'blur'
      }
    ];
  } else if (mes.length===1) {
    return [
      baseRule,
      {
        pattern: /^([0-9]|[1-9][0-9]|[a-zA-Z][0-9])$/ , // 两位数 01-99
        message:'请输入0-99之间的数字或A0-Z9',
        trigger: 'blur',
        transform: value => value.replace(/^0+/,  '')
      }
    ];
  } else {
    return [baseRule];
  }
});
const message=ref("");
const changeMessage = async ()=>{
  const  mes=FreId.value.code;
  if(mes.length===2){
    message.value="请输入货区编码(数字),例如：0-9或小写字母a-z"
  }else if (mes.length===1){
    message.value="请输入货区编码(数字或字母+数字),例如：0-99-A0-Z9"
  }else{
    message.value="请重新更改二级货区名称"
  }
}



const SheRules = computed(() => {
  const value = form.value.sheQuantityMax;

  return [
    {
      required: true,
      message: '请输入最大货区数量',
      trigger: ['blur', 'change'] // 同时监听输入变化
    },
    {
      validator: (_, val, callback) => {
        const num = Number(val);
        if (isNaN(num)) {
          callback(new Error('必须输入数字'));
        } else if (num > 255) {
          callback(new Error('不得超过255'));
        } else if (/^0\d+/.test(val)) {
          callback(new Error('不能以0开头'));
        } else {
          callback();
        }
      },
      trigger: 'blur'
    }
  ];
});



const initFormData: DepotForm = {
  id: undefined,
  code: undefined,
  name: undefined,
  unit:"库",
  address: undefined,
  manager: undefined,
  status: "1",
  remark: undefined,
  sheQuantityMax: undefined,
  sheNumber:undefined,
  userName: undefined,
  userId: undefined,
  hasChildren:undefined,
  level:undefined,
  templateId:undefined
}
const data = reactive<PageData<DepotForm, DepotQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    code: undefined,
    name: undefined,
    unit:undefined,
    address: undefined,
    sheNumber:undefined,
    manager: undefined,
    status: undefined,
    userName: undefined,
    hasChildren:undefined,
    children:undefined,
    templateId:undefined,
    params: {
    }
  }
});

// 二级货区
const  shelvesFormData = reactive({
  id: undefined,
  name: undefined,
  sheCapMax: undefined,
  depotId: undefined,
  unit:"库",
  status: undefined,
  code: undefined,
  depotName: undefined,
  level:undefined
})

// 三级货区
const freightFormData=reactive({
  id: undefined,
  shelvesId: undefined,
  code: undefined,
  name: undefined,
  unit:"库",
  capMax: undefined,
  artNo: undefined,
  status: undefined,
  shelvesName:undefined,
  level:undefined
})

// const freightFormData=reactive(freightfrom1())
const { queryParams, form } = toRefs(data);



// 运费模板表单数据
const tForm = reactive({
  id: undefined,
  code: undefined,
  name: undefined,
  unit:"库",
  address: undefined,
  manager: undefined,
  status: undefined,
  remark: undefined,
  sheQuantityMax: undefined,
  sheNumber:undefined,
  userName: undefined,
  userId: undefined,
  templateId:undefined,
});


const handleFreightTemplate = async (row?: DepotVO) => {
  freightDialog.visible = true;
  reset();
  const _id = row?.id || ids.value[0]
  const res = await getDepot(_id);
  console.log(res)
  templateId.value={
    id:res.data.templateId,
    templateName:res.data.templateName,
  }
  Object.assign(tForm, res.data);
  freightDialog.visible = true;
  freightDialog.isAdd = false; // 设置为修改操作
}

//导出仓库规则事件
/** 导出模板按钮操作 */
const handleDownload = () => {
  ElMessageBox.confirm('确定要下载仓库建立规则吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'info'
  }).then(() => {
    // 创建模板数据
    downloadTemplate()
    ElMessage.success('模板下载成功')
  }).catch(() => {
    // 取消下载
    ElMessage.info('已取消下载')
  })
}
const downloadTemplate = () => {
  //模板放置路径public/templates/*.xlsx
  const fileUrl = '/templates/depotRule.doc'
  const link = document.createElement("a")
  link.href = fileUrl
  link.download = '仓库建立规则.doc' // 设置下载文件名
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}



/** 提交按钮 */
const submit1 = () => {

  reset();

  tForm.templateId = templateId.value?.id  || null  // 解构基础类型值
  tForm.status="0";
  updateDepot(tForm).finally(() => buttonLoading.value = false);
  temName.value=templateId.value.templateName;
  proxy?.$modal.msgSuccess("操作成功");
  freightDialog.visible = false;
  getList();

}

const handleLDepotTypeChange=async(val)=>{
  form.value.unit=val
}
const handleLocationTypeChange=async(val)=>{
   shelvesFormData.unit=val
}

const handleLFreTypeChange=async(val)=>{
  freightFormData.unit=val
}
/** 查询仓库信息设置列表 */
const getList = async () => {
  loading.value = true;
  const res = await listDepot(queryParams.value);
  depotList.value = res.rows;
  total.value = res.total;
  loading.value = false;
}

/** 一级货区取消按钮 */
const cancel =() => {
  reset();
  dialog.visible = false;
}
/** 二级货区取消按钮 */
const cancelShe =() => {
  reset();
  dialogShelves.visible = false;
}
/** 三级货区取消按钮 */
const cancelFre =() => {
  reset();
  dialogFreight.visible = false;
}
/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  depotFormRef.value?.resetFields();
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
}
//
// /** 多选框选中数据 */
// const handleSelectionChange = (selection: DepotVO[]) => {
//   ids.value = selection.map(item => item.id);
//   single.value = selection.length != 1;
//   multiple.value = !selection.length;
// }



/** 一级货区新增按钮操作 */
const handleDepotAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = "添加一级货区信息设置";
  dialog.isAdd = true; // 设置为新增操作
}

/** 二三级货区新增按钮操作 */
const handleAdd =async (row?: DepotVO) => {
  reset();
  if(row.level==1){
    dialogShelves.visible  = true;
    dialogShelves.title  = "创建二级货区信息";
    selectedId.value={
      id:row.id,
      name:row.depotName
    }
    dialogShelves.isAdd  = true;
  }else if(row.level==2){
    dialogFreight.visible = true;
    dialogFreight.title = "创建三级货区信息";
    FreId.value={
      id:row.id,
      name:row.name,
      code:row.code
    };
    changeMessage();
    dialogFreight.isAdd = true;
  }
}

/** 修改按钮操作 */
const handleUpdate = async (row?: DepotVO) => {
  reset();
  const _id = row?.id || ids.value[0]
  if(row.level==1){
    const res = await getDepot(_id);
    selected.value = res.data.unit;
    Object.assign(form.value, res.data);
    dialog.visible = true;
    dialog.title = "修改一级货区信息设置";
    dialog.isAdd = false; // 设置为修改操作
  }else if(row.level==2){
    const res = await getShelves(_id);
    selectFre.value = res.data.unit;
    selectedId.value={
      id:res.data.depotId,
      name:res.data.depotName
    }
    Object.assign(shelvesFormData,  res.data);

    dialogShelves.visible  = true;
    dialogShelves.title  = "修改二级货区信息";
    dialogShelves.isAdd  = false; // 设置为修改操作
  }else if(row.level==3){
    const res = await getFreight(_id);
    const sheList = await getShelves(res.data.shelvesId);

    fSelect.value=res.data.unit;

    FreId.value={
      id:res.data.shelvesId,
      name:res.data.shelvesName,
      code:sheList.data.code
    };

    Object.assign(freightFormData, res.data);
    changeMessage();
    dialogFreight.visible = true;
    dialogFreight.title = "修改三级货区管理";
    dialogFreight.isAdd = false; // 设置为修改操作
  }
}

/** 一级货区提交按钮 */
const submitDepotForm = () => {
  depotFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateDepot(form.value).finally(() => buttonLoading.value = false);
      } else {
        await addDepot(form.value).finally(() => buttonLoading.value = false);
      }
      proxy?.$modal.msgSuccess("操作成功");
      dialog.visible = false;
      await getList();
    }
  });
}

/** 二级货区提交按钮 */
const submitShelvesForm = () => {
  shelvesFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
        buttonLoading.value = true;
      if (shelvesFormData.id) {
        await updateShelves(shelvesFormData).finally(() =>  buttonLoading.value = false);
        dialogShelves.visible = false;
      } else {
        shelvesFormData.depotId  = selectedId.value?.id  || null  // 解构基础类型值
        await addShelves(shelvesFormData).finally(() =>  buttonLoading.value = false);
        proxy?.$modal.msgSuccess("操作成功");
        dialogShelves.visible = false;
      }
      await getList();
    }
    location.reload();
  });
}

/** 三级货区提交按钮 */
const submitFreightForm = () => {
  freightFormRef.value?.validate(async (valid: boolean) => {
    // freightFormData
    if (valid) {
      buttonLoading.value = true;
      if (freightFormData.id) {
        await updateFreight(freightFormData).finally(() =>  buttonLoading.value = false);
      } else {
        freightFormData.shelvesId  = FreId.value?.id  || null  // 解构基础类型值
        await addFreight(freightFormData).finally(() =>  buttonLoading.value = false);
      }
      proxy?.$modal.msgSuccess("操作成功");
      dialogFreight.visible = false;
      location.reload();
    }
  });
}


/** 删除按钮操作 */
const handleDelete = async (row?: DepotVO) => {
  const _ids = row?.id || ids.value;
  if(row.level==1){
    await proxy?.$modal.confirm('是否确认删除一级货区信息设置编号为"' + _ids + '"的数据项？').finally(() => loading.value = false);
    await delDepot(_ids);
  }else if(row.level==2){
    await proxy?.$modal.confirm('是否确认删除二级货区信息编号为"' + _ids + '"的数据项？').finally(() => loading.value = false);
    await delShelves(_ids);
  }else if(row.level==3){
    await proxy?.$modal.confirm('是否确认删除三级货区管理编号为"' + _ids + '"的数据项？').finally(() => loading.value = false);
    await delFreight(_ids);
  }
  proxy?.$modal.msgSuccess("删除成功");
  await getList();
  location.reload();
}



/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download('depot/depot/export', {
    ...queryParams.value
  }, `depot_${new Date().getTime()}.xlsx`)
}

onMounted(() => {
  getList();
  loadData();
});
</script>
<style>
/* 文本溢出处理 */
.truncate-cell {
  width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 增强悬浮提示样式 */
.el-tooltip__popper {
  max-width: 400px;
  word-break: break-all;
}

/* 运费模板样式 */
.freight-template-container {
  padding: 10px;
}

.overseas-section {
  margin-top: 20px;
  border-top: 1px solid #ebeef5;
  padding-top: 15px;
}
</style>
