<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="货区编码" prop="code">
              <el-input v-model="queryParams.code" placeholder="请输入货位编码" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['zhishu:freight:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['zhishu:freight:edit']">修改</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['zhishu:freight:remove']">删除</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['zhishu:freight:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="freightList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="40" align="center" />
        <el-table-column label="货区ID" align="center" prop="id" v-if="true" :show-overflow-tooltip="true">
          <template #default="{ row }">
            <div class="truncate-cell"> <!-- 文本截断容器 -->
              {{ row.id }}
            </div>
          </template>
        </el-table-column>
       <el-table-column label=" 货区编码" align="center" prop="code" />
        <el-table-column label="名称" align="center" prop="name" />
        <el-table-column label="二级名称" align="center" prop="shelvesName" />
        <el-table-column label="货位编码" align="center" prop="comcode" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['zhishu:freight:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['zhishu:freight:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改三级货区管理对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body>
      <el-form ref="freightFormRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="货区名称" prop="freightId">
          <el-select v-model="selectedId" :disabled="!dialog.isAdd"  value-key="id" placeholder="请选择二级货区" :reserve-keyword="false"  clearable filterable style="width: 100%" :loading="loading" >
            <el-option v-for="item in shelveList" :key="item.id" :label="item.name+''+item.unit" :value="item" @click="changeMessage" :disabled="!dialog.isAdd"/>
          </el-select>
        </el-form-item>

        <el-form-item label="名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入名称">
            <template #append>
              <el-select v-model="fSelect" placeholder="" :disabled="!dialog.isAdd" style="width: 60px" @change="handleLocationTypeChange">
                <el-option v-for="item in locationTypes" :key="item.value" :label="item.label" :value="item.value"/>
              </el-select>
            </template>
          </el-input>
        </el-form-item>


        <el-form-item label="货区编码" prop="code" :rules="codeRules">
          <el-input v-model="form.code" :placeholder="message"   :disabled="!dialog.isAdd  || !selectedId" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Freight" lang="ts">


import { FreightForm, FreightQuery, FreightVO } from '@/api/zhishu/freight/types';
import { ShelvesVO } from '@/api/zhishu/shelves/types';
import { addFreight, delFreight, getFreight, listFreight, shelveListData, updateFreight } from '@/api/zhishu/freight';
import { ref } from 'vue';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const freightList = ref<FreightVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const freightFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: '',
  isAdd: true // 默认是新增操作
});

// 类型选项配置
const locationTypes = [
  { value: '库', label: '库' },
  { value: '架', label: '架' },
  { value: '层', label: '层' },
  { value: '位', label: '位' }
];


const initFormData: FreightForm = {
  id: undefined,
  shelvesId: undefined,
  code: undefined,
  name: undefined,
  unit:"库",
  capMax: undefined,
  artNo: undefined,
  status: undefined,
  shelvesName:undefined,
}
const data = reactive<PageData<FreightForm, FreightQuery>>({
  form: {...initFormData},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    shelvesId: undefined,
    code: undefined,
    name: undefined,
    unit:undefined,
    capMax: undefined,
    artNo: undefined,
    status: undefined,
    shelvesName:undefined,
    comcode:undefined,
    params: {
    }
  },
  rules: {
    name:[
      { required: true, message: '货区名称不能为空', trigger: 'blur' },
      { max: 10, message: '货区名称长度不能超过10位', trigger: 'blur' }
    ]
  }
});

const { queryParams,rules, form } = toRefs(data);

const message=ref("");

// const rules = reactive({
//     freightId:[
//       { required: true,
//         message: '请选择二级货区',
//         trigger: ['blur', 'change']}
//     ]
// }
// )
const codeRules = computed(() => {
  const mes = selectedId.value?.code || '';

  const baseRule = {
    required: true,
    message: '请选择二级货区,并不能为空',
    trigger: 'blur'
  };

  if (mes.length===2) {
    return [
      baseRule,
      {
        pattern: /^[0-9]$/, // 一位数 0-9
        message: '请输入1-9之间的数字',
        trigger: 'blur'
      }
    ];
  } else if (mes.length===1) {
    return [
      baseRule,
      {
        pattern: /^([0-9]|[1-9][0-9]|[a-zA-Z][0-9])$/ , // 两位数 01-99
        message:'请输入0-99之间的数字或A0-Z9',
        trigger: 'blur'
      }
    ];
  } else {
    return [baseRule];
  }
});

const changeMessage = async ()=>{

  const  mes=selectedId.value.code
  if(mes.length===2){
    message.value="请输入货区编码(数字),例如：0-9"
  }else if (mes.length===1){
    message.value="请输入货区编码(数字或字母+数字),例如：0-99-A0-Z9"
  }else{
    message.value="请重新更改二级货区名称"
  }
}




interface ShelveOption {
  id: number
  name: string
  code:string
}
const selectedId = shallowRef<ShelveOption | null>(null) // 严格匹配选项类型
const shelveList = ref<ShelvesVO[]>([])


let fSelect=ref("")

const loadSheData = async () => {
  loading.value  = true
  try {
    const res = await   shelveListData()
    shelveList.value=res.rows
  } catch (error) {
  } finally {
    loading.value  = false
  }
}

const handleLocationTypeChange=async(val)=>{
  form.value.unit=val
}

/** 查询三级货区管理列表 */
const getList = async () => {
  loading.value = true;
  const res = await listFreight(queryParams.value);
  freightList.value = res.rows;
  total.value = res.total;
  loading.value = false;
}

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
}

/** 表单重置 */
const reset = () => {
  form.value = {...initFormData};
  freightFormRef.value?.resetFields();
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
}

/** 多选框选中数据 */
const handleSelectionChange = (selection: FreightVO[]) => {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = "添加三级货区管理";
  dialog.isAdd = true; // 设置为修改操作

}

/** 修改按钮操作 */
const handleUpdate = async (row?: FreightVO) => {
  reset();
  const _id = row?.id || ids.value[0]
  const res = await getFreight(_id);
  fSelect.value=res.data.unit;
  selectedId.value={
    id:res.data.shelvesId,
    name:res.data.shelvesName,
    code:res.data.code
  };
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = "修改三级货区管理";
  dialog.isAdd = false; // 设置为修改操作
}

/** 提交按钮 */
const submitForm = () => {
  freightFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateFreight(form.value).finally(() =>  buttonLoading.value = false);
      } else {
        form.value.shelvesId  = selectedId.value?.id  || null  // 解构基础类型值
        await addFreight(form.value).finally(() =>  buttonLoading.value = false);

      }
      proxy?.$modal.msgSuccess("操作成功");
      dialog.visible = false;
      await getList();
    }
  });
}



/** 删除按钮操作 */
const handleDelete = async (row?: FreightVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除三级货区管理编号为"' + _ids + '"的数据项？').finally(() => loading.value = false);
  await delFreight(_ids);
  proxy?.$modal.msgSuccess("删除成功");
  await getList();
}

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download('zhishu/freight/export', {
    ...queryParams.value
  }, `freight_${new Date().getTime()}.xlsx`)
}

onMounted(() => {
  loadSheData();
  getList();
});
</script>
<style>
/* 文本溢出处理 */
.truncate-cell {
  width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
