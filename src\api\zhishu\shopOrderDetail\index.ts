import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { ShopOrderDetailVO, ShopOrderDetailForm, ShopOrderDetailQuery } from '@/api/zhishu/shopOrderDetail/types';

/**
 * 查询订单详情列表
 * @param query
 * @returns {*}
 */

export const listShopOrderDetail = (query?: ShopOrderDetailQuery): AxiosPromise<ShopOrderDetailVO[]> => {
  return request({
    url: '/zhishu/shopOrderDetail/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询订单详情详细
 * @param id
 */
export const getShopOrderDetail = (id: string | number): AxiosPromise<ShopOrderDetailVO> => {
  return request({
    url: '/zhishu/shopOrderDetail/' + id,
    method: 'get'
  });
};

/**
 * 新增订单详情
 * @param data
 */
export const addShopOrderDetail = (data: ShopOrderDetailForm) => {
  return request({
    url: '/zhishu/shopOrderDetail',
    method: 'post',
    data: data
  });
};

/**
 * 修改订单详情
 * @param data
 */
export const updateShopOrderDetail = (data: ShopOrderDetailForm) => {
  return request({
    url: '/zhishu/shopOrderDetail',
    method: 'put',
    data: data
  });
};

/**
 * 删除订单详情
 * @param id
 */
export const delShopOrderDetail = (id: string | number | Array<string | number>) => {
  return request({
    url: '/zhishu/shopOrderDetail/' + id,
    method: 'delete'
  });
};
