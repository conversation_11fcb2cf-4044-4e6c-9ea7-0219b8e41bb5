{"$schema": "https://json.schemastore.org/tsconfig", "name": "ruoyi-vue-plus", "version": "5.3.0", "description": "与书同行", "author": "LionLi", "license": "MIT", "type": "module", "scripts": {"dev": "vite serve --mode development", "build:prod": "vite build --mode production", "build:dev": "vite build --mode development", "preview": "vite preview", "lint:eslint": "eslint", "lint:eslint:fix": "eslint --fix", "prettier": "prettier --write ."}, "repository": {"type": "git", "url": "https://gitee.com/JavaLionLi/plus-ui.git"}, "dependencies": {"@element-plus/icons-vue": "2.3.1", "@highlightjs/vue-plugin": "2.1.0", "@vueup/vue-quill": "1.2.0", "@vueuse/core": "11.3.0", "@zxing/browser": "^0.1.5", "@zxing/library": "^0.21.3", "animate.css": "4.1.1", "await-to-js": "3.0.0", "axios": "1.7.8", "blueimp-md5": "^2.19.0", "crypto-js": "4.2.0", "diagram-js": "12.3.0", "didi": "9.0.2", "echarts": "5.5.0", "el-table-horizontal-scroll": "^1.2.5", "element-china-area-data": "^6.1.0", "element-plus": "2.8.8", "file-saver": "2.0.5", "fuse.js": "7.0.0", "highlight.js": "11.9.0", "image-conversion": "2.1.1", "js-cookie": "3.0.5", "jsencrypt": "3.3.2", "nprogress": "0.2.0", "pdf-lib": "^1.17.1", "pinia": "2.2.6", "qrcode-vue": "^1.2.0", "qrcode.vue": "^3.6.0", "screenfull": "6.0.2", "vue": "3.5.13", "vue-barcode": "^1.3.0", "vue-cropper": "1.1.1", "vue-i18n": "10.0.5", "vue-json-pretty": "2.4.0", "vue-router": "4.4.5", "vue-types": "5.1.3", "vuedraggable": "^4.1.0", "vxe-table": "4.5.22"}, "devDependencies": {"@eslint/js": "9.15.0", "@iconify/json": "2.2.276", "@types/blueimp-md5": "^2.18.2", "@types/crypto-js": "4.2.2", "@types/file-saver": "2.0.7", "@types/js-cookie": "3.0.6", "@types/node": "18.18.2", "@types/nprogress": "0.2.3", "@unocss/preset-attributify": "0.64.1", "@unocss/preset-icons": "0.64.1", "@unocss/preset-uno": "0.64.1", "@vitejs/plugin-vue": "5.0.4", "@vue/compiler-sfc": "3.4.23", "autoprefixer": "10.4.18", "eslint": "9.15.0", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-vue": "9.31.0", "fast-glob": "3.3.2", "globals": "15.12.0", "postcss": "8.4.36", "prettier": "3.2.5", "sass": "1.72.0", "typescript": "5.7.2", "typescript-eslint": "8.16.0", "unocss": "0.64.1", "unplugin-auto-import": "0.17.5", "unplugin-icons": "0.18.5", "unplugin-vue-components": "0.26.0", "unplugin-vue-setup-extend-plus": "1.0.1", "vite": "5.4.11", "vite-plugin-compression": "0.5.1", "vite-plugin-svg-icons": "2.0.1", "vitest": "1.5.0", "vue-tsc": "2.0.13"}}