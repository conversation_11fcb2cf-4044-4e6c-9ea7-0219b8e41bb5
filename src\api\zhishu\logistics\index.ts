import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { LogisticsVO, LogisticsForm, LogisticsQuery } from '@/api/zhishu/logistics/types';
import { templateData } from '@/api/zhishu/district/types';

/**
 * 查询物流管理列表
 * @param query
 * @returns {*}
 */

export const listLogistics = (query?: LogisticsQuery): AxiosPromise<LogisticsVO[]> => {
  return request({
    url: '/zhishu/logistics/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询物流管理详细
 * @param id
 */
export const getLogistics = (id: string | number): AxiosPromise<LogisticsVO> => {
  return request({
    url: '/zhishu/logistics/' + id,
    method: 'get'
  });
};

/**
 * 新增物流管理
 * @param data
 */
export const addLogistics = (data: LogisticsForm) => {
  return request({
    url: '/zhishu/logistics',
    method: 'post',
    data: data
  });
};

/**
 * 修改物流管理
 * @param data
 */
export const updateLogistics = (data: LogisticsForm) => {
  return request({
    url: '/zhishu/logistics',
    method: 'put',
    data: data
  });
};

/**
 * 删除物流管理
 * @param id
 */
export const delLogistics = (id: string | number | Array<string | number>) => {
  return request({
    url: '/zhishu/logistics/' + id,
    method: 'delete'
  });
};

/**
 * 获取运费模版名称和id
 */
export const templateNameList = (): AxiosPromise<templateData[]> => {
  return request({
    url: '/zhishu/logistics/namelist',
    method: 'get'
  });
};
