<template>
  <div class="register">
    <el-form ref="registerRef" :model="registerForm" :rules="registerRules" class="register-form">
      <div class="title-box">
        <h3 class="title">与书同行</h3>
        <lang-select />
      </div>
      <!--      <el-form-item v-if="tenantEnabled" prop="tenantId">-->
      <!--        <el-select v-model="registerForm.tenantId" filterable :placeholder="proxy.$t('register.selectPlaceholder')" style="width: 100%">-->
      <!--          <el-option v-for="item in tenantList" :key="item.tenantId" :label="item.companyName" :value="item.tenantId"> </el-option>-->
      <!--          <template #prefix><svg-icon icon-class="company" class="el-input__icon input-icon" /></template>-->
      <!--        </el-select>-->
      <!--      </el-form-item>-->
      <el-form-item prop="username">
        <el-input v-model="registerForm.username" type="text" size="large" auto-complete="off" :placeholder="proxy.$t('register.username')" :disabled="registerForm.pddMallName.length > 0">
          <template #prefix>
            <svg-icon icon-class="user" class="el-input__icon input-icon" />
          </template>
        </el-input>
      </el-form-item>

      <el-form-item prop="phoneNumber">
        <el-input v-model="registerForm.phoneNumber" type="text" size="large" auto-complete="off" placeholder="手机号">
          <template #prefix>
            <el-icon>
              <Iphone />
            </el-icon>
          </template>
        </el-input>
      </el-form-item>

      <el-form-item prop="password">
        <el-input
          v-model="registerForm.password"
          type="password"
          size="large"
          auto-complete="off"
          :placeholder="proxy.$t('register.password')"
          @keyup.enter="handleRegister"
        >
          <template #prefix>
            <svg-icon icon-class="password" class="el-input__icon input-icon" />
          </template>
        </el-input>
      </el-form-item>
      <el-form-item prop="confirmPassword">
        <el-input
          v-model="registerForm.confirmPassword"
          type="password"
          size="large"
          auto-complete="off"
          :placeholder="proxy.$t('register.confirmPassword')"
          @keyup.enter="handleRegister"
        >
          <template #prefix>
            <svg-icon icon-class="password" class="el-input__icon input-icon" />
          </template>
        </el-input>
      </el-form-item>

      <el-form-item prop="inviteCode">
        <el-input v-model="registerForm.inviteCode" type="text" size="large" auto-complete="off" placeholder="邀请码">
          <template #prefix>
            <svg-icon icon-class="guide" class="el-input__icon input-icon" />
          </template>
        </el-input>
      </el-form-item>
      <el-form-item v-if="captchaEnabled" prop="code">
        <el-input
          v-model="registerForm.code"
          size="large"
          auto-complete="off"
          :placeholder="proxy.$t('register.code')"
          style="width: 63%"
          @keyup.enter="handleRegister"
        >
          <template #prefix>
            <svg-icon icon-class="validCode" class="el-input__icon input-icon" />
          </template>
        </el-input>
        <div class="register-code">
          <img :src="codeUrl" class="register-code-img" @click="getCode" />
        </div>
      </el-form-item>
      <el-form-item style="width: 100%">
        <el-button :loading="loading" size="large" type="primary" style="width: 100%" @click.prevent="handleRegister">
          <span v-if="!loading">{{ proxy.$t('register.register') }}</span>
          <span v-else>{{ proxy.$t('register.registering') }}</span>
        </el-button>
        <div style="float: right">
          <router-link class="link-type" :to="'/login'">{{ proxy.$t('register.switchLoginPage') }}</router-link>
        </div>
      </el-form-item>
    </el-form>
    <!--  底部  -->
    <div class="el-register-footer">
      <span>Copyright © 2018-2024 疯狂的狮子Li All Rights Reserved.</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { getCodeImg, register, getTenantList, login } from '@/api/login';
import { RegisterForm, TenantVO } from '@/api/types';
import { to } from 'await-to-js';
import { useI18n } from 'vue-i18n';
import { Iphone } from '@element-plus/icons-vue';
import { useRouter, useRoute } from 'vue-router';
import { getCurrentInstance, onMounted } from 'vue';

// 从localStorage获取邀请码和拼多多相关参数
const inviteCode = localStorage.getItem('inviteCode') || '';
const pddMallId = localStorage.getItem('pddMallId') || '';
const pddMallName = localStorage.getItem('pddMallName') || '';
const pddType = localStorage.getItem('pddType') || '';
// 优先使用accessToken，兼容pddToken
const accessToken = localStorage.getItem('accessToken') || localStorage.getItem('pddToken') || '';
const skuSpec = localStorage.getItem("skuSpec") || '';

// 确保cookie中也有这些值
if (accessToken) {
  document.cookie = `accessToken=${accessToken}; path=/; max-age=86400; secure`;
  document.cookie = `pddToken=${accessToken}; path=/; max-age=86400; secure`;
}

console.log('注册页获取到的参数:', {
  inviteCode,
  pddMallId,
  pddMallName,
  pddType,
  accessToken,
  skuSpec,
  urlParams: Object.fromEntries(new URLSearchParams(window.location.search).entries()),
  cookies: document.cookie
});

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const router = useRouter();

const { t } = useI18n();

const registerForm = ref<RegisterForm>({
  tenantId: '',
  username: '',
  phoneNumber: '',
  password: '',
  confirmPassword: '',
  code: '',
  uuid: '',
  userType: 'sys_user',
  inviteCode: inviteCode,
  // 添加拼多多相关参数
  pddMallId: pddMallId,
  pddMallName: pddMallName,
  pddType: pddType,
  accessToken: accessToken,
  skuSpec: skuSpec
});

// 租户开关
const tenantEnabled = ref(true);

const equalToPassword = (rule: any, value: string, callback: any) => {
  if (registerForm.value.password !== value) {
    callback(new Error(t('register.rule.confirmPassword.equalToPassword')));
  } else {
    callback();
  }
};

const registerRules: ElFormRules = {
  tenantId: [{ required: true, trigger: 'blur', message: t('register.rule.tenantId.required') }],
  username: [
    { required: true, trigger: 'blur', message: t('register.rule.username.required') },
    { min: 2, max: 20, message: t('register.rule.username.length', { min: 2, max: 20 }), trigger: 'blur' },
    {
      pattern: /^[A-Za-z0-9]+$/, // 正则表达式：仅允许字母和数字
      trigger: ['blur', 'input'], // 输入时和失焦时均验证
      message: '用户名只能包含字母和数字' // 提示语示例："用户名只能包含字母和数字"
    }
  ],
  phoneNumber: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    {
      pattern: /^(?:(?:\+|00)86)?1[3-9]\d{9}$/,
      message: '手机号格式错误',
      trigger: ['blur', 'input']
    }
  ],
  inviteCode: [{ pattern: /^[A-Za-z0-9]{6,12}$/, message: '邀请码格式不正确', trigger: 'blur' }],
  password: [
    { required: true, trigger: 'blur', message: t('register.rule.password.required') },
    { min: 5, max: 20, message: t('register.rule.password.length', { min: 5, max: 20 }), trigger: 'blur' },
    {
      pattern: /^[^<>"'|\\]+$/,
      message: t('register.rule.password.pattern', { strings: '< > " \' \\ |' }),
      trigger: 'blur'
    }
  ],
  confirmPassword: [
    { required: true, trigger: 'blur', message: t('register.rule.confirmPassword.required') },
    { required: true, validator: equalToPassword, trigger: 'blur' }
  ],
  code: [{ required: true, trigger: 'change', message: t('register.rule.code.required') }]
};
const codeUrl = ref('');
const loading = ref(false);
const captchaEnabled = ref(true);
const registerRef = ref<ElFormInstance>();
// 租户列表
const tenantList = ref<TenantVO[]>([]);

// 页面加载时获取验证码和租户信息
onMounted(() => {
  // console.log('=============== 注册页面加载 ===============');
  // console.log('当前URL:', window.location.href);
  // console.log('URL参数:', Object.fromEntries(new URLSearchParams(window.location.search).entries()));

  // 从URL获取参数
  const urlParams = new URLSearchParams(window.location.search);
  const pddMallId = urlParams.get('pddMallId');
  const pddMallName = urlParams.get('pddMallName');
  const skuSpec = urlParams.get('skuSpec');
  const type = urlParams.get('type');
  const token = urlParams.get('accessToken'); // 修改为accessToken
  const code = urlParams.get('code');
  // if (pddMallId) localStorage.setItem('pddMallId', pddMallId);
  // if (pddMallName) localStorage.setItem('pddMallName', pddMallName);
  // if (type) localStorage.setItem('pddType', type);
  // if (token) localStorage.setItem('pddToken', token);

  // // 更新registerForm中的值，确保表单能够使用URL中的参数
  // if (pddMallId) {
  //   registerForm.value.pddMallId = pddMallId;
  //   // 设置用户名为"ppd"+pddMallId的拼接
  //   registerForm.value.username = 'ppd' + pddMallId;
  // }
  // if (pddMallName) {
  //   registerForm.value.pddMallName = pddMallName;

  // 检查URL中是否有拼多多参数
  const hasPddParams = !!(pddMallId || pddMallName || type || token || skuSpec);

  // 如果URL中有拼多多参数，则保存到localStorage
  if (hasPddParams) {
    if (pddMallId) localStorage.setItem('pddMallId', pddMallId);
    if (pddMallName) localStorage.setItem('pddMallName', pddMallName);
    if (type) localStorage.setItem('pddType', type);
    if (token) localStorage.setItem('pddToken', token);
    if (skuSpec) localStorage.setItem('skuSpec', skuSpec);
    // 更新registerForm中的值
    if (pddMallId) {
      registerForm.value.pddMallId = pddMallId;
      // 设置用户名为"pdd"+pddMallId的拼接
      registerForm.value.username = 'pdd' + pddMallId;
    }
    if (pddMallName) registerForm.value.pddMallName = pddMallName;
    if (type) registerForm.value.pddType = type;
    if (token) registerForm.value.accessToken = token;
    if (skuSpec) registerForm.value.skuSpec = skuSpec;
  } else {
    // 如果URL中没有拼多多参数，则清除localStorage中的相关数据
    localStorage.removeItem('pddMallId');
    localStorage.removeItem('pddMallName');
    localStorage.removeItem('pddType');
    localStorage.removeItem('pddToken');
    localStorage.removeItem('skuSpec');


    // 清除表单中的相关数据
    registerForm.value.pddMallId = '';
    registerForm.value.pddMallName = '';
    registerForm.value.pddType = '';
    registerForm.value.accessToken = '';
    registerForm.value.skuSpec = '';
  }

  // 处理邀请码
  if (code) {
    localStorage.setItem('inviteCode', code);
    registerForm.value.inviteCode = code;
  }
  // if (type) registerForm.value.pddType = type;
  // if (token) registerForm.value.accessToken = token; // 修正为accessToken
  // console.log('localStorage中的参数:');
  // console.log('- pddMallId:', localStorage.getItem('pddMallId'));
  // console.log('- pddMallName:', localStorage.getItem('pddMallName'));
  // console.log('- pddType:', localStorage.getItem('pddType'));
  // console.log('- token/pddToken:', localStorage.getItem('pddToken'));
  // console.log('- inviteCode:', localStorage.getItem('inviteCode'));
  // console.log('注册表单初始值:', registerForm.value);
  // console.log('=========================================');

  getCode();
  initTenantList();
// 在onMounted函数中添加
// 检查是否有必要的cookie/session
const checkAndSetCookies = () => {
  // 检查是否有pddToken
  const pddToken = localStorage.getItem('pddToken');
  
  if (!pddToken) {
    console.log('register页面未检测到pddToken，正在创建...');
    
    // 从URL中获取accessToken
    const accessToken = urlParams.get('accessToken');
    
    if (accessToken) {
      // 设置localStorage和表单值
      localStorage.setItem('pddToken', accessToken);
      registerForm.value.accessToken = accessToken;
      
      console.log('register页面已创建pddToken:', accessToken);
    }
  } else {
    // 确保表单中也有这个值
    registerForm.value.accessToken = pddToken;
  }
};

  
  // 执行检查
  checkAndSetCookies();
});

const handleRegister = () => {
  registerRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      loading.value = true;

      console.log('=============== 提交注册表单 ===============');
      console.log('提交注册表单数据:', JSON.stringify(registerForm.value, null, 2));
      console.log('==========================================');

      const [err] = await to(register(registerForm.value));
      if (!err) {
        const username = registerForm.value.username;
        const phoneNumber = registerForm.value.phoneNumber;
        console.log(phoneNumber);
        await ElMessageBox.alert(
          '恭喜你，您的账号' + username + '注册成功！' + '手机号是唯一标识，接下来请使用手机号:' + phoneNumber + '  进行登录',
          '系统提示',
          {
            type: 'success',
            dangerouslyUseHTMLString: true
          }
        );
        // 注册成功后清除本地存储的临时参数
        localStorage.removeItem('pddMallId');
        localStorage.removeItem('pddMallName');
        localStorage.removeItem('pddType');
        localStorage.removeItem('inviteCode');
        localStorage.removeItem('skuSpec');
        // 检查是否有token，如果有，尝试自动登录
        const token = localStorage.getItem('pddToken');
        if (token && registerForm.value.pddType === '2') {
          // 使用注册成功的手机号和密码自动登录
          try {
            // 调用登录接口
            const loginData = {
              phoneNumber: registerForm.value.phoneNumber,
              password: registerForm.value.password,
              clientId: 'erp',
              grantType: 'password',
              rememberMe: true
            };

            console.log('自动登录尝试:', loginData);

            const res = await login(loginData);
            if (res.data && res.data.access_token) {
              // 登录成功，保存token
              localStorage.setItem('Access-Token', res.data.access_token);
              localStorage.removeItem('pddToken'); // 清除临时token

              // 跳转到首页
              router.push('/');
              return;
            }
          } catch (error) {
            console.error('自动登录失败:', error);
          }
        }

        // 如果没有token或自动登录失败，跳转到登录页
        await router.push('/login');
      } else {
        loading.value = false;
        if (captchaEnabled.value) {
          getCode();
        }
      }
    }
  });
};

const getCode = async () => {
  const res = await getCodeImg();
  const { data } = res;
  captchaEnabled.value = data.captchaEnabled === undefined ? true : data.captchaEnabled;
  if (captchaEnabled.value) {
    codeUrl.value = 'data:image/gif;base64,' + data.img;
    registerForm.value.uuid = data.uuid;
  }
};

const initTenantList = async () => {
  const { data } = await getTenantList(false);
  tenantEnabled.value = data.tenantEnabled === undefined ? true : data.tenantEnabled;
  if (tenantEnabled.value) {
    tenantList.value = data.voList;
    if (tenantList.value != null && tenantList.value.length !== 0) {
      registerForm.value.tenantId = tenantList.value[0].tenantId;
    }
  }
};
</script>

<style lang="scss" scoped>
.register {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  background-image: url('../assets/images/login-background.jpg');
  background-size: cover;
}

.title-box {
  display: flex;

  .title {
    margin: 0px auto 30px auto;
    text-align: center;
    color: #707070;
  }

  :deep(.lang-select--style) {
    line-height: 0;
    color: #7483a3;
  }
}

.register-form {
  border-radius: 6px;
  background: #ffffff;
  width: 400px;
  padding: 25px 25px 5px 25px;

  .el-input {
    height: 40px;

    input {
      height: 40px;
    }
  }

  .input-icon {
    height: 39px;
    width: 14px;
    margin-left: 0;
  }
}

.register-tip {
  font-size: 13px;
  text-align: center;
  color: #bfbfbf;
}

.register-code {
  width: 33%;
  height: 40px;
  float: right;

  img {
    cursor: pointer;
    vertical-align: middle;
  }
}

.el-register-footer {
  height: 40px;
  line-height: 40px;
  position: fixed;
  bottom: 0;
  width: 100%;
  text-align: center;
  color: #fff;
  font-family: Arial, serif;
  font-size: 12px;
  letter-spacing: 1px;
}

.register-code-img {
  height: 40px;
  padding-left: 12px;
}
</style>
