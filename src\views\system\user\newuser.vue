<template>
  <div class="settle-in-container">
    <!-- 主页面内容 -->
    <div class="main-content">
      <h1 class="welcome-title">升级完整版</h1>
      <p class="welcome-desc">成为我们的合作伙伴，共创美好未来</p>
      <el-button type="primary" size="large" @click="dialogVisible = true" class="apply-btn">点击升级</el-button>
    </div>


    <!-- 申请入驻对话框 -->
    <el-dialog title="完善信息" v-model="dialogVisible" width="50%" :before-close="handleClose">
      <el-form ref="settleForm" :model="form" :rules="rules" label-width="120px">
        <el-form-item label="身份证正反面" prop="cardIdentity" :error="cartInfo">
          <el-upload
            style="margin-right: 40px"
            class="avatar-uploader"
            :action="uploadCardImage"
            :show-file-list="false"
            :headers="headers"
            :on-success="handleCartFrontSuccess"
            :before-upload="beforeAvatarUpload"
          >
            <img v-if="cardFronUrl" :src="cardFronUrl" class="avatar" />
            <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
            <!-- 新增提示文字 -->
            <template #tip>
              <div class="upload-tip">请上传身份证正面</div>
            </template>
          </el-upload>

          <el-upload
            class="avatar-uploader"
            :action="uploadCardImage"
            :show-file-list="false"
            :headers="headers"
            :on-success="handleCartSideSuccess"
            :before-upload="beforeAvatarUpload"
          >
            <img v-if="cartSideUrl" :src="cartSideUrl" class="avatar" />
            <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
            <template #tip>
              <div class="upload-tip">请上传身份证反面</div>
            </template>
          </el-upload>
        </el-form-item>

        <el-form-item label="联系人" prop="contactPerson">
          <el-input v-model="form.contactPerson" disabled="true" placeholder="联系人姓名"></el-input>
        </el-form-item>
        <el-form-item label="联系电话" prop="contactPhone">
          <el-input v-model="form.contactPhone" placeholder="请输入联系电话"></el-input>
        </el-form-item>

        <el-form-item label="电子邮箱" prop="email">
          <el-input v-model="form.email" placeholder="请输入电子邮箱"></el-input>
        </el-form-item>

        <el-form-item label="营业执照" prop="license" :error="licenseInfo">
          <!--          <template>-->
          <el-upload
            class="avatar-uploader"
            :action="uploadiMage"
            :headers="headers"
            :show-file-list="false"
            :on-success="handleAvatarSuccess"
            :before-upload="beforeAvatarUpload"
          >
            <img v-if="imageUrl" :src="imageUrl" class="avatar" />
            <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
            <template #tip>
              <div class="upload-tip">请上传营业执照</div>
            </template>
          </el-upload>
          <!--          </template>-->
        </el-form-item>

        <el-form-item style="display: none" label="营业执照名称:" prop="licenseName">
          <el-input v-model="form.licenseName"  placeholder="请输入营业执照名称"></el-input>
        </el-form-item>
        <el-form-item label="注册号:" prop="licenseNumber">
          <el-input v-model="form.licenseNumber" disabled="true" placeholder="营业执照注册号"></el-input>
        </el-form-item>
        <el-form-item label="企业名称" prop="companyName">
          <el-input v-model="form.companyName" disabled="true" placeholder="企业全称"></el-input>
        </el-form-item>
        <el-form-item label="店铺地址" prop="adress">
          <el-input v-model="form.adress" disabled="true" placeholder="店铺地址"></el-input>
        </el-form-item>
        <el-form-item label="企业类型" prop="companyType">
          <el-select v-model="form.companyType" placeholder="请选择企业类型">
            <el-option label="有限责任公司" value="1"></el-option>
            <el-option label="股份有限公司" value="2"></el-option>
            <el-option label="个人独资企业" value="3"></el-option>
            <el-option label="合伙企业" value="4"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="营业执照过期时间" prop="licenseTime" label-width="150">
          <el-date-picker
            v-model="form.licenseTime"
            date-format="YYYY/MM/DD"
            value-format="YYYY/MM/DD"
            type="date"
            placeholder="date"

          />
        </el-form-item>

        <el-form-item label="经营许可证" prop="businessLicense">
          <el-upload
            class="avatar-uploader"
            :action="imagelicenseUpload"
            :show-file-list="false"
            :headers="headers"
            :on-success="handleLicenseSuccess"
            :before-upload="beforeAvatarUpload"
          >
            <img v-if="licenseImage" :src="licenseImage" class="avatar" />
            <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
            <template #tip>
              <div class="upload-tip">请上传经营许可证</div>
            </template>
          </el-upload>
        </el-form-item>

        <el-form-item label="经营许可证过期时间" prop="businessLicenseTime" label-width="150">
          <el-date-picker
            v-model="form.businessLicenseTime"
            date-format="YYYY/MM/DD"
            value-format="YYYY/MM/DD"
            type="date"
            placeholder="date"
          />
        </el-form-item>
        <el-form-item label="出版物许可证证照核实方式" prop="remark">
          <el-input
            type="textarea"
            :rows="3"
            v-model="form.remark"
            placeholder="请输入其他需要说明的信息"
          ></el-input>
        </el-form-item>
      </el-form>

      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitForm">提 交</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { ElMessage, ElMessageBox, type FormInstance, type UploadProps } from 'element-plus';
import { addDept, updateDept } from '@/api/system/dept';
import { addNewUser } from '@/api/system/user/newuser';
import { getImage } from '@/api/zhishu/image';
import { globalHeaders } from '@/utils/request';

// 表单引用
const settleForm = ref<FormInstance>();

const baseUrl = import.meta.env.VITE_APP_BASE_API;
const imagelicenseUpload=ref(baseUrl+'/zhishu/image/userUpload')
const uploadiMage=ref(baseUrl+'/system/newUser/licenseRedirect')
const uploadCardImage=ref(baseUrl+'/system/newUser/cardRedirect')

const headers = ref(globalHeaders());
// 身份证正面
const cardFronUrl=ref('')
// 身份证反面
const cartSideUrl=ref('')
// 营业执照图片
const imageUrlOld = ref('')
const imageUrl = ref('')

const licenseImageOld = ref('')
const licenseImage=ref('')

const cartUrl=ref([])

const cartInfo=ref('')

const licenseInfo=ref('')
// 身份证正面
const handleCartFrontSuccess = async (res, file) => {
  cartInfo.value=res.data.message;
  form.value.contactPerson=res.data.name;
  if(res.data.url!=null){
    const oldUrl = res.data.url;
    const url = await getImage(oldUrl);
    cardFronUrl.value = url;
    // cartUrl.value = [cardFronUrl.value, ...cartUrl.value.slice(1)];
    cartUrl.value[0] = oldUrl;
  }
  console.log(cartUrl.value,"----------------");
}

//身份证反面

const handleCartSideSuccess = async (res, file) => {
  cartInfo.value=res.data.message;
  if(res.data.url!=null){
    const oldUrl = res.data.url;
    const url = await getImage(oldUrl);
    cartSideUrl.value=url;
    cartUrl.value = [cartUrl.value[0] || '', oldUrl];
  }
}
// 展示营业执照

const handleAvatarSuccess = async (res, file) => {

  imageUrlOld.value = res.data.url;
  imageUrl.value = await getImage(imageUrlOld.value);
  form.value.companyName=res.data.name;
  form.value.licenseNumber=res.data.regNum;
  form.value.adress=res.data.address;
  if(res.data.period!=null){
    form.value.licenseTime=res.data.period;
  }
  licenseInfo.value=res.data.message;
}
// 展示经营许可证
const handleLicenseSuccess= async (res, file) => {
  licenseImageOld.value = res.data.url
  licenseImage.value=res.data.url
}

// 图片上传之前操作
const beforeAvatarUpload: UploadProps['beforeUpload'] = (rawFile) => {
  if (rawFile.type !== 'image/jpeg') {
    ElMessage.error('Avatar picture must be JPG format!')
    return false
  } else if (rawFile.size / 1024 / 1024 > 2) {
    ElMessage.error('Avatar picture size can not exceed 2MB!')
    return false
  }
  return true
}




// 响应式数据
const dialogVisible = ref(false);
const form = ref({
  companyName: '',
  contactPerson: '',
  contactPhone: '',
  email: '',
  companyType: '',
  cardIdentity:'',
  licenseName:'',
  licenseNumber:'',
  license: '',
  remark: '',
  adress:'',
  businessLicense:'',
  licenseTime:'',
  businessLicenseTime:'',
});

// 表单验证规则
const rules = {
  companyName: [
    { required: true, message: '请输入企业名称', trigger: 'blur' }
  ],
  adress:[
    { required: true, message: '请输入店铺地址', trigger: 'blur' }
  ],
  contactPerson: [
    { required: true, message: '请输入联系人姓名' },
    { max: 8, message: '最长8位字符', trigger: 'blur' }
  ],
  contactPhone: [
    { required: true, message: '请输入联系电话', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱地址', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱地址', trigger: ['blur', 'change'] }
  ],
  companyType: [
    { required: true, message: '请选择企业类型', trigger: 'change' }
  ],
  // licenseName:[
  //   { required: true, message: '请输入营业执照名称', trigger: 'change' }
  // ],
  licenseNumber:[
    { required: true, message: '请输入营业执照注册号', trigger: 'change' }
  ],
  license:[
    { required: true, message: '请上传营业执照', trigger: 'change' }
  ],
  businessLicense:[
    { required: true, message: '请上传经营许可证', trigger: 'change' }
  ],
  cardIdentity: [
    { required: true},
    {
      validator: (rule, value, callback) => {
        if(cartUrl.value[0]==null&&cartUrl.value[1]==null){
          callback(new Error('请上传身份证正反面照片'));
        }else if(cartUrl.value[0]==null){
          callback(new Error('请上传身份证正面照片'));
        }else if(cartUrl.value[1]==null){
          callback(new Error('请上传身份证反面照片'));
        } else {
            callback();
          }
      },
      trigger: 'change'
    }
  ]
};

// 关闭对话框前的确认
const handleClose = (done: () => void) => {
  ElMessageBox.confirm('确认关闭？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    resetForm();
    done();
  }).catch(() => {});
};

// 提交表单
const submitForm =  () => {
  if (!settleForm.value) return;
  console.log(form.value)
  form.value.license=imageUrlOld.value;
  form.value.cardIdentity=JSON.stringify(cartUrl.value);

  form.value.businessLicense=licenseImageOld.value;
  settleForm.value.validate(async (valid) => {
    if (valid) {
      alert('提交成功')
      // console.log(form.value)
      await addNewUser(form.value);
      ElMessage.success('提交成功！');
      dialogVisible.value = false;
      resetForm();
    } else {
      ElMessage.warning('请填写完整信息');
      return false;
    }
  });
};

// 重置表单
const resetForm = () => {
  if (settleForm.value) {
    settleForm.value.resetFields();
  }
};

// 文件上传成功处理
const handleUploadSuccess: UploadProps['onSuccess'] = (response, file) => {
  form.value.license = response.data.url; // 根据实际API返回调整
  console.log(form.value.license)
  ElMessage.success('上传成功');
};

// 文件上传前验证
const beforeUpload: UploadProps['beforeUpload'] = (file) => {
  const isJPG = file.type === 'image/jpeg' || file.type === 'image/png';
  const isLt2M = file.size / 1024 / 1024 < 2;

  if (!isJPG) {
    ElMessage.error('上传图片只能是 JPG/PNG 格式!');
  }
  if (!isLt2M) {
    ElMessage.error('上传图片大小不能超过 2MB!');
  }
  return isJPG && isLt2M;
};
</script>


<style scoped>
.settle-in-container {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f5f7fa;
  min-height: 100vh; /* 确保至少填充整个视口高度 */
}

.main-content {
  text-align: center;
  padding: 40px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  width: 80%;
  max-width: 600px;
  margin: 20px;
}

.welcome-title {
  font-size: 28px;
  color: #303133;
  margin-bottom: 16px;
}

.welcome-desc {
  font-size: 16px;
  color: #909399;
  margin-bottom: 40px;
}

.apply-btn {
  padding: 12px 36px;
  font-size: 16px;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .el-dialog {
    width: 90% !important;
  }

  .main-content {
    width: 90%;
    padding: 20px;
  }

  .welcome-title {
    font-size: 24px;
  }

  .welcome-desc {
    font-size: 14px;
  }
}
.avatar-uploader{
  border: 1px dashed var(--el-border-color);
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: var(--el-transition-duration-fast);
}

.avatar-uploader:hover {
  border-color: var(--el-color-primary);
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 140px;
  height: 120px;
  text-align: center;
}
.upload-tip {
  margin-top: 8px;
  width: 100%; /* 与上传区域同宽 */
  text-align: center; /* 文字居中 */
}
</style>
<style scoped>
.avatar-uploader .avatar {
  width: 178px;
  height: 178px;
  display: block;
}
</style>
