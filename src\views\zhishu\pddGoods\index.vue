<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="店铺名称" prop="shopId" >
              <el-select v-model="shopId" placeholder="请选择店铺" clearable @change="handleQuery">
                <el-option v-for="dict in shopList" :key="dict.value" :label="dict.label" :value="dict.value"/>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="tongBu">同步</el-button>
              <!-- <el-button type="danger" icon="Search" @click="tongBuDetail">详细同步</el-button> -->
              <el-button type="primary" @click="createVerifyPriceUrl">生成核价链接</el-button>
              <!-- <el-button type="primary" icon="Search" @click="handleQuery">拉取</el-button> -->
            </el-form-item>
          </el-form>
        </el-card>
        <el-dialog v-model="verifyPriceDialog" title="核价链接" width="700px" @close="closeData">
          <div class="verify-price-dialog">
            <el-form :model="encrypForm" :rules="encrypRules" ref="encrypFormRef">
            <el-form-item label="店铺优惠" prop="shopPrice" v-if="!pricingLink">
              <el-input v-model="encrypForm.shopPrice" placeholder="请输入店铺优惠、在3-20之间整数"/>
            </el-form-item>
            <el-form-item label="利润区间" prop="profitRange"  v-if="!pricingLink">
              <el-row :gutter="10">
                <el-col :span="10">
                  <el-form-item prop="minPrice">
                  <el-input v-model="encrypForm.minPrice" type="number" placeholder="利润最小值(整数)">
                    <template #append>元</template>
                  </el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="4" style="text-align: center; line-height: 32px;">
                  至
                </el-col>
                <el-col :span="10">
                  <el-form-item prop="maxPrice">
                  <el-input v-model="encrypForm.maxPrice" type="number" placeholder="利润最大值(整数)">
                    <template #append>元</template>
                  </el-input>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form-item>
              <el-form-item label="价格模版名称" prop="shopPrice" v-if="pricingLink">
                <el-input v-model="priceName" disabled/>
              </el-form-item>
            </el-form>
            <div style="height: 110px; padding: 10px;">
              <span>{{ pricingLink }}</span>
            </div>
          </div>
          <template #footer>
            <el-button
              v-if="pricingLink"
              @click="copyLink"
              type="primary"
              style="margin-left: 10px;"
            >
              一键复制
            </el-button>
            <el-button type="primary" @click="encrypMethod" v-if="!pricingLink">确定</el-button>
          </template>
        </el-dialog>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading"
      :data="pddGoodsList"
      :element-loading-text="loadingText"
      element-loading-svg-view-box="-10, -10, 50, 50"
      element-loading-background="rgba(122, 122, 122, 0.8)"
      @selection-change="handleSelectionChange">

        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="缩略图" align="left" prop="thumb_url" width="70" :show-overflow-tooltip="true"  >
          <template #default="{ row }">
            <el-image
              style="width: 30px;height: 30px"
              :src="row.Img"
              fit="scale-down"
              :preview-src-list="[currentPreviewImg]"
              preview-teleported
              @click="handlePreview(row.Img)"
            >
              <template #error>
                <div class="image-slot">
                  暂无
                </div>
              </template>
            </el-image>
          </template>
        </el-table-column>
        <el-table-column label="商品id" align="center" prop="TrilateralId" width="150"/>
        <el-table-column label="商品编码" align="center" prop="ISBN" width="150"/>
        <el-table-column label="商品名称" align="center" prop="Title"/>
        <el-table-column label="库存数量" align="center" prop="Stock" width="100"/>
        <el-table-column label="是否在架上" align="center" prop="IsOnSale" width="100">
           <template #default="{row}">
            {{row.IsOnSale == '1' ? '是' : '否'}}
           </template>
        </el-table-column>
        <el-table-column label="价格" align="center" prop="price" width="100">
           <template #default="{row}">
            {{row.TotalPrice ? row.TotalPrice / 100 : ''}}
           </template>
        </el-table-column>
        <el-table-column label="规格编码" align="center" prop="SkuCode" />
        <el-table-column label="创建时间" align="center" prop="FinishTime" width="200"/>
      </el-table>


      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
  </div>
</template>

<script setup name="Notice" lang="ts">
import { getShopGoodsList,createShopGoodsList,createShopGoodsDetailList,checkFile } from '@/api/zhishu/pdd';
import { NoticeVO, NoticeQuery, NoticeForm } from '@/api/zhishu/notice/types';
import { encrypUrlMenthod, getListShop, getVerifyPriceUrl } from '@/api/zhishu/shop';
import { ElMessage } from 'element-plus';
import { BaseInfoForm } from '@/api/zhishu/baseInfo/types';
import { EncrypData } from '@/api/zhishu/shop/types';


// 添加响应式数据
const verifyPriceDialog = ref(false);
const verifyPriceUrl = ref('');
const loadingVerifyUrl = ref(false);

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { t_notice_status } = toRefs<any>(proxy?.useDict('t_notice_status'));

const pddGoodsList = ref<NoticeVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(false);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const shopId =ref('')
const loadingText = ref('正在加载数据');

const queryFormRef = ref<ElFormInstance>();
const noticeFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData = {
  created_at: undefined,
  goods_id: undefined,
  goods_name: undefined,
  goods_quantity: undefined,
  goods_reserve_quantity: undefined,
  image_url: undefined,
  is_more_sku: undefined,
  is_onsale: undefined,
  sku_list: undefined,
  thumb_url: undefined,
  price:undefined,
  skuCode:undefined,
}
const data = reactive<PageData<NoticeForm, NoticeQuery>>({
  form: {...initFormData},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    created_at: undefined,
    goods_id: undefined,
    goods_name: undefined,
    goods_quantity: undefined,
    goods_reserve_quantity: undefined,
    image_url: undefined,
    is_more_sku: undefined,
    is_onsale: undefined,
    sku_list: undefined,
    thumb_url: undefined,
    price:undefined,
    skuCode:undefined,
    params: {
    }
  }
});

//加密需要的数据
const encrypForm = reactive<EncrypData>({
  dataUrl: undefined,
  shopPrice: undefined,
  maxPrice: undefined,
  minPrice: undefined,
  shopId: undefined,
  shopType: undefined
});

const pricingLink = ref('');

const priceName = ref('');

const { queryParams, form } = toRefs(data);

//表单校验
const encrypFormRef = ref<ElFormInstance>(); // 表单引用
const encrypRules={
  shopPrice: [
    { required: true, message: '请输入店铺优惠', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        const num = Number(value);
        if (!Number.isInteger(num)){
          callback(new Error('必须输入整数'));
        } else if (num < 3 || num > 20) {
          callback(new Error('请输入3-20之间的整数'));
        } else {
          callback();
        }
      },
      trigger: 'blur'
    }
  ],
    minPrice: [
    { required: true, message: '请输入利润最小值', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (!Number.isInteger(Number(value))) {
          callback(new Error('必须输入整数'));
        } else {
          callback();
        }
      },
      trigger: 'blur'
    }
  ],
  maxPrice: [
    { required: true, message: '请输入利润最大值', trigger: 'blur' },
    {
      validator: function(rule, value, callback) {
        const min = Number(encrypForm.minPrice);
        const max = Number(value);
        if (!Number.isInteger(max)) {
          callback(new Error('必须输入整数'));
        } else if (max <= min) {
          callback(new Error('最大值必须大于最小值'));
        } else {
          callback();
        }
      }.bind(this),
      trigger: 'blur'
    }
  ]
};



/** 查询消息通知列表 */
const getList = async () => {
  loading.value = true;
  const res = await getShopGoodsList(shopId.value,queryParams.value.pageNum,queryParams.value.pageSize);
  pddGoodsList.value = res.rows;
  total.value = res.total;
  loading.value = false;
}

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
}

/** 表单重置 */
const reset = () => {
  form.value = {...initFormData};
  noticeFormRef.value?.resetFields();
}

/** 搜索按钮操作 */
const handleQuery = async () => {

  if(shopId.value == null || shopId.value == undefined || shopId.value == ''){
    proxy?.$modal.msgError('请选择店铺');
    return
  }

  queryParams.value.pageNum = 1;
  getList();

}

const tongBu = async () => {
  if(shopId.value == null || shopId.value == undefined || shopId.value == ''){
    proxy?.$modal.msgError('请选择店铺');
    return
  }
  const res = await createShopGoodsList(shopId.value);

  ElMessage.success(res.msg);
}

const createVerifyPriceUrl = async () => {
  if (!shopId.value) {
    proxy?.$modal.msgError('请先选择店铺')
    return
  }

  if (pddGoodsList.value.length === 0) {
    proxy?.$modal.msgError('当前店铺没有商品数据')
    return
  }

  try {
    loadingVerifyUrl.value = true
    // 这里调用你的后端API，替换成实际的API调用
    const res = await createVerifyPriceUrlApi(shopId.value)

    if (res.code === 200) {
      verifyPriceUrl.value = res.data
      verifyPriceDialog.value = true
    } else {
      ElMessage.error(res.msg || '生成核价链接失败')
    }
  } catch (error) {
    console.error('生成核价链接出错:', error)
    ElMessage.error('生成核价链接出错')
  } finally {
    loadingVerifyUrl.value = false
  }
}

// 模拟API调用，替换成你实际的API
const createVerifyPriceUrlApi = async (shopId: string) => {
  // 这里应该是你的实际API调用，例如：
  return await getVerifyPriceUrl(shopId);
}

const copyVerifyUrl = () => {
  if (!verifyPriceUrl.value) return

  navigator.clipboard.writeText(verifyPriceUrl.value)
    .then(() => {
      ElMessage.success('链接已复制到剪贴板')
    })
    .catch(err => {
      console.error('复制失败:', err)
      ElMessage.error('复制链接失败')
    })
}

const tongBuDetail = async () => {
  if(shopId.value == null || shopId.value == undefined || shopId.value == ''){
    proxy?.$modal.msgError('请选择店铺');
    return
  }

  const intervalId = setInterval( async () => {
    const res = await checkFile(shopId.value);
    if(res){

      clearInterval(intervalId);

      queryParams.value.pageNum = 1;
      loading.value = false;
      loadingText.value = "正在加载数据";
      getList();
    }
  }, 30000);

  loadingText.value = "详细数据正在同步中，耗时较久";
  loading.value = true;
  const res = await createShopGoodsDetailList(shopId.value);
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
}

const currentPreviewImg = ref('') // 仅存储当前点击的图片
// 点击事件处理
const handlePreview = (imgUrl) => {
  currentPreviewImg.value  = imgUrl
}


/** 多选框选中数据 */
const handleSelectionChange = (selection: NoticeVO[]) => {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

const shopList=[];
const getShopList = async () => {
  const res = await getListShop();

  for (var i = 0; i < res.length; i++) {
    if(res[i].shopType==="1"){
      shopList.push(
        {
          value: res[i].id,
          label: res[i].shopName,
        }
      )
    }
  }
}

const formatTimestamp = (row, column, timestamp) => {
    if (!timestamp) return '';

    // 将10位时间戳转为毫秒（JS的Date需要毫秒）
    const date = new Date(timestamp * 1000);

    // 格式化为 YYYY-MM-DD hh:mm:ss
    const padZero = num => num.toString().padStart(2, '0');

    const year = date.getFullYear();
    const month = padZero(date.getMonth() + 1);
    const day = padZero(date.getDate());
    const hours = padZero(date.getHours());
    const minutes = padZero(date.getMinutes());
    const seconds = padZero(date.getSeconds());

    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
  }
  //加密链接生成
const encrypMethod = async () => {
  try {
    // 1. 执行表单验证
    await encrypFormRef.value?.validate();

    // 2. 验证通过后，设置加载状态
    loadingVerifyUrl.value = true;

    // 3. 准备请求数据
    encrypForm.dataUrl = verifyPriceUrl.value;
    encrypForm.shopId = shopId.value;
    encrypForm.shopType = "1"; // 假设这是拼多多店铺类型

    // 4. 调用加密接口
    const res = await encrypUrlMenthod(encrypForm);

    // 5. 处理结果
    if (res) {
      pricingLink.value = res.url;
      if(res.url!=null){
        priceName.value=res.name;
      }
      ElMessage.success('核价链接生成成功');
    } else {
      ElMessage.warning("拼多多以外店铺正在开发中，请重新选择");
    }
  } catch (error) {
    // 验证失败会直接进入catch，不需要额外处理
    console.error('表单验证失败:', error);
  } finally {
    loadingVerifyUrl.value = false;
  }
};




//一键复制核价链接
const copyLink = async () => {
  try {
    await navigator.clipboard.writeText(pricingLink.value);
    ElMessage.success('复制成功');
  } catch (err) {
    ElMessage.error('复制失败，请手动复制');
  }
};
const closeData=async ()=>{
  pricingLink.value=null;
  encrypForm.dataUrl=null;
  encrypForm.shopId=null;
  encrypForm.shopPrice=null;
  encrypForm.maxPrice=null;
  encrypForm.minPrice=null;
}
onMounted(() => {
  getShopList();
});
</script>


<style>
/* 文本溢出处理 */
.truncate-cell {
  width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.verify-price-dialog {
  padding: 0 20px;
}
.url-display {
  margin-bottom: 20px;
}
.tips p {
  margin: 5px 0;
  line-height: 1.5;
}
</style>
