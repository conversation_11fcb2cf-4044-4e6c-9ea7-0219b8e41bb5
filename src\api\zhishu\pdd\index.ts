import request from '@/utils/request';
import { AxiosPromise } from 'axios';

var pageCode = '';
var pati = '';

async function getPageCodeAndPati(mallId) {
  try {
    // 注意：这里必须 await request
    pageCode = await request({
      url: '/huidiao/pdd/getPageCode/'+mallId,
      method: 'get'
    });

    await PDD_OPEN_init({
      code: pageCode
    });

    pati = await window.PDD_OPEN_getPati();
  } catch (e) {
    console.log(e);
  }
}

async function  getPati(){
  await PDD_OPEN_init({
    code: ''
  });
  pageCode = '';
  pati = await window.PDD_OPEN_getPati();
  return pati;
}

/** 获取getPati */
export function getPatiAndPageCode(){
  getPati();
  return {
    "pageCode":"",
    "pati":pati
  }
}

// 前往拼多多授权页面
export function toPddGetCode(id){
  return request({
    url:'/huidiao/pdd/toPddGetCode?id='+id,
    method:'get'
  })
}


// 获取商品运费模板接口
export async function getLogisticsTemplate(shopId,mallId){
  await getPageCodeAndPati(mallId);
  return request({
    url:'/huidiao/pdd/getLogisticsTemplate/'+shopId,
    method:'get',
    headers:{
      'X-PDD-Pati':pati,
      'X-PDD-PageCode':pageCode
    }
  })
}

export async function getCats(){
  await getPati()
  return request({
    url:'/huidiao/pdd/getCats',
    method:'get',
    headers:{
      'X-PDD-Pati':pati,
      'X-PDD-PageCode':pageCode
    }
  })
}

export async function getSpecs(shopId,mallId){
  await getPageCodeAndPati(mallId);
  return request({
    url:'/huidiao/pdd/getSpec/'+shopId,
    method:'get',
    headers:{
      'X-PDD-Pati':pati,
      'X-PDD-PageCode':pageCode
    }
  })
}

export async function getShopGoodsList(shopId,page,pageSize){
  return request({
    url:'/huidiao/pdd/getShopGoodsList?shopId='+shopId+"&page="+page+"&pageSize="+pageSize,
    method:'get'
  })
}


export async function createShopGoodsList(shopId){
  return request({
    url:'/huidiao/pdd/createShopGoodsList?shopId='+shopId,
    method:'get'
  })
}

export async function createShopGoodsDetailList(shopId){
  return request({
    url:'/huidiao/pdd/createShopGoodsDetailList?shopId='+shopId,
    method:'get'
  })
}


export async function checkFile(shopId){
  return request({
    url:'/huidiao/pdd/checkFile?shopId='+shopId,
    method:'get'
  })
}
