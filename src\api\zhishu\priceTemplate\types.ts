export interface PriceTemplateVO {
  /**
   * 主键
   */
  id: string | number;

  /**
   * 价格模板名称
   */
  templateName: string;

  /**
     * 价格类型 0 书价  1 总价（书价+运费）
     */
  priceType: string;

  /**
   * 增加比例
   */
  proportion: number;

  /**
   * 增加金额
   */
  addAmount: number;

  /**
   * 价格模板状态（0正常 1停用）
   */
  status: string;

  /**
   * 价格参数
   */
  priceAdjustments?: {
    minPrice?: number;
    maxPrice?: number;
    adjustPercent?: number;
    adjustAmount?: number;
  }[];

}

export interface PriceTemplateForm extends BaseEntity {
  /**
   * 主键
   */
  id?: string | number;

  /**
   * 价格模板名称
   */
  templateName?: string;

  /**
     * 价格类型 0 书价  1 总价（书价+运费）
     */
  priceType?: string;

  /**
   * 增加比例
   */
  proportion?: number;

  /**
   * 增加金额
   */
  addAmount?: number;

  /**
   * 价格模板状态（0正常 1停用）
   */
  status?: string;

  /**
   * 高价
   */
  highPrice?:number;
  /**
   * 低价
   */
  lowPrice?:number;

  /**
   * 价格参数
   */
  priceAdjustments?: {
    minPrice?: number;
    maxPrice?: number;
    adjustPercent?: number;
    adjustAmount?: number;
  }[];

  /**
   * 传递的价格参数
   */
  rangePrice?:string;
}

export interface PriceTemplateQuery extends PageQuery {

  /**
   * 价格模板名称
   */
  templateName?: string;

  /**
     * 价格类型 0 书价  1 总价（书价+运费）
     */
  priceType?: string;

  /**
   * 增加比例
   */
  proportion?: number;

  /**
   * 增加金额
   */
  addAmount?: number;

  /**
   * 价格模板状态（0正常 1停用）
   */
  status?: string;

    /**
     * 日期范围参数
     */
    params?: any;
}



