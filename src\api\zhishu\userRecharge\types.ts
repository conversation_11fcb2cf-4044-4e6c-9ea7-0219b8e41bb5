export interface UserRechargeVO {
  /**
   * 主键
   */
  id: string | number;

  /**
   * 用户id
   */
  userId: string | number;

  /**
   * 充值类型 1 微信支付 2 支付宝支付 ...
   */
  rechargType: string;

  /**
   * 充值金额 单位分
   */
  rechargPrice: number;

  /**
   * 支付成功时间
   */
  successTime: string;

  createTime: string;

  /**
   * 支付成功后回调方法中数据字符串
   */
  allDataStr: string;

  /**
   * 状态（0正常 1停用）
   */
  status: string;

  /**
   * 手续费
   */
  commission: number;

}

export interface UserRechargeForm extends BaseEntity {
  /**
   * 主键
   */
  id?: string | number;

  wxId?: string;

  /**
   * 用户id
   */
  userId?: string | number;

  /**
   * 充值类型 1 微信支付 2 支付宝支付 ...
   */
  rechargType?: string;

  /**
   * 充值金额 单位分
   */
  rechargPrice?: number;

  /**
   * 支付成功时间
   */
  successTime?: string;

  createTime?: string;

  /**
   * 支付成功后回调方法中数据字符串
   */
  allDataStr?: string;

  /**
   * 状态（0正常 1停用）
   */
  status?: string;

   /**
   * 手续费
   */
   commission?: number;
}

export interface UserRechargeQuery extends PageQuery {

  /**
   * 用户id
   */
  userId?: string | number;

  /**
   * 充值类型 1 微信支付 2 支付宝支付 ...
   */
  rechargType?: string;

  /**
   * 充值金额 单位分
   */
  rechargPrice?: number;

  /**
   * 支付成功时间
   */
  successTime?: string;

  createTime?: string;

  /**
   * 支付成功后回调方法中数据字符串
   */
  allDataStr?: string;

  /**
   * 状态（0正常 1停用）
   */
  status?: string;

    /**
     * 日期范围参数
     */
    params?: any;

    /**
   * 手续费
   */
   commission?: number;
}



