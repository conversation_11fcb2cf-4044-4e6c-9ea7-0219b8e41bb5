<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="任务类型" prop="taskType">
              <el-select v-model="queryParams.taskType" placeholder="请选择任务类型" clearable>
                <el-option v-for="dict in t_task_type" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="文件名称" prop="fileName">
              <el-input v-model="queryParams.fileName" placeholder="请输入文件名称" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="任务状态" prop="taskStatus">
              <el-select v-model="queryParams.taskStatus" placeholder="请选择任务状态" clearable>
                <el-option v-for="dict in t_task_status" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <el-table v-loading="loading" :data="taskList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="任务编码" align="center" prop="id" v-if="true" :show-overflow-tooltip="true">
          <template #default="{ row }">
            <div class="truncate-cell"> <!-- 文本截断容器 -->
              {{ row.id }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="任务类型" align="center" prop="taskType">
          <template #default="scope">
            <dict-tag :options="t_task_type" :value="scope.row.taskType" />
          </template>
        </el-table-column>
        <el-table-column label="货区名称" align="center" prop="depotIds" />
        <el-table-column label="文件名称" align="center" prop="fileName" width="250"/>
        <el-table-column label="执行数据条数" align="center" prop="dataNum" />
        <el-table-column label="任务状态" align="center" prop="taskStatus">
          <template #default="scope">
            <dict-tag :options="t_task_status" :value="scope.row.taskStatus" />
          </template>
        </el-table-column>
         <el-table-column label="创建时间" align="center" prop="createTime" width="200">
          <template #default="scope">
            <span>{{ proxy.parseTime(scope.row.createTime) }}</span>
          </template>
        </el-table-column>

        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="日志" placement="top">
              <el-button link type="primary" icon="Memo" @click="shopSetUp(scope.row)"></el-button>
            </el-tooltip>
            <el-tooltip content="暂停" placement="top">
              <el-button :class="{isHide : scope.row.taskStatus != 1}" link type="primary" icon="VideoPause" @click="editThread(scope.row,0)"></el-button>
            </el-tooltip>
            <el-tooltip content="恢复" placement="top">
              <el-button :class="{isHide : scope.row.taskStatus != 2}" link type="primary" icon="VideoPlay" @click="editThread(scope.row,1)"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['zhishu:task:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改任务列表对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="900px" append-to-body>
      <el-form ref="taskFormRef" :model="formRelease" :rules="rulesRelease" label-width="80px" style="height:600px">
        <el-form-item label="任务类型" prop="taskType">
          <el-select v-model="formRelease.taskType" placeholder="请选择任务类型">
            <el-option v-for="dict in t_task_type" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="仓库选择" prop="depotIds" label-width="100px" style="width: 100%">
          <el-checkbox-group v-model="formRelease.depotIds">
            <el-checkbox v-for="item in depotList" :key="item.value" :label="item.value">
              {{ item.label }}
            </el-checkbox>
          </el-checkbox-group>
        </el-form-item>

        <el-form-item label="文件选择" prop="fileUrl" label-width="100px" style="width: 100%">
          <el-row>
            <el-col :span="14">
              <el-upload
                ref="upload"
                class="upload-demo"
                :action="uploadImgUrl"
                :limit="1"
                :headers="headers"
                :on-success="handleAvatarSuccess"
                :on-exceed="handleExceed"
                :list-type="picture"
              >
                <template #trigger>
                  <el-button type="primary">上传文件</el-button>
                </template>
              </el-upload>
            </el-col>
            <el-col :span="6">
              <el-button @click="importTemplate">下载模板</el-button>
            </el-col>
          </el-row>

        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 店铺日志总信息 -->
    <el-dialog :title="dialog.title" v-model="dialog.shopSetup" width="800px" append-to-body>
      <div style="width:732px;height:162px;background:#f2f2f2;white-space: pre-wrap;padding:13px;margin-bottom:15px"  >
        {{logsMessage}}
      </div>
      <el-table v-loading="loading" :data="taskLogsList" height="400px">
        <el-table-column label="仓库名称" align="center" prop="depotName" />
        <el-table-column label="进度" align="center" >
          <template #default="scope">
            {{scope.row.progress }}%
          </template>
        </el-table-column>
        <el-table-column label="创建时间/修改时间" align="center" prop="createTime" width="300">
          <template #default="scope">
            <span>{{ scope.row.createTime }} / {{scope.row.updateTime}}</span>
          </template>
        </el-table-column>

        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="查看" placement="top">
              <el-button link type="primary" icon="Memo" @click="getLogsDetailList(scope.row)">查看</el-button>
            </el-tooltip>
            <el-tooltip content="下载" placement="top">
              <el-button link type="primary" icon="Download" @click="downloadLogs(scope.row.taskId + scope.row.shopId)">下载</el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancel">关 闭</el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 店铺任务详细信息 -->
    <el-dialog :title="dialog.logDetailTitle" v-model="dialog.taskLogsDetail" width="700px" append-to-body>

      <el-table v-loading="loading" :data="taskLogsDetailList" height="700px">
        <el-table-column label="日志名称" align="center" prop="logName"/>
        <el-table-column label="数量" align="center" prop="num" width="100px"/>
        <el-table-column label="占比" align="center" width="100px">
          <template #default="scope">
            {{ scope.row.progress }}%
          </template>
        </el-table-column>

        <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="100px">
          <template #default="scope">
            <el-tooltip content="下载日志" placement="top">
              <el-button link type="primary" icon="Download" @click="downloadLogs(scope.row.mark)">下载日志</el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="detailCancel">关 闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Task" lang="ts">
import { getListShop } from '@/api/zhishu/shop';
import { genFileId } from 'element-plus'
import type { UploadInstance, UploadProps, UploadRawFile } from 'element-plus'
import { globalHeaders } from '@/utils/request';
import { ExcelTaskForm, ExcelTaskQuery, ExcelTaskVO } from '@/api/zhishu/excelTask/types';
import {
  excelAddTask,
  excelContinueThread, excelDelTask, excelGetTask,
  excelListTask,
  excelLogsDetailTask,
  excelLogsMsg,
  excelPauseThread
} from '@/api/zhishu/excelTask';
import { depotNameList } from '@/api/zhishu/shelves';
import { delTask } from '@/api/zhishu/task';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { t_task_type, t_task_status,t_update_method,t_list_status,t_book_category }
= toRefs<any>(proxy?.useDict('t_task_type', 't_task_status','t_update_method','t_list_status','t_book_category'));


const taskList = ref<ExcelTaskVO[]>([]);
const taskLogsList = ref([]);
const taskLogsDetailList = ref([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const taskFormRef = ref<ElFormInstance>();

const baseUrl = import.meta.env.VITE_APP_BASE_API;
const uploadImgUrl = ref(baseUrl + '/zhishu/excelTask/upload'); // 上传的图片服务器地址
const headers = ref(globalHeaders());
const logsMessage = ref('');

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});


const depotList = [];

const initFormData: ExcelTaskForm = {
  id: undefined,
  taskType: undefined,
  depotIds: undefined,
  fileName: undefined,
  dataNum: undefined,
  taskStatus: undefined,
  status: undefined,
  threadId :undefined
};



const data = reactive<PageData<ExcelTaskForm, ExcelTaskQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    taskType: undefined,
    depotIds: undefined,
    fileName: undefined,
    dataNum: undefined,
    taskStatus: undefined,
    status: undefined,
    params: {}
  },
  rules: {
    id: [{ required: true, message: '不能为空', trigger: 'blur' }]
  }
});

const { queryParams, form, rules } = toRefs(data);

const formRelease = ref({
  taskType: undefined,
  way: '0',
  listStatus: '0',
  bookCategory: '1',
  bookCategoryAppoint: undefined,
  appointCategory: undefined,
  depotIds: undefined,
  fileUrl: undefined
});

const rulesRelease = {
  id: [{ required: true, message: '不能为空', trigger: 'blur' }],
  taskType: [{ required: true, message: '任务类型不能为空', trigger: 'blur' }],
  depotIds: [{ required: true, message: '仓库不能为空', trigger: 'blur' }],
  fileUrl: [{ required: true, message: '文件不能为空', trigger: 'blur' }]
}

/** 查询任务列表列表 */
const getList = async () => {
  loading.value = true;
  const res = await excelListTask(queryParams.value);
  taskList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

const getdepotList = async () => {
  const res = await depotNameList();
  const resList=res.rows;
    for (var i = 0; i < resList.length; i++) {
      depotList.push({
        value: resList[i].id,
        label: resList[i].name
      });
    }
}

const editThread = async (row,type) =>{
  if(type == 0){
    await excelPauseThread(row.threadId,row.id);
  }else if(type == 1){
    await excelContinueThread(row.threadId,row.id);
  }
  getList();
}

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
  dialog.shopSetup = false;
};

const detailCancel = () => {
  reset();
  dialog.taskLogsDetail = false;
}

const shopSetUp = async (row) => {
  const logsDepotList = await excelListTask(row.id);
  logsMessage.value = await excelLogsMsg(row.id);
  console.log(logsMessage,"-----------------");
  taskLogsList.value = logsDepotList;
  reset();
  dialog.shopSetup = true;
  dialog.title = '任务日志';
};

const getLogsDetailList = async (row) => {
  reset();
  const logsDetailList = await excelLogsDetailTask(row);
  taskLogsDetailList.value = logsDetailList;
  dialog.taskLogsDetail = true;
  dialog.logDetailTitle = '详细日志信息:'+taskLogsDetailList.value[0].depotName;
}

const downloadLogs = async (fileName) => {
    fileName = fileName + ".xlsx";
    const url = 'http://localhost:8080/zhishu/excelTask/downloadLogs/'+fileName;
    const link = document.createElement('a');
    link.href = url;
    link.download = fileName; // 设置下载的文件名
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

const returnNumber = (num,total) => {
  return (Number(num) / total * 100).toFixed(2);
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  taskFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: ExcelTaskVO[]) => {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = '添加任务列表';
};

/** 修改按钮操作 */
const handleUpdate = async (row?: ExcelTaskVO) => {
  reset();
  const _id = row?.id || ids.value[0];
  const res = await excelGetTask(_id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = '修改任务列表';
};

/** 提交按钮 */
const submitForm = async () => {


  buttonLoading.value = true;
  // formRelease.value.depotIds = formRelease.value.depotIds.join(',')
  // console.log("1233212")
  // formRelease.value.bookCategoryAppoint = formRelease.value.bookCategoryAppoint[2];
  await excelAddTask(formRelease.value).finally(() => (buttonLoading.value = false));
  proxy?.$modal.msgSuccess('操作成功');
  dialog.visible = false;
  await getList();

  // taskFormRef.value?.validate(async (valid: boolean) => {
  //   if (valid) {
  //     buttonLoading.value = true;
  //     if (form.value.id) {
  //       await updateTask(form.value).finally(() => (buttonLoading.value = false));
  //     } else {
  //       await addTask(form.value).finally(() => (buttonLoading.value = false));
  //     }
  //     proxy?.$modal.msgSuccess('操作成功');
  //     dialog.visible = false;
  //     await getList();
  //   }
  // });
};

/** 删除按钮操作 */
const handleDelete = async (row?: ExcelTaskVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除任务列表编号为"' + _ids + '"的数据项？').finally(() => (loading.value = false));
  await excelDelTask(_ids);
  proxy?.$modal.msgSuccess('删除成功');
  await getList();
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    'zhishu/excelTask/export',
    {
      ...queryParams.value
    },
    `task_${new Date().getTime()}.xlsx`
  );
};

/** 下载模板操作 */
const importTemplate = () => {
  proxy?.download('zhishu/exceltask/importTemplate', {}, `task_template_${new Date().getTime()}.xlsx`);
};

/**
 * 文件上传
 */
const upload = ref<UploadInstance>()

const handleAvatarSuccess: UploadProps['onSuccess'] = async (response, uploadFile) => {
  formRelease.value.fileUrl = response.data.url;
};
const handleExceed: UploadProps['onExceed'] = (files) => {
  upload.value!.clearFiles()
  const file = files[0] as UploadRawFile
  file.uid = genFileId()
  upload.value!.handleStart(file)
}

const submitUpload = () => {
  upload.value!.submit()
}

let catsList = [];

onMounted(() => {
  getList();
  getdepotList();
});

</script>

<style>
.isHide{
  display: none;
}
   /* 文本溢出处理 */
 .truncate-cell {
   width: 100%;
   white-space: nowrap;
   overflow: hidden;
   text-overflow: ellipsis;
 }

/* 增强悬浮提示样式 */
.el-tooltip__popper {
  max-width: 400px;
  word-break: break-all;
}

/* 运费模板样式 */
.freight-template-container {
  padding: 10px;
}

.overseas-section {
  margin-top: 20px;
  border-top: 1px solid #ebeef5;
  padding-top: 15px;
}

</style>
