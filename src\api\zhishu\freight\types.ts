  export interface FreightVO {
  /**
   * 货位id
   */
  id: string | number;

  /**
   * 货架id
   */
  shelvesId: string | number;

  /**
   * 货位编码
   */
  code: string;

  /**
   * 货位名称
   */
  name: string;

    /**
     * 单位
     */
    unit: string;

  /**
   * 货位容量
   */
  capMax: number;

  /**
   * 货号
   */
  artNo: string;

  /**
   * 仓库状态（0正常 1停用）
   */
  status: string;

    /**
     * 二级货区名称
     */
  shelvesName: string;

  /**
   * 二级货区code
   */
  shelvesCode:string;


}

export interface FreightForm extends BaseEntity {
  /**
   * 货位id
   */
  id?: string | number;

  /**
   * 货架id
   */
  shelvesId?: string | number;

  /**
   * 货位编码
   */
  code?: string;

  /**
   * 货位名称
   */
  name?: string;
  /**
   * 单位
   */
  unit?: string;

  /**
   * 货位容量
   */
  capMax?: number;

  /**
   * 货号
   */
  artNo?: string;

  /**
   * 仓库状态（0正常 1停用）
   */
  status?: string;

  /**
   * 二级货区名称
   */
  shelvesName?: string;

  /**
   * 完整code
   */
  comcode?:string;

  /**
   * 是否有下一级
   */
  hasChildren?:boolean;

  /**
   * 层级
   */
  level:number;


}

export interface FreightQuery extends PageQuery {

  /**
   * 货架id
   */
  shelvesId?: string | number;

  /**
   * 货位编码
   */
  code?: string;

  /**
   * 货位名称
   */
  name?: string;

  /**
   * 单位
   */
  unit?: string;

  /**
   * 货位容量
   */
  capMax?: number;

  /**
   * 货号
   */
  artNo?: string;

  /**
   * 仓库状态（0正常 1停用）
   */
  status?: string;

    /**
     * 日期范围参数
     */
    params?: any;

  /**
   * 二级货区名称
   */
  shelvesName?: string;

  /**
   * 完整code
   */
  comcode?:string;

}



