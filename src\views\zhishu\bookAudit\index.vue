<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="商品编号" prop="itemNumber">
              <el-input v-model="queryParams.itemNumber" placeholder="请输入商品编号" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="品相" prop="conditionCode">
              <el-input v-model="queryParams.conditionCode" placeholder="请输入品相" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="isbn" prop="isbn">
              <el-input v-model="queryParams.isbn" placeholder="请输入isbn" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['zhishu:bookAudit:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['zhishu:bookAudit:edit']">修改</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['zhishu:bookAudit:remove']">删除</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['zhishu:bookAudit:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="bookAuditList" @selection-change="handleSelectionChange">
        <el-table-column type="expand" >
          <template #default="{ row }">
            <el-form label-position="left" inline class="demo-table-expand">
              <el-form-item>
                <el-image
                  style="width: 100px; height: 120px"
                  :src="row.bookPic"
                  fit="cover"
                  :preview-src-list="[row.bookPic]"
                />
              </el-form-item>
            </el-form>
          </template>
        </el-table-column>

        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="审核图书id" align="center" prop="id" v-if="true" />
        <el-table-column label="产品编码" align="center" prop="productId" />
        <el-table-column label="商品名称" align="center" prop="goodsName" :show-overflow-tooltip="true" >
          <template #default="{ row }">
            <div class="truncate-cell"> <!-- 文本截断容器 -->
              {{ row.goodsName }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="isbn" align="center" prop="isbn" />
        <el-table-column label="标准售价" align="center" prop="price" >
          <template #default="{ row }">
            {{ (row.price/100).toFixed(2) }}
          </template>
        </el-table-column>
        <el-table-column label="审核状态" align="center" prop="status">
          <template #default="{ row }">
            <el-tag
              :type="row.status  === '0' ? 'success' : row.status === '1' ? 'danger' : 'info'"
              :effect="row.status  === '0' ? 'dark' : 'light'"
              size="large"
            >
              {{ row.status  === '0' ? '通过' : row.status  === '1' ? '未通过' : '待审核' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">

            <el-tooltip content="通过" placement="top">
              <el-button link type="success" icon="Check" @click="handleUpdateStatus(scope.row.id,'0')"></el-button>
            </el-tooltip>

            <el-tooltip content="未通过" placement="top" >
              <el-button link type="danger"  circle  @click="handleUpdateStatus(scope.row.id,'1')">X</el-button>
            </el-tooltip>

          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改图书审核管理对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body>
      <el-form ref="bookAuditFormRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="用户id" prop="userId">
          <el-input v-model="form.userId" placeholder="请输入用户id" />
        </el-form-item>
        <el-form-item label="产品编码" prop="productId">
          <el-input v-model="form.productId" placeholder="请输入产品编码" />
        </el-form-item>
        <el-form-item label="商品名称" prop="goodsName">
          <el-input v-model="form.goodsName" placeholder="请输入商品名称" />
        </el-form-item>
        <el-form-item label="isbn" prop="isbn">
          <el-input v-model="form.isbn" placeholder="请输入isbn" />
        </el-form-item>
        <el-form-item label="货号" prop="artNo">
          <el-input v-model="form.artNo" placeholder="请输入货号" />
        </el-form-item>
        <el-form-item label="品相" prop="conditionCode">
          <el-input v-model="form.conditionCode" placeholder="请输入品相" />
        </el-form-item>
        <el-form-item label="商品编号" prop="itemNumber">
          <el-input v-model="form.itemNumber" placeholder="请输入商品编号" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="BookAudit" lang="ts">
import {
  listBookAudit,
  getBookAudit,
  delBookAudit,
  addBookAudit,
  updateBookAudit,
  UpdateBookStatus
} from '@/api/zhishu/bookAudit';
import { BookAuditVO, BookAuditQuery, BookAuditForm, BookAudit } from '@/api/zhishu/bookAudit/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const bookAuditList = ref<BookAuditVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const bookAuditFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: BookAuditForm = {
  userId: undefined,
  productId: undefined,
  goodsName: undefined,
  isbn: undefined,
  artNo: undefined,
  conditionCode: undefined,
  itemNumber: undefined,
  status: undefined,
  inventory:undefined,
  bookPic:undefined
}
const data = reactive<PageData<BookAuditForm, BookAuditQuery>>({
  form: {...initFormData},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    userId: undefined,
    productId: undefined,
    goodsName: undefined,
    isbn: undefined,
    artNo: undefined,
    conditionCode: undefined,
    itemNumber: undefined,
    status: undefined,
    inventory:undefined,
    bookPic:undefined,
    params: {
    }
  },
  rules: {
    conditionCode: [
      { required: true, message: "品相不能为空", trigger: "blur" }
    ],
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 审核通过按钮操作*/
const handleUpdateStatus=async (id: number | string,status: '0' | '1')=> {
  await proxy?.$modal.confirm('是否审核编号为"' + id + '"的数据项？').finally(() => loading.value = false);
  // dialog1.visible = true;
  const params: BookAudit = {
    id: id,
    status: status
  }
  const response = UpdateBookStatus(params)
  proxy?.$modal.msgSuccess("审核通过");
  await getList();
}




/** 查询图书审核管理列表 */
const getList = async () => {
  loading.value = true;
  const res = await listBookAudit(queryParams.value);
  bookAuditList.value = res.rows;
  total.value = res.total;
  loading.value = false;
}

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
}

/** 表单重置 */
const reset = () => {
  form.value = {...initFormData};
  bookAuditFormRef.value?.resetFields();
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
}

/** 多选框选中数据 */
const handleSelectionChange = (selection: BookAuditVO[]) => {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = "添加图书审核管理";
}

/** 修改按钮操作 */
const handleUpdate = async (row?: BookAuditVO) => {
  reset();
  const _id = row?.id || ids.value[0]
  const res = await getBookAudit(_id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = "修改图书审核管理";
}

/** 提交按钮 */
const submitForm = () => {
  bookAuditFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateBookAudit(form.value).finally(() =>  buttonLoading.value = false);
      } else {
        await addBookAudit(form.value).finally(() =>  buttonLoading.value = false);
      }
      proxy?.$modal.msgSuccess("操作成功");
      dialog.visible = false;
      await getList();
    }
  });
}

/** 删除按钮操作 */
const handleDelete = async (row?: BookAuditVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除图书审核管理编号为"' + _ids + '"的数据项？').finally(() => loading.value = false);
  await delBookAudit(_ids);
  proxy?.$modal.msgSuccess("删除成功");
  await getList();
}

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download('zhishu/bookAudit/export', {
    ...queryParams.value
  }, `bookAudit_${new Date().getTime()}.xlsx`)
}

onMounted(() => {
  getList();
});
</script>
<style>
/* 文本溢出处理 */
.truncate-cell {
  width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 增强悬浮提示样式 */
.el-tooltip__popper {
  max-width: 400px;
  word-break: break-all;
}
.demo-table-expand {
  font-size: 0;
}
.demo-table-expand label {
  width: 90px;
  color: #99a9bf;
}
.demo-table-expand .el-form-item {
  margin-right: 0;
  margin-bottom: 0;
  width: 50%;
}
</style>
