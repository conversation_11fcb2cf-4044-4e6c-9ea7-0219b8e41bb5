<template>
  <el-config-provider :locale="appStore.locale" :size="appStore.size">
    <router-view />
  </el-config-provider>
</template>

<script setup lang="ts">
import useSettingsStore from '@/store/modules/settings';
import { handleThemeStyle } from '@/utils/theme';
import useAppStore from '@/store/modules/app';
import router from '@/router';

const appStore = useAppStore();

// 从URL中获取参数并存储到localStorage
const urlParams = new URLSearchParams(window.location.search);
const currentPath = window.location.pathname;

// console.log('当前路径:', currentPath);
// console.log('URL参数:', Object.fromEntries(urlParams.entries()));

// 处理token参数
const token = urlParams.get('token');
if (token) {
  localStorage.setItem('token', token);
  // 同时保存为pddToken供注册后自动登录使用
  localStorage.setItem('pddToken', token);
  // console.log('已保存token参数:', token);
}

// 处理邀请码参数
const code = urlParams.get('code');
if (code) {
  localStorage.setItem('inviteCode', code);
  // console.log('已保存邀请码参数:', code);
}

// 处理拼多多相关参数
const pddMallId = urlParams.get('pddMallId');
if (pddMallId) {
  localStorage.setItem('pddMallId', pddMallId);
  // console.log('已保存pddMallId参数:', pddMallId);
}

const pddMallName = urlParams.get('pddMallName');
if (pddMallName) {
  try {
    // 对URL编码的店铺名称进行解码
    const decodedPddMallName = decodeURIComponent(pddMallName);
    localStorage.setItem('pddMallName', decodedPddMallName);
    // console.log('已保存pddMallName参数:', decodedPddMallName);
  } catch (e) {
    console.error('解码店铺名称失败:', e);
    localStorage.setItem('pddMallName', pddMallName);
  }
}

const skuSpec = urlParams.get('skuSpec');
if (skuSpec) {
  try {
    // 对URL编码的店铺名称进行解码
    const decodedSkuSpec = decodeURIComponent(skuSpec);
    localStorage.setItem('skuSpec', decodedSkuSpec);
    // console.log('skuSpec:', decodedSkuSpec);
  } catch (e) {
    console.error('解码skuSpec名称失败:', e);
    localStorage.setItem('skuSpec', skuSpec);
  }
}

const type = urlParams.get('type');
if (type) {
  localStorage.setItem('pddType', type);
}

// 处理授权token参数
const pddToken = urlParams.get('accessToken');
if (pddToken) {
  // 统一使用accessToken作为键名
  localStorage.setItem('accessToken', pddToken);
  localStorage.setItem('pddToken', pddToken); // 保留兼容性
  
  // 设置cookie，不指定domain以确保在当前域下可用
  document.cookie = `accessToken=${pddToken}; path=/; max-age=86400; secure`;
  document.cookie = `pddToken=${pddToken}; path=/; max-age=86400; secure`;
  document.cookie = `pati=${pddToken}; path=/; max-age=86400; secure`;
  
  console.log('已设置accessToken cookie:', pddToken);
}

// 只有在非注册页面时，才清除URL参数
if ((token || code) && !currentPath.includes('/register')) {
  // console.log('清除URL参数，当前页面非注册页');
  window.history.replaceState(null, '', window.location.pathname);
} else {
  // console.log('保留URL参数');
}

// 检查是否有必要的cookie/session
const checkAndSetCookies = () => {
  // 检查是否有pddToken
  const hasPddToken = localStorage.getItem('pddToken') || document.cookie.includes('pddToken=');
  
  if (!hasPddToken && window.location.pathname.includes('/register')) {
    console.log('未检测到pddToken，正在创建...');
    
    // 从URL中获取accessToken
    const accessToken = urlParams.get('accessToken');
    
    if (accessToken) {
      // 设置localStorage
      localStorage.setItem('pddToken', accessToken);
      
      // 同时设置cookie，有效期7天
      const expiryDate = new Date();
      expiryDate.setDate(expiryDate.getDate() + 7);
      document.cookie = `pddToken=${accessToken}; expires=${expiryDate.toUTCString()}; path=/`;
      
      console.log('已创建pddToken:', accessToken);
    } else {
      // 如果URL中没有accessToken，创建一个临时token
      const tempToken = 'temp_' + Math.random().toString(36).substring(2, 15);
      localStorage.setItem('pddToken', tempToken);
      document.cookie = `pddToken=${tempToken}; path=/`;
      
      console.log('已创建临时token:', tempToken);
    }
  }
};

// 执行检查
checkAndSetCookies();

// 打印最终存储的所有参数
// console.log('localStorage中的所有参数:');
// console.log('- token:', localStorage.getItem('token'));
// console.log('- pddToken:', localStorage.getItem('pddToken'));
// console.log('- inviteCode:', localStorage.getItem('inviteCode'));
// console.log('- pddMallId:', localStorage.getItem('pddMallId'));
// console.log('- pddMallName:', localStorage.getItem('pddMallName'));
// console.log('- pddType:', localStorage.getItem('pddType'));
// console.log('- skuSpec:', localStorage.getItem('skuSpec'));

onMounted(() => {
  const isWeChat=isWeChatBrowser();
  console.log(isWeChat)
  if(isWeChat){
    //跳转到404
    router.push({ name: 'NotFound' }) // 确保路由中有 name 为 NotFound 的路由
    return // 阻止后续逻辑执行（可选）
  }
  nextTick(() => {
      // 初始化主题样式
      handleThemeStyle(useSettingsStore().theme);
    });
});

// 检测是否微信浏览器
function isWeChatBrowser() {
  const ua = navigator.userAgent.toLowerCase();
  // 检测微信浏览器且排除企业微信
  return /micromessenger/i.test(ua) && !/wxwork/i.test(ua);
}

</script>
