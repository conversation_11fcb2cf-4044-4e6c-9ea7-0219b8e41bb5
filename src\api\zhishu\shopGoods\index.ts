import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import {
  ShopGoodsVO,
  ShopGoodsForm,
  ShopGoodsQuery,
  StockChangeLogVo, BatchUpdateCargoVo
} from '@/api/zhishu/shopGoods/types';

var pageCode = '';
var pati = '';
async function  getPati(){
  await PDD_OPEN_init({
    code: ''
  });
  pageCode = '';
  pati = await window.PDD_OPEN_getPati();
  return pati;
}

/**
 * 查询商品信息列表
 * @param query
 * @returns {*}
 */

export const listShopGoods = (query?: ShopGoodsQuery): AxiosPromise<ShopGoodsVO[]> => {
  return request({
    url: '/zhishu/shopGoods/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询商品信息详细
 * @param id
 */
export const getShopGoods = (id: string | number): AxiosPromise<ShopGoodsVO> => {
  return request({
    url: '/zhishu/shopGoods/' + id,
    method: 'get'
  });
};

/**
 * 新增商品信息
 * @param data
 */
export const addShopGoods = (data: ShopGoodsForm) => {
  return request({
    url: '/zhishu/shopGoods',
    method: 'post',
    data: data
  });
};

/**
 * 修改商品信息
 * @param data
 */
export const updateShopGoods = (data: ShopGoodsForm) => {
  return request({
    url: '/zhishu/shopGoods',
    method: 'put',
    data: data
  });
};

/**
 * 删除商品信息
 * @param id
 * @param isProSynch
 */
export const delShopGoods = (id: string | number | Array<string | number>,isProSynch : boolean) => {
  return request({
    url: '/zhishu/shopGoods/' + id,
    method: 'delete',
    params:{ isProSynch }
  });
};

/**
 * 发布商品
 */
export const addGoods = async (data) => {
  debugger;
  // await getPati();
  return request({
    url: '/zhishu/shopGoods/goodsAdd',
    method: 'post',
    data: data,
    headers:{
      'X-PDD-Pati':pati,
      'X-PDD-PageCode':pageCode
    }
  });
};

/**
 * 查询商品信息详细
 * @param id
 */
export const getStockChangeLog = (id: string | number): AxiosPromise<StockChangeLogVo[]> => {
  return request({
    url: '/zhishu/shopGoods/stockLog/' + id,
    method: 'get'
  });
};


/**
 * 批量修改货区
 * @param data
 */
export const updateBatchModCarAre = (data: BatchUpdateCargoVo) => {
  return request({
    url: '/zhishu/shopGoods/batchUpdateAre',
    method: 'put',
    data: data
  });
};
