export interface BookAuditVO {
  /**
   * 审核图书id
   */
  id: string | number;

  /**
   * 用户id
   */
  userId: string | number;

  /**
   * 产品编码
   */
  productId: string | number;

  /**
   * 商品名称
   */
  goodsName: string;

  /**
   * isbn
   */
  isbn: string;

  /**
   * 货号
   */
  artNo: string;

  /**
   * 标准售价
   */
  price: number;

  /**
   * 品相
   */
  conditionCode: string;

  /**
   * 商品编号
   */
  itemNumber: string;

  /**
   * 审核状态(0 通过 1 未通过 2 待审核)
   */
  status: string;
  /**
   * 库存
   */
  inventory: number;

  /**
   * 书图片
   */
  bookPic:string;

}

export interface BookAuditForm extends BaseEntity {
  /**
   * 用户id
   */
  userId?: string | number;

  /**
   * 产品编码
   */
  productId?: string | number;

  /**
   * 商品名称
   */
  goodsName?: string;

  /**
   * isbn
   */
  isbn?: string;

  /**
   * 货号
   */
  artNo?: string;

  /**
   * 品相
   */
  conditionCode?: string;

  /**
   * 商品编号
   */
  itemNumber?: string;

  /**
   * 审核状态(0 通过 1 未通过 2 待审核)
   */
  status?: string;

  /**
   * 库存
   */
  inventory?: number;


  /**
   * 书图片
   */
  bookPic?:string;

}

export interface BookAuditQuery extends PageQuery {

  /**
   * 用户id
   */
  userId?: string | number;

  /**
   * 产品编码
   */
  productId?: string | number;

  /**
   * 商品名称
   */
  goodsName?: string;

  /**
   * isbn
   */
  isbn?: string;

  /**
   * 货号
   */
  artNo?: string;

  /**
   * 品相
   */
  conditionCode?: string;

  /**
   * 商品编号
   */
  itemNumber?: string;

  /**
   * 审核状态(0 通过 1 未通过 2 待审核)
   */
  status?: string;
  /**
   * 库存
   */
  inventory?: number;

    /**
     * 日期范围参数
     */
    params?: any;

  /**
   * 书图片
   */
  bookPic?:string;
}
// 定义接口规范请求参数
export interface BookAudit {
  id: number | string
  status: '0' | '1'
}


