import { templateData } from '@/api/zhishu/district/types';
import { ShelvesVO } from '@/api/zhishu/shelves/types';

export interface DepotVO {
  /**
   * 仓库id
   */
  id: string | number;

  /**
   * 仓库编号
   */
  code: string;

  /**
   * 仓库名称
   */
  name: string;

  /**
   * 单位
   */
  unit: string;

  /**
   * 仓库地址
   */
  address: string;

  /**
   * 仓库管理员
   */
  manager: string;


  /**
   * 仓库状态（0正常 1停用）
   */
  status: string;

  /**
   * 备注
   */
  remark: string;

  /**
   * 货架数量
   */
  sheQuantityMax: number;


  /**
   * 已用数量
   */
  sheNumber?: number;


  /**
   * 用户姓名
   */
  userName: string;

  /**
   * 用户id
   */
  userId: string | number;
  /**
   * 是否有下一级
   */
  hasChildren:boolean;

  /**
   * 层级
   */
  level:number;


  children: string;

  /**
   * 模版名称
   */
  templateName:number;

  /**
   * 书品库存总数
   */
  inventory:number;

  /**
   * 书品类型数量
   */
  categoryNumber:number;
}

export interface DepotForm extends BaseEntity {
  /**
   * 仓库id
   */
  id?: string | number;

  /**
   * 仓库编号
   */
  code?: string;

  /**
   * 仓库名称
   */
  name?: string;

  /**
   * 单位
   */
  unit?: string;

  /**
   * 仓库地址
   */
  address?: string;

  /**
   * 仓库管理员
   */
  manager?: string;


  /**
   * 仓库状态（0正常 1停用）
   */
  status?: string;

  /**
   * 备注
   */
  remark?: string;

  /**
   * 货架数量
   */
  sheQuantityMax?: number;

  /**
   * 已用数量
   */
  sheNumber?: number;


  /**
   * 用户姓名
   */
  userName?: string;
  /**
   * 用户id
   */
  userId?: string | number;

  templateId?: string |number;
  /**
   * 是否有下一级
   */
  hasChildren?:boolean;

  /**
   * 层级
   */
  level:number;

}

export interface DepotQuery extends PageQuery {

  /**
   * 仓库编号
   */
  code?: string;

  /**
   * 仓库名称
   */
  name?: string;

  /**
   * 单位
   */
  unit?: string;

  /**
   * 仓库地址
   */
  address?: string;

  /**
   * 仓库管理员
   */
  manager?: string;


  /**
   * 仓库状态（0正常 1停用）
   */
  status?: string;

    /**
     * 日期范围参数
     */
    params?: any;

  /**
   * 用户姓名
   */
  userName?: string;
  /**
   * 用户id
   */
  userId?: string | number;
  /**
   * 是否有下一级
   */
  hasChildren?:boolean;

}



