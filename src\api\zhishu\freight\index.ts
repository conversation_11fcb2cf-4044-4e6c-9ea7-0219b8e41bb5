import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { FreightVO, FreightForm, FreightQuery } from '@/api/zhishu/freight/types';
import { ShelvesVO } from '@/api/zhishu/shelves/types';

/**
 * 查询三级货区管理列表
 * @param query
 * @returns {*}
 */

export const listFreight = (query?: FreightQuery): AxiosPromise<FreightVO[]> => {
  return request({
    url: '/zhishu/freight/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询三级货区管理详细
 * @param id
 */
export const getFreight = (id: string | number): AxiosPromise<FreightVO> => {
  return request({
    url: '/zhishu/freight/' + id,
    method: 'get'
  });
};

/**
 * 新增三级货区管理
 * @param data
 */
export const addFreight = (data: FreightForm) => {
  return request({
    url: '/zhishu/freight',
    method: 'post',
    data: data
  });
};

/**
 * 修改三级货区管理
 * @param data
 */
export const updateFreight = (data: FreightForm) => {
  return request({
    url: '/zhishu/freight',
    method: 'put',
    data: data
  });
};

/**
 * 删除三级货区管理
 * @param id
 */
export const delFreight = (id: string | number | Array<string | number>) => {
  return request({
    url: '/zhishu/freight/' + id,
    method: 'delete'
  });
};



export const shelveListData = (): AxiosPromise<ShelvesVO[]> => {
  return request({
    url: '/zhishu/freight/sheList',
    method: 'get'
  });
};
