import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { ShelvesVO, ShelvesForm, ShelvesQuery } from '@/api/zhishu/shelves/types';
import { DepotVO } from '@/api/zhishu/depot/types';

/**
 * 查询货架信息列表
 * @param query
 * @returns {*}
 */

export const listShelves = (query?: ShelvesQuery): AxiosPromise<ShelvesVO[]> => {
  return request({
    url: '/shelves/shelves/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询货架信息详细
 * @param id
 */
export const getShelves = (id: string | number): AxiosPromise<ShelvesVO> => {
  return request({
    url: '/shelves/shelves/' + id,
    method: 'get'
  });
};

/**
 * 新增货架信息
 * @param data
 */
export const addShelves = (data: ShelvesForm) => {
  return request({
    url: '/shelves/shelves',
    method: 'post',
    data: data
  });
};

/**
 * 修改货架信息
 * @param data
 */
export const updateShelves = (data: ShelvesForm) => {
  return request({
    url: '/shelves/shelves',
    method: 'put',
    data: data
  });
};

/**
 * 删除货架信息
 * @param id
 */
export const delShelves = (id: string | number | Array<string | number>) => {
  return request({
    url: '/shelves/shelves/' + id,
    method: 'delete'
  });
};


export const depotNameList = (): AxiosPromise<DepotVO[]> => {
  return request({
    url: '/shelves/shelves/namelist',
    method: 'get'
  });
};
