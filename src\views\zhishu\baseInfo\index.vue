<template>
  <div class="p-2">
    <!-- 支付验证弹窗 -->
    <el-dialog v-model="showPaymentDialog" title="开通选品中心功能请提前联系客服，协商后在开通" width="400px" @click="pageFees"
      class="custom-dialog">
      <!--      <div style="text-align: center; font-size: 20px; margin-bottom: 15px;">-->
      <!--        <span>请使用微信支付0.01元解锁选品中心内容</span>-->
      <!--      </div>-->
      <div style="text-align: center;">
        <img :src="qrCodeText" alt="支付二维码" style="width: 200px; height: 200px;" />
      </div>
      <div style="text-align: center; margin-top: 15px; color: #999;">
        支付成功后内容将自动显示
      </div>
    </el-dialog>

    <div v-if="paymentVerified">
      <transition :enter-active-class="proxy?.animate.searchAnimate.enter"
        :leave-active-class="proxy?.animate.searchAnimate.leave">
        <div v-show="showSearch" class="mb-[10px]">
          <el-card shadow="hover">
            <el-form ref="queryFormRef" :model="queryParams" :inline="true">
              <el-form-item label="书名" prop="bookName">
                <el-input v-model="queryParams.bookName" placeholder="请输入书名" clearable @keyup.enter="handleQuery" />
              </el-form-item>
              <el-form-item label="isbn" prop="isbn">
                <el-input v-model="queryParams.isbn" placeholder="请输入isbn" clearable @keyup.enter="handleQuery" />
              </el-form-item>
              <el-form-item label="作者" prop="author">
                <el-input v-model="queryParams.author" placeholder="请输入作者" clearable @keyup.enter="handleQuery" />
              </el-form-item>
              <el-form-item label="出版社" prop="publisher">
                <el-input v-model="queryParams.publisher" placeholder="请输入出版社" clearable @keyup.enter="handleQuery" />
              </el-form-item>


              <el-form-item label="书图片" prop="bookPic">
                <el-select v-model="queryParams.bookPic" placeholder="请选择图片状态" clearable filterable>
                  <!--                @change="handleQuery"-->

                  <el-option label="有图片" value="1" :title="'book_pic字段不为空的记录'" />
                  <el-option label="无图片" value="0" :title="'book_pic字段为空的记录'" />
                </el-select>
              </el-form-item>

              <el-form-item label="出版时间范围" prop="timeRange" label-width="100">
                <el-date-picker v-model="Range.timeRange" type="yearrange" range-separator="至" start-placeholder="开始日期"
                  end-placeholder="结束日期" value-format="YYYY" />
              </el-form-item>
              <!--0、正常  1、异常-->
              <el-form-item label="违规信息筛选" label-width="100">
                <el-select v-model="selectedOptions" multiple clearable placeholder="请选择筛选条件"
                  @change="handleOptionsChange">
                  <el-option v-for="item in filterOptions" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
              <el-form-item label="分类" prop="catgory" style="margin-right: 10px;">
                <el-input v-model="queryParams.category" placeholder="请输入分类" clearable @keyup.enter="handleQuery" />
              </el-form-item>
              <el-form-item>
                <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              </el-form-item>
              <!-- 新增：销量和高级搜索单独一行 -->
              <div style="width: 100%; display: flex; margin-top: 10px;">
                <el-form-item label="销量" prop="selectSales" style="margin-right: 10px;">
                  <el-tooltip content="专属VIP功能，请升级后使用" :disabled=isVip placement="top">
                    <el-select v-model="queryParams.saleSelect" :disabled=!isVip clearable filterable
                      placeholder="请选择销量" style="width: 180px">
                      <el-option v-for="item in salesList" :key="item.value" :label="item.label" :value="item.value" />
                    </el-select>
                  </el-tooltip>
                </el-form-item>
                <el-form-item label="已售" prop="buyCount">
                  <el-tooltip content="专属VIP功能，请升级后使用" :disabled=isVip placement="top">
                    <el-input-number :disabled=!isVip v-model="Min1Number" :min="0" :max="10000000" placeholder="最小值"
                      controls-position="right"></el-input-number>
                  </el-tooltip>
                  <span class="range-separator">至</span>
                  <el-tooltip content="专属VIP功能，请升级后使用" :disabled=isVip placement="top">
                    <el-input-number :disabled=!isVip v-model="Max1Number" :min="0" :max="10000000" placeholder="最大值"
                      controls-position="right"></el-input-number>
                  </el-tooltip>
                </el-form-item>
                <el-form-item label="在售" prop="sellCount">
                  <el-tooltip content="专属VIP功能，请升级后使用" :disabled=isVip placement="top">
                    <el-input-number :disabled=!isVip v-model="Min2Number" :min="0" :max="10000000" placeholder="最小值"
                      controls-position="right"></el-input-number>
                  </el-tooltip>
                  <span class="range-separator">至</span>
                  <el-tooltip content="专属VIP功能，请升级后使用" :disabled=isVip placement="top">
                    <el-input-number :disabled=!isVip v-model="Max2Number" :min="0" :max="10000000" placeholder="最大值"
                      controls-position="right"></el-input-number>
                  </el-tooltip>
                </el-form-item>
                <el-form-item>
                  <el-tooltip content="专属VIP享用，请进行升级" :disabled=isVip placement="top">
                    <el-button type="primary" icon="Search" @click="handleSaleQuery" :disabled=!isVip>
                      高级搜索
                    </el-button>
                  </el-tooltip>
                  <el-button icon="Key" v-show=!isVip @click="RegAdd" v-hasPermi="['zhishu:baseInfo:recharge']">充值
                  </el-button>
                  <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                </el-form-item>
              </div>
            </el-form>
          </el-card>
        </div>
      </transition>

      <el-card shadow="never">
        <template #header>
          <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
              <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['zhishu:baseInfo:add']">新增
              </el-button>
            </el-col>
            <el-col :span="1.5">
              <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()"
                v-hasPermi="['zhishu:baseInfo:edit']">修改
              </el-button>
            </el-col>
            <el-col :span="1.5">
              <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()"
                v-hasPermi="['zhishu:baseInfo:remove']" disabled>删除
              </el-button>
            </el-col>

            <el-col :span="1.5">
              <!--            <el-button type="primary" plain icon="Upload" @click="goodsAddBtn()" :disabled="multiple" v-hasPermi="['zhishu:shopGoods:goodsAdd']">发布</el-button>-->
            </el-col>

            <el-col :span="1.5">
              <el-button type="primary" plain icon="WarnTriangleFilled" @click="batchUpdateData()" :disabled="multiple"
                v-hasPermi="['zhishu:bookBase:bookssIllAdd']">违规设置
              </el-button>
            </el-col>

            <el-col :span="1.5">
              <el-button type="primary" plain icon="Money" @click="pricingUrl()"
                v-hasPermi="['zhishu:baseInfo:pricing']">
                核价
              </el-button>
            </el-col>
            <!--          -->
            <!--v-hasPermi="['zhishu:baseInfo:pricing']"-->
            <!--          <el-col :span="1.5">-->
            <!--            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['zhishu:baseInfo:export']">导出</el-button>-->
            <!--          </el-col>-->

            <!--          <el-upload-->
            <!--            ref="fileUploadRef"-->
            <!--            multiple-->
            <!--            :action="uploadFileUrl"-->
            <!--            :before-upload="handleBeforeUpload"-->
            <!--            :file-list="fileList"-->
            <!--            :limit="limit"-->
            <!--            :data="{type:1}"-->
            <!--            :on-error="handleUploadError"-->
            <!--            :on-exceed="handleExceed"-->
            <!--            :on-success="handleUploadSuccess"-->
            <!--            :show-file-list="false"-->
            <!--            :headers="headers"-->
            <!--            class="upload-file-uploader"-->
            <!--          >-->
            <!-- 上传按钮 -->
            <!-- <el-button type="primary">导入</el-button> -->
            <!--          </el-upload>-->

            <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
          </el-row>
        </template>

        <el-table v-loading="loading" :data="baseInfoList" :row-class-name="setRowClassName"
          @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="55" align="center" />
          <!--        <el-table-column label="id" align="center" prop="id" v-if="true" />-->
          <el-table-column label="书名" align="left" prop="bookName" width="200" :show-overflow-tooltip="true">
            <template #default="{ row }">
              <div class="truncate-cell"> <!-- 文本截断容器 -->
                {{ row.bookName }}
              </div>
            </template>
          </el-table-column>
          <el-table-column label="书图片" align="left" prop="bookPic" width="100" :show-overflow-tooltip="true">
            <template #default="{ row }">
              <el-image style="width: 30px;height: 30px" :src="row.bookPic" fit="scale-down"
                :preview-src-list="[currentPreviewImg]" preview-teleported @click="handlePreview(row.bookPic)">
                <template #error>
                  <div class="image-slot">
                    暂无
                  </div>
                </template>
              </el-image>
            </template>
          </el-table-column>
          <el-table-column label="ibsn" align="left" prop="isbn" width="200" />
          <el-table-column label="作者" align="left" prop="author" width="150" :show-overflow-tooltip="true">
            <template #default="{ row }">
              <div class="truncate-cell"> <!-- 文本截断容器 -->
                {{ row.author }}
              </div>
            </template>
          </el-table-column>
          <!--        <el-table-column label="编辑" align="center" prop="editor" width="150"/>-->
          <el-table-column label="出版社" align="left" prop="publisher" width="200" :show-overflow-tooltip="true">
            <template #default="{ row }">
              <div class="truncate-cell"> <!-- 文本截断容器 -->
                {{ row.publisher }}
              </div>
            </template>
          </el-table-column>
          <el-table-column label="出版时间" align="left" prop="publicationTime" width="200" />
          <el-table-column label="装帧" align="left" prop="bindingLayout" />
          <el-table-column label="定价" align="left" prop="fixPrice" width="200">
            <template #default="{ row }">
              {{ row.fixPrice / 100 }}
            </template>
          </el-table-column>

          <el-table-column label="全部销量" align="left" prop="buyCount" width="100">
            <template #default="{ row }">
              <span v-if="row.buyCount == null"></span>
              <span v-else>{{ row.buyCount.replace('人买过', '') }}</span>
            </template>
          </el-table-column>
          <el-table-column label="在售" align="left" prop="sellCount" width="70">
            <template #default="{ row }">
              <span v-if="row.sellCount == null"></span>
              <span v-else>{{ row.sellCount.replace('条', '') }}</span>
            </template>
          </el-table-column>
          <el-table-column label="违规类型" align="left" prop="IllegalType" width="220">
            <template #default="{ row }">
              <div style="display: flex; gap: 10px; flex-wrap: wrap;">
                <el-tag type="danger" v-if="row.vioBook == 1">
                  <span>违规书号</span>
                </el-tag>
                <el-tag type="danger" v-if="row.bookSet == 1">
                  <span>套装书</span>
                </el-tag>
                <el-tag type="danger" v-if="row.onenumMbooks == 1">
                  <span>一号多书</span>
                </el-tag>
                <el-tag type="danger" v-if="row.illPublisher == 1">
                  <span>违规出版社</span>
                </el-tag>
                <el-tag type="danger" v-if="row.illAuthor == 1">
                  <span>违规作者</span>
                </el-tag>
                <!--            <el-tag v-else type="success">-->
                <!--              <span>无违规</span>-->
                <!--            </el-tag>-->
              </div>
            </template>
          </el-table-column>
          <el-table-column label="销量" align="left" width="200">
            <template #default="{ row }">
              <el-tooltip placement="top" :open-delay="500" :disabled=!isVip>
                <template #content>
                  <div>7天销量: {{ row.daySale7 }}</div>
                  <div>15天销量: {{ row.daySale15 }}</div>
                  <div>30天销量: {{ row.daySale30 }}</div>
                  <div>60天销量: {{ row.daySale60 }}</div>
                  <div>90天销量: {{ row.daySale90 }}</div>
                  <div>180天销量: {{ row.daySale180 }}</div>
                  <div>365天销量: {{ row.daySale365 }}</div>
                  <div>今年销量: {{ row.thisYearSale }}</div>
                  <div>去年销量: {{ row.lastYearSale }}</div>
                  <div>总销量: {{ row.totalSale }}</div>
                </template>
                <div>
                  <span>
                    7天销量: {{ row.daySale7 }}
                  </span>
                  <!--              <span v-if="row.salesData?.[0]?.daySale7">-->
                  <!--                     7天销量: {{ row.salesData[0].daySale7}}-->
                  <!--              </span>-->
                  <!--                <span v-else>点击查看销量详情</span>-->
                </div>
              </el-tooltip>
            </template>
          </el-table-column>

          <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
            <template #default="scope">
              <el-tooltip content="修改" placement="top">
                <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)"
                  v-hasPermi="['zhishu:baseInfo:edit']"></el-button>
              </el-tooltip>
              <el-tooltip content="删除" placement="top">
                <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)"
                  v-hasPermi="['zhishu:baseInfo:remove']" disabled></el-button>
              </el-tooltip>
            </template>
          </el-table-column>
        </el-table>

        <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize" @pagination="getList" />
      </el-card>

      <!--    :disabled="!dialog.isAdd"-->
      <!-- 添加或修改基础信息对话框 -->
      <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body>
        <el-form ref="baseInfoFormRef" :model="form" :rules="rules" label-width="80px">
          <el-form-item label="书名" prop="bookName">
            <el-input v-model="form.bookName" type="textarea" placeholder="请输入内容" />
          </el-form-item>
          <el-form-item label="ibsn" prop="isbn">
            <el-input v-model="form.isbn" placeholder="请输入内容" :disabled="!dialog.isAdd" />
          </el-form-item>
          <el-form-item label="作者" prop="author">
            <el-input v-model="form.author" type="textarea" placeholder="请输入内容" :disabled="!dialog.isAdd" />
          </el-form-item>
          <el-form-item label="装帧" prop="bindingLayout">
            <el-input v-model="form.bindingLayout" placeholder="请输入内容" :disabled="!dialog.isAdd" />
          </el-form-item>
          <el-form-item label="出版社" prop="publisher">
            <el-input v-model="form.publisher" placeholder="请输入内容" :disabled="!dialog.isAdd" />
          </el-form-item>
          <el-form-item label="开本" prop="format">
            <el-input v-model="form.format" placeholder="请输入内容" :disabled="!dialog.isAdd" />
          </el-form-item>

          <el-form-item label="出版时间" prop="publicationTime">
            <el-input v-model="form.publicationTime" placeholder="请输入内容" :disabled="!dialog.isAdd" />
          </el-form-item>
          <el-form-item label="印刷时间" prop="printTime">
            <el-input v-model="form.printTime" placeholder="请输入内容" :disabled="!dialog.isAdd" />
          </el-form-item>
          <el-form-item label="定价" prop="fixPrice">
            <el-input v-model="form.fixPrice" placeholder="请输入内容" :disabled="!dialog.isAdd" />
          </el-form-item>
          <el-form-item label="备注" prop="remark">
            <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" :disabled="!dialog.isAdd" />
          </el-form-item>
        </el-form>
        <template #footer>
          <div class="dialog-footer">
            <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
            <el-button @click="cancel">取 消</el-button>
          </div>
        </template>
      </el-dialog>

      <!--    违规设置信息弹窗-->
      <el-dialog :title="dialog.illegalSetUpTitle" v-model="dialog.illegalSetUp" width="600px" append-to-body>
        <el-form ref="taskFormRef" :model="form" style="height:80px">
          <div style="display: flex; flex-wrap: wrap; gap: 10px 0px;">
            <el-checkbox v-model="vioBook" label="违规书号" border />
            <el-checkbox v-model="bookSet" label="套装书" border />
            <el-checkbox v-model="onenumMbooks" label="一号多书" border />
            <el-checkbox v-model="illPublisher" label="违规出版社" border />
            <el-checkbox v-model="illAuthor" label="违规作者" border />
          </div>
        </el-form>
        <template #footer>
          <div class="dialog-footer">
            <el-button :loading="buttonLoading" type="primary" @click="submitFormIllgeData">确 定</el-button>
            <el-button @click="cancel">取 消</el-button>
          </div>
        </template>
      </el-dialog>


      <!--    核价链接弹窗-->
      <el-dialog :title="dialog.pricingTitle" v-model="dialog.pricing" @close="handleDialogClose" width="800px"
        append-to-body>
        <div style="padding: 10px;">
          <!-- 店铺选择 -->
          <el-form-item label="店铺选择:" prop="shopId" label-width="100px">
            <el-select v-model="formShops.shopId" placeholder="请选择店铺" clearable filterable style="width: 300px">
              <el-option v-for="item in allShopList" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>



          <el-form-item label="核价数量选择" prop="numbers" label-width="100">
            <el-select v-model="numbers" clearable placeholder="请选择匹配数量" style="width: 200px">
              <el-option v-for="item in numberList" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>

          <!--        <el-form-item label="图片选择" prop="picchange" label-width="100" >-->
          <!--          <el-radio-group v-model="picchnage">-->
          <!--            <el-radio value="1" size="large" border>官图</el-radio>-->
          <!--            <el-radio value="2" size="large" border>孔网图</el-radio>-->
          <!--          </el-radio-group>-->
          <!--        </el-form-item>-->

        </div>
        <div style="height: 110px; padding: 10px;">
          <span>{{ pricingLink }}</span>
        </div>
        <template #footer>
          <div class="dialog-footer">
            <el-button v-if="pricingLink" @click="copyLink" type="primary" style="margin-left: 10px;">
              一键复制
            </el-button>
            <el-button @click="sumbitPricing" v-if="!pricingLink">确 定</el-button>
            <el-button @click="cancel">取 消</el-button>
          </div>
        </template>
      </el-dialog>

      <el-dialog :title="dialog.titlePrcode" v-model="dialog.visiblePrcode" width="360px" append-to-body>
        <div style="text-align: center;font-size: 20px">
          <span>开通高级搜索功能请使用微信支付:98</span>
          <img :src="qrCodeText" alt="Base64 图片" />
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script setup name="BaseInfo" lang="ts">
import {
  addBaseInfo,
  delBaseInfo,
  getBaseInfo, getImage, getUseriSVip,
  linkEncry,
  listBaseInfo, pageRecharge, postRecharge, setBalance,
  updateBaseIllgeData,
  updateBaseInfo
} from '@/api/zhishu/baseInfo';
import { BaseInfoForm, BaseInfoQuery, BaseInfoVO, IllDataUpSet } from '@/api/zhishu/baseInfo/types';
import { getImageMd5 } from '@/api/zhishu/image';
import { propTypes } from '@/utils/propTypes';
import { globalHeaders } from '@/utils/request';
import md5 from 'blueimp-md5';
import { ElMessage } from 'element-plus';
import { ref } from 'vue';
import { getListShop } from '@/api/zhishu/shop';
import { string } from 'vue-types';
import { addGoods } from '@/api/zhishu/shopGoods';
import { getPatiAndPageCode } from '@/api/zhishu/pdd';
import { UserRechargeForm } from '@/api/zhishu/userRecharge/types';
import { checkTask, editRechargeToError } from '@/api/zhishu/userRecharge';
import { async } from 'fast-glob';


const { proxy } = getCurrentInstance() as ComponentInternalInstance;


const {
  t_recharge_way,
  t_recharge_status
} = toRefs<any>(
  proxy?.useDict(
    't_recharge_way',
    't_recharge_status'
  )
);

const payAmount = ref(0);

/** 扣款操作按钮 */
const RegAdd = async () => {
  const res = await setBalance();
  if (res.status === 303) {
    console.log('返回的数据：', res.price);
    recharge(res.price);
    payAmount.value = res.price / 100;
  } else {
    ElMessage.warning(res.message);
  }
  //获取res数据当显示余额不足时使用warning,操作成功使用success，操作失败使用error
  // if(res==="操作成功"){
  //   ElMessage.success(res);
  //   isVip.value=true
  // }else if(res==="余额不足"){
  //   ElMessage.warning("余额不足，请进行充值");
  // }else{
  //   ElMessage.error("操作失败");
  // }
};

let spareImage: string = "";


const getBookImageUrl = async (row): Promise<string> => {

  if (!row.bookPic.startsWith("https")) {
    const res = await getImage(row.isbn)
    if (res.success == true) {
      spareImage = "https://" + res.image_url;
      return spareImage;
    }
  }
  return row.bookPic;

  //
  // //校验阿里云是否存在图片
  // if (row.bookPic != null || row.bookPic != undefined) {
  //   let imageUrl = await getImageMd5(row.bookPic, row.bookName);
  //   const exists = await checkImageExists(imageUrl);
  //   if(exists==false){
  //     const res=await getImage(row.isbn)
  //     if(res.success==true){
  //       spareImage="https://"+res.image_url;
  //       return spareImage;
  //     }
  //   }
  //   if (exists == true) {
  //     return imageUrl;
  //   }
  // }
  //

  // const imageEnd = ref('');
  // const imageUrlOne = 'https://static.buzhiyushu.cn/images/';
  // const imageUrlTwo = 'https://book.goods.img.buzhiyushu.cn/';
  // if (row.bookPic != null || row.bookPic != undefined) {
  //   const image = imageUrlOne + row.bookPic;
  //   const exists = await checkImageExists(image);
  //   if (exists === true) {
  //     imageEnd.value = image;
  //   } else {
  //     const hashedTitle = md5(row.bookName);
  //     const imageTwo = imageUrlTwo + hashedTitle.charAt(0) + '/' + row.isbn + '_01.jpg';
  //     imageEnd.value = imageTwo;
  //   }
  // }
  // return imageEnd.value;
};
const checkImageExists = async (url) => {
  return new Promise((resolve) => {
    const img = new Image();
    img.onload = () => resolve(true);
    img.onerror = () => resolve(false);
    img.src = url;
  });
};

const baseInfoList = ref<BaseInfoVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const baseInfoFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: '',
  isAdd: false
});

const Range = reactive({
  timeRange: []
});
const paymentVerified = ref(false);
const showPaymentDialog = ref(false);
// const qrCodeText = ref('');
const paymentTaskId = ref(''); const Min1Number = ref(0);
const Max1Number = ref(999999);
const Min2Number = ref(0);
const Max2Number = ref(999999);

const initFormData: BaseInfoForm = {
  id: undefined,
  bookName: undefined,
  bookPic: undefined,
  isbn: undefined,
  author: undefined,
  editor: undefined,
  bindingLayout: undefined,
  publisher: undefined,
  edition: undefined,
  format: undefined,
  languages: undefined,
  publicationTime: undefined,
  printTime: undefined,
  paper: undefined,
  pages: undefined,
  wordage: undefined,
  fixPrice: undefined,
  buyCount: undefined,
  sellCount: undefined,
  content: undefined,
  remark: undefined,
  vioBook: 0,
  bookSet: 0,
  onenumMbooks: 0,
  illPublisher: 0
};
const data = reactive<PageData<BaseInfoForm, BaseInfoQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    bookName: undefined,
    bookPic: undefined,
    isbn: undefined,
    author: undefined,
    editor: undefined,
    bindingLayout: undefined,
    publisher: undefined,
    edition: undefined,
    format: undefined,
    languages: undefined,
    publicationTime: undefined,
    publicationStartTime: undefined,
    publicationEndTime: undefined,
    min1: undefined,
    max1: undefined,
    min2: undefined,
    max2: undefined,
    printTime: undefined,
    paper: undefined,
    pages: undefined,
    wordage: undefined,
    fixPrice: undefined,
    buyCount: undefined,
    sellCount: undefined,
    content: undefined,
    buy_counts: undefined,
    sell_counts: undefined,
    publiction_times: undefined,
    vioBook: 0,
    bookSet: 0,
    onenumMbooks: 0,
    illPublisher: 0,
    illAuthor: 0,
    shopIds: undefined,
    total: undefined,
    shopNames: undefined,
    numbers: undefined,
    imageSelect: undefined,
    saleSelect: undefined,
    category: undefined,
    params: {}
  },
  rules: {
    id: [
      { required: true, message: 'id不能为空', trigger: 'blur' }
    ]
  }
});


const { queryParams, form, rules } = toRefs(data);

const currentPreviewImg = ref(''); // 仅存储当前点击的图片

// 点击事件处理
const handlePreview = (imgUrl) => {
  currentPreviewImg.value = imgUrl;
};

// 基础警告

const chageNumber = async () => {
  if (Min1Number.value != null && Max2Number.value != null) {
    if (Min2Number.value > Max2Number.value) {
      ElMessage.warning('在售->最小值大于最大值,自动将最小值等于最大值');
      Min2Number.value = Max2Number.value;
    }
  }
};
const handleNumber = async () => {
  if (Min1Number.value != null && Min2Number.value != null) {
    if (Min1Number.value > Max1Number.value) {
      ElMessage.warning('已售->最小值大于最大值,自动将最小值等于最大值');
      Min1Number.value = Max1Number.value;
    }
  }
};

const numberList = [
  { value: '1', label: '小于等于1万条' },
  { value: '3', label: '小于等于3万条' },
  { value: '5', label: '小于等于5万条' },
  { value: '12', label: '小于等于12万条' },
  { value: '25', label: '小于等于25万条' },
  { value: '35', label: '小于等于35万条' },
  { value: '55', label: '小于等于55万条' }
];
const numbers = ref();
// 新增的地方

const formIllge = reactive<IllDataUpSet>({
  ids: undefined,
  vioBook: undefined,
  bookSet: undefined,
  onenumMbooks: undefined,
  illPublisher: undefined,
  illAuthor: undefined
});


const vioBook = ref(0);
const bookSet = ref(0);
const onenumMbooks = ref(0);
const illPublisher = ref(0);
const illAuthor = ref(0);


//批量设置违规信息
const batchUpdateData = async (row?: BaseInfoVO) => {
  const _ids = row?.id || ids.value;
  formIllge.ids = _ids;
  dialog.illegalSetUp = true;
  dialog.illegalSetUpTitle = '设置违规类型';
};
//违规设置提交按钮
const submitFormIllgeData = async () => {

  formIllge.vioBook = vioBook.value ? 1 : 0;
  formIllge.bookSet = bookSet.value ? 1 : 0;
  formIllge.onenumMbooks = onenumMbooks.value ? 1 : 0;
  formIllge.illPublisher = illPublisher.value ? 1 : 0;
  formIllge.illAuthor = illAuthor.value ? 1 : 0;
  await updateBaseIllgeData(formIllge).finally(() => buttonLoading.value = false);
  proxy?.$modal.msgSuccess('操作成功');
  dialog.illegalSetUp = false;
  await getList();

};

const pricingLink = ref('');
const picchnage = ref('1');
const pricingUrl = async () => {
  getShopList();
  dialog.pricing = true;
  dialog.pricingTitle = '核价链接';
};


// const shopIdData=ref([]);
// const sumbitPricing=async ()=>{
//   // 验证输入条件
//   if (shopIdData.value == null || numbers.value == null) {
//     if (shopIdData.value == null) {
//       ElMessage.warning("请选择店铺");
//     }
//     if (numbers.value == null) {
//       ElMessage.warning("请选择核价数量");
//     }
//     return; // 直接返回，不执行后续代码
//   }
//   // 同步更新 shopNames（假设 shopList 是你选项的数据源）
//   const selectedNames = shopIdData.value.map(id => {
//     const found = shopList.find(item => item.value === id);
//     return found ? found.label : '';
//   });
//   queryParams.value.shopIds=shopIdData;
//   queryParams.value.total=total;
//   queryParams.value.shopNames=selectedNames;
//   queryParams.value.numbers=numbers.value;
//   const res=await linkEncry(queryParams.value);
//   pricingLink.value=res;
//   console.log("加密链接：",pricingLink.value)
// }

//核价提交按钮
const sumbitPricing = async () => {
  // 验证是否选中店铺和数量
  if (!formShops.shopId || !numbers.value) {
    ElMessage.warning(!formShops.shopId ? '请选择店铺' : '请选择核价数量');
    return;
  }

  // 获取选中的店铺名称（用于提交）
  const selectedShop = allShopList.value.find(item => item.value === formShops.shopId);
  const selectedName = selectedShop ? selectedShop.label : '';

  queryParams.value.imageSelect = picchnage.value;
  queryParams.value.shopIds = [formShops.shopId]; // 转换为数组格式以兼容后端
  queryParams.value.total = total;
  queryParams.value.shopNames = [selectedName]; // 转换为数组格式以兼容后端
  queryParams.value.numbers = numbers.value;

  // 调用接口
  const res = await linkEncry(queryParams.value);
  pricingLink.value = res;
  console.log('生成的核价链接:', pricingLink.value);
};


// 弹窗关闭后执行逻辑
const handleDialogClose = () => {
  pricingLink.value = '';
  picchnage.value = '1';
  queryParams.value.numbers = null;
  numbers.value = null;
  formShops.shopId = null; // 重置为单选
  shopList = [];
  kfzShopList.value = [];
  pddShopList.value = [];
  allShopList.value = [];
};

//违规信息条件查询

const selectedOptions = ref([]);
const filterOptions = [
  { value: 'vioBook', label: '违规书号' },
  { value: 'bookSet', label: '套装书' },
  { value: 'onenumMbooks', label: '一号多书' },
  { value: 'illPublisher', label: '违规出版社' },
  { value: 'illAuthor', label: '违规作者' }
];
const handleOptionsChange = async (selectedValues: string[]) => {

  console.log('添加的数据：', selectedValues);
  // 1. 定义所有可能被操作的字段
  const allFilterFields = ['vioBook', 'bookSet', 'onenumMbooks', 'illPublisher', 'illAuthor'];

  // // 2. 先将所有相关字段重置为 undefined（或 0）
  // // 2. 先将所有字段初始化为 0（未选中）
  // allFilterFields.forEach(field => {
  //   data.queryParams[field] = 0;
  // });
  //
  //
  // // 3. 只给当前选中的字段设置为 1
  // selectedValues.forEach(value => {
  //   if (allFilterFields.includes(value)) {
  //     data.queryParams[value] = 1;
  //   }
  // });

  if (selectedValues.length === 0) {
    // 如果什么都没选，所有字段设为 0
    allFilterFields.forEach(field => {
      data.queryParams[field] = 0;
    });
  } else {
    // 如果有选中的字段：
    // 1. 先全部设为 0（未选中状态）
    allFilterFields.forEach(field => {
      data.queryParams[field] = 0;
    });

    // 2. 再把选中的字段设为 1
    selectedValues.forEach(value => {
      if (allFilterFields.includes(value)) {
        data.queryParams[value] = 1;
      }
    });
  }


};


//一键复制核价链接
const copyLink = async () => {
  try {
    await navigator.clipboard.writeText(pricingLink.value);
    ElMessage.success('复制成功');
  } catch (err) {
    ElMessage.error('复制失败，请手动复制');
  }
};


//店铺列表
let shopList = [];
// 响应式店铺列表
const pddShopList = ref([]); // 拼多多店铺
const kfzShopList = ref([]); // 孔夫子店铺
const allShopList = ref([]); // 统一的店铺列表

// 表单数据（绑定单选框）
const formShops = reactive({
  shopId: null // 存储选中的店铺ID（单选）
});
const getShopList = async () => {
  const res = await getListShop();
  // 清空之前的数据
  pddShopList.value = [];
  kfzShopList.value = [];
  allShopList.value = [];

  res.forEach(item => {
    const shopItem = {
      value: item.id,
      label: item.shopName
    };

    if (item.shopType === '1') {
      pddShopList.value.push(shopItem);
    } else if (item.shopType === '2') {
      kfzShopList.value.push(shopItem);
    }

    // 添加到统一列表
    allShopList.value.push(shopItem);
  });
};

const selectSales = ref();
//
// //查询销量
// const SalesSearch=async (isbn:string)=>{
//   try {
//     const res = await saleSearch(isbn);
//     return res;
//   } catch (error) {
//     console.error('获取销售数据失败:', error);
//     return null;
//   }
// }

// 销量选择
const salesList = [
  { value: '7', label: '7天销量' },
  { value: '15', label: '15天销量' },
  { value: '30', label: '30天销量' },
  { value: '60', label: '60天销量' },
  { value: '90', label: '90天销量' },
  { value: '180', label: '180天销量' },
  { value: '365', label: '365天销量' },
  { value: '0', label: '今年销量' },
  { value: '1', label: '去年销量' }
];

const saleMin = ref(0);
const saleMax = ref(999999);

// 充值内容
let userRegType = ref();
let userRegPrice = ref();
let minRecharge = ref();


const props = defineProps({
  modelValue: {
    type: [String, Object, Array],
    default: () => []
  },
  // 数量限制
  limit: propTypes.number.def(5),
  // 大小限制(MB)
  fileSize: propTypes.number.def(5),
  // 文件类型, 例如['png', 'jpg', 'jpeg']
  fileType: propTypes.array.def(['doc', 'xls', 'ppt', 'txt', 'pdf']),
  // 是否显示提示
  isShowTip: propTypes.bool.def(true)
});

const emit = defineEmits(['update:modelValue']);
const number = ref(0);
const uploadList = ref<any[]>([]);

const baseUrl = import.meta.env.VITE_APP_BASE_API;
const uploadFileUrl = ref(baseUrl + '/zhishu/baseInfo/importExcel'); // 上传文件服务器地址
const headers = ref(globalHeaders());

const fileList = ref<any[]>([]);
// const showTip = computed(() => props.isShowTip && (props.fileType || props.fileSize));


/** 查询基础信息列表 */
const getList = async () => {
  loading.value = true;
  if (Array.isArray(Range.timeRange) && Range.timeRange.length === 2) {
    queryParams.value.publiction_times = Range.timeRange.join(',');
  }

  if (Min1Number.value == null) {
    ElMessage.warning('已售最小值不能为空');
  } else if (Max1Number.value == null) {
    ElMessage.warning('已售最大值不能为空');
  } else {
    queryParams.value.buy_counts = [Min1Number.value, Max1Number.value].join(',');
  }

  if (Min2Number.value == null) {
    ElMessage.warning('在售最小值不能为空');
  } else if (Max2Number.value == null) {
    ElMessage.warning('在售最大值不能为空');
  } else {
    queryParams.value.sell_counts = [Min2Number.value, Max2Number.value].join(',');
  }

  if (queryParams.value.saleSelect != null) {
    console.log('销量：', queryParams.value.saleSelect);
  }

  try {
    const res = await listBaseInfo(queryParams.value);
    console.log('返回的数据', res);
    const validRows = Array.isArray(res.rows) ? res.rows : [];

    // 过滤掉无效数据（书名、ISBN为空的记录）
    const filteredRows = validRows.filter(row =>
      row.bookName && row.bookName.trim() !== '' &&
      row.isbn && row.isbn.trim() !== ''
    );

    // 先显示数据，使用原始图片URL
    baseInfoList.value = filteredRows;
    total.value = res.total;
    console.log('数据总数', baseInfoList);
    console.log(isVip.value);

    // 异步加载图片，不阻塞页面显示
    filteredRows.forEach(async (row, index) => {
      try {
        const updatedBookPic = await getBookImageUrl(row);
        // 更新对应行的图片URL
        if (baseInfoList.value[index] && baseInfoList.value[index].id === row.id) {
          baseInfoList.value[index].bookPic = updatedBookPic;
        }
      } catch (error) {
        console.error(`获取图片失败 (ISBN: ${row.isbn}):`, error);
      }
    });

  } catch (error) {
    console.error('获取列表失败:', error);
  } finally {
    loading.value = false;
  }
};


const setRowClassName = ({ row }: { row: BaseInfoVO }) => {
  if (!row.bookPic) {
    return 'row-without-image'; // 当 bookPic 为空时返回这个类名
  }
  return '';
};

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
  dialog.shopChoose = false;
  dialog.pricing = false;
  shopList = [];
  dialog.visibleRecharge = false;
  userRegType.value = null;
  userRegPrice.value = null;
  minRecharge.value = null;
  userRecFrom.rechargType = null;
  userRecFrom.rechargPrice = null;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  baseInfoFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  chageNumber();
  handleNumber();
  queryParams.value.saleSelect = null;
  queryParams.value.pageNum = 1;
  getList();
};


const saleNumber = () => {
  if (saleMin.value != null && saleMax.value != null) {
    if (saleMin.value > saleMax.value) {
      ElMessage.warning('销量范围->最小值大于最大值,自动将最小值等于最大值');
      saleMin.value = saleMax.value;
    }
  }
};
const userRecFrom: UserRechargeForm = {
  id: undefined,
  wxId: undefined,
  userId: undefined,
  rechargType: undefined,
  rechargPrice: undefined,
  successTime: undefined,
  createTime: undefined,
  allDataStr: undefined,
  status: undefined,
  commission: undefined
};


// 高级搜索
const handleSaleQuery = () => {
  chageNumber();
  handleNumber();
  saleNumber();
  queryParams.value.pageNum = 1;
  getList();
};

const isVip = ref<boolean | null>(null); // 初始值设为 null

//获取用户是否是按钮会员
const getIsVip = async () => {
  const type = 0;
  const res = await getUseriSVip(type);
  isVip.value = res;
  console.log("按钮会员：", res)

};

//页面会员
const getPageVip = async () => {
  const type = 1;
  const res = await getUseriSVip(type);
  console.log(res)
  if (res === true) {
    paymentVerified.value = res;
    showPaymentDialog.value = false;
  } else {
    pageFees();
    showPaymentDialog.value = true;
  }
  console.log("页面会员：", res)
}

//页面充值
const pageFees = async () => {
  userRecFrom.rechargType = '1';
  userRecFrom.rechargPrice = 1;
  const res = await pageRecharge(userRecFrom);
  console.log('code：', res.code, '--------------', res);

  if (res.code == 200) {
    qrCodeText.value = 'data:image/png;base64,' + res.data.img;
    dialog.visiblePrcode1 = true;
    dialog.titlePrcode1 = '支付';
    //定时器 2分钟持续获取 支付单据状态
    let count = 0;
    const maxCount = 40; // 两分钟内，每隔三秒打印一次，总共打印40次
    let intervalId = setInterval(async () => {
      if (count < maxCount) {
        const status = await checkTask(res.data.id);
        if (status == '1') {
          count = 80;
        }
        count++;

      } else {
        if (count == 40) {
          editRechargeToError(res.data.id);
        }
        dialog.visible = false;
        dialog.visiblePrcode = false;
        qrCodeText.value = undefined;
        buttonLoading.value = false;
        showPaymentDialog.value = false;
        getPageVip();
        getList();
        clearInterval(intervalId); // 停止定时器
      }
    }, 3000);
  }

}

// const  recharge=async ()=>{
//   userRecFrom.rechargType="1";
//   userRecFrom.rechargPrice=11;
//   userRecFrom.commission=1;
//   postRecharge(userRecFrom);
// }

const qrCodeText = ref('');

/** 充值提交按钮 */
const recharge = async (price: number) => {
  userRecFrom.rechargType = '1';
  userRecFrom.rechargPrice = price;
  console.log('需要的数据：', userRecFrom);
  const res = await postRecharge(userRecFrom);
  console.log('code：', res.code, '--------------', res);

  if (res.code == 200) {
    qrCodeText.value = 'data:image/png;base64,' + res.data.img;
    dialog.visiblePrcode = true;
    dialog.titlePrcode = '支付';
    //定时器 2分钟持续获取 支付单据状态
    let count = 0;
    const maxCount = 40; // 两分钟内，每隔三秒打印一次，总共打印40次
    let intervalId = setInterval(async () => {
      if (count < maxCount) {
        const status = await checkTask(res.data.id);
        if (status == '1') {
          count = 80;
        }
        count++;

      } else {
        if (count == 40) {
          editRechargeToError(res.data.id);
        }
        dialog.visible = false;
        dialog.visiblePrcode = false;
        qrCodeText.value = undefined;
        buttonLoading.value = false;
        window.location.reload(true);
        clearInterval(intervalId); // 停止定时器
      }
    }, 1500);
  }
};


/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  Min1Number.value = 0;
  Max1Number.value = 99999;
  Min2Number.value = 0;
  Max2Number.value = 99999;
  Range.timeRange = [];
  queryParams.value.buy_counts = null;
  queryParams.value.sell_counts = null;
  queryParams.value.publiction_times = null;
  queryParams.value.vioBook = 0;
  queryParams.value.bookSet = 0;
  queryParams.value.onenumMbooks = 0;
  queryParams.value.illPublisher = 0;
  queryParams.value.illAuthor = 0;
  selectedOptions.value = [];
  queryParams.value.saleSelect = null;
  queryParams.value.category = null;
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: BaseInfoVO[]) => {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.isAdd = true;
  dialog.visible = true;
  dialog.title = '添加基础信息';
};

/** 修改按钮操作 */
const handleUpdate = async (row?: BaseInfoVO) => {
  reset();
  const _id = row?.id || ids.value[0];
  const res = await getBaseInfo(_id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = '修改基础信息';
};

/** 提交按钮 */
const submitForm = () => {
  baseInfoFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateBaseInfo(form.value).finally(() => buttonLoading.value = false);
      } else {
        await addBaseInfo(form.value).finally(() => buttonLoading.value = false);
      }
      proxy?.$modal.msgSuccess('操作成功');
      dialog.visible = false;
      await getList();
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row?: BaseInfoVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除基础信息编号为"' + _ids + '"的数据项？').finally(() => loading.value = false);
  await delBaseInfo(_ids);
  proxy?.$modal.msgSuccess('删除成功');
  await getList();
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download('zhishu/baseInfo/export', {
    ...queryParams.value
  }, `baseInfo_${new Date().getTime()}.xlsx`);
};

onMounted(() => {
  getPageVip();
  getIsVip();
  getList();
});
</script>
<style>
/* 文本溢出处理 */
.truncate-cell {
  width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 增强悬浮提示样式 */
.el-tooltip__popper {
  max-width: 400px;
  word-break: break-all;
}

/* 运费模板样式 */
.freight-template-container {
  padding: 10px;
}

.overseas-section {
  margin-top: 20px;
  border-top: 1px solid #ebeef5;
  padding-top: 15px;
}

.image-slot {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  background: var(--el-fill-color-light);
  color: var(--el-text-color-secondary);
  font-size: 10px;
}
</style>
<style scoped>
/* 当 bookPic 为空时的行样式 */
:deep(.el-table .row-without-image) {
  background-color: #ffebee !important;
  /* 浅红色背景 */
}

/* 如果需要更醒目的红色 */
:deep(.el-table .row-without-image--strong) {
  background-color: #ff0000 !important;
  color: white !important;
  /* 白色文字提高可读性 */
}

/* 或者如果你使用 scoped CSS */
:deep(.el-dialog__title) {
  font-size: 23px !important;
}

.isHide {
  display: none;
}
</style>
