# 搜索过滤优化需求文档

## 介绍

优化书籍基础信息搜索功能，确保在没有明确选择违规筛选条件时，搜索请求不携带违规相关参数，并且重置后的列表显示逻辑符合用户预期。

## 需求

### 需求 1：搜索参数优化

**用户故事：** 作为用户，我希望在输入ISBN等基础搜索条件时，系统不会自动携带违规筛选参数，这样可以获得更准确的搜索结果。

#### 验收标准

1. WHEN 用户输入ISBN、书名、作者或出版社等基础搜索条件 AND 没有选择违规信息筛选条件 THEN 系统SHALL不在请求中携带vioBook、bookSet、onenumMbooks、illPublisher、illAuthor等参数
2. WHEN 用户明确选择了违规信息筛选条件 THEN 系统SHALL只携带用户选中的违规筛选参数
3. WHEN 用户清空违规信息筛选条件 THEN 系统SHALL从后续请求中移除所有违规相关参数

### 需求 2：重置功能优化

**用户故事：** 作为用户，我希望点击重置按钮后，列表显示的数据逻辑与初始加载时保持一致，不显示带有违规标记的数据。

#### 验收标准

1. WHEN 用户点击重置按钮 THEN 系统SHALL清除所有搜索条件包括违规筛选条件
2. WHEN 重置完成后 THEN 系统SHALL发送不包含违规筛选参数的请求
3. WHEN 重置后的数据加载完成 THEN 列表SHALL显示符合默认筛选逻辑的数据（不显示违规数据）
4. WHEN 重置后 THEN 违规信息筛选下拉框SHALL显示为未选择状态

### 需求 3：默认数据显示逻辑

**用户故事：** 作为用户，我希望在没有明确选择显示违规数据时，系统默认不显示带有违规标记的数据行。

#### 验收标准

1. WHEN 页面初始加载 THEN 系统SHALL默认过滤掉带有违规标记的数据
2. WHEN 用户进行基础搜索（非违规筛选） THEN 系统SHALL在结果中排除违规数据
3. WHEN 用户明确选择查看违规数据 THEN 系统SHALL显示相应的违规数据
4. WHEN 违规数据被过滤时 THEN 系统SHALL在界面上提供明确的提示说明过滤逻辑