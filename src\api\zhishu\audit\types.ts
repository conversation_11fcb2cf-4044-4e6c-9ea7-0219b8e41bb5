export interface AuditVO {
  /**
   * ID
   */
  id: string | number;

  /**
   * 用户ID
   */
  userId: string | number;

  /**
   * 管理员ID
   */
  adminId: string | number;

  /**
   * 审核状态（0通过 1未通过 2待审核)
   */
  status: string;

  /**
   * 企业名称
   */
  companyName : string;

  /**
   * 企业类型
   */
  companyType : string;

  /**
   * 联系人名
   */
  contactPerson : string;

  /**
   * 联系方式
   */
  contactPhone : string;

  /**
   * 邮箱
   */
  email : string;

  /**
   * 营业执照
   */
  license : string;

  /**
   * 备注
   */
  remark : string;

  /**
   * 用户昵称
   */
  userName : string;

}

export interface AuditForm extends BaseEntity {
  /**
   * 用户ID
   */
  userId?: string | number;

  /**
   * 用户昵称
   */
  userName?: string;

  /**
   * 管理员ID
   */
  adminId?: string | number;

  /**
   * 审核状态（0通过 1未通过 2待审核)
   */
  status?: string;

  /**
   * 企业名称
   */
  companyName?: string;

  /**
   * 企业类型
   */
  companyType?: string;

  /**
   * 联系人名
   */
  contactPerson?: string;

  /**
   * 联系方式
   */
  contactPhone?: string;

  /**
   * 邮箱
   */
  email?: string;

  /**
   * 营业执照
   */
  license?: string;

  /**
   * 备注
   */
  remark?: string;

}

export interface AuditQuery extends PageQuery {

  /**
   * 用户ID
   */
  userId?: string | number;

  /**
   * 用户昵称
   */
  userName?: string;

  /**
   * 管理员ID
   */
  adminId?: string | number;

  /**
   * 审核状态（0通过 1未通过)
   */
  status?: string;

  /**
   * 企业名称
   */
  companyName?: string;

  /**
   * 企业类型
   */
  companyType?: string;

  /**
   * 联系人名
   */
  contactPerson?: string;

  /**
   * 联系方式
   */
  contactPhone?: string;

  /**
   * 邮箱
   */
  email?: string;

  /**
   * 营业执照
   */
  license?: string;

  /**
   * 备注
   */
  remark?: string;

    /**
     * 日期范围参数
     */
    params?: any;
}
// 定义接口规范请求参数
export interface AuditParams {
  id: number | string
  status: '0' | '1'
}

export interface SendFailed{
  id: number | string
  status: '0' | '1'
  remark: string
}
