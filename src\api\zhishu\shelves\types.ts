export interface ShelvesVO {
  /**
   * 货架id
   */
  id: string | number;

  /**
   * 货架名称
   */
  name: string;


  /**
   * 单位
   */
  unit: string;

  /**
   * 货架容量
   */
  sheCapMax: number;

  /**
   * 货架状态（0正常 1停用）
   */
  status: string;

  /**
   * 货架编码
   */
  code: string;

  /**
   * 仓库名称
   */
  depotName: string;



}

export interface ShelvesForm extends BaseEntity {
  /**
   * 货架id
   */
  id?: string | number;

  /**
   * 货架名称
   */
  name?: string;

  /**
   * 单位
   */
  unit?: string;


  /**
   * 货架容量
   */
  sheCapMax?: number;

  /**
   * 仓库id
   */
  depotId?: string | number;

  /**
   * 货架状态（0正常 1停用）
   */
  status?: string;

  /**
   * 货架编码
   */
  code?: string;

  /**
   * 仓库名称
   */
  depotName?: string;
  /**
   * 是否有下一级
   */
  hasChildren?:boolean;

  /**
   * 层级
   */
  level:number;

}

export interface ShelvesQuery extends PageQuery {

  /**
   * 货架名称
   */
  name?: string;

  /**
   * 单位
   */
  unit?: string;

  /**
   * 货架容量
   */
  sheCapMax?: number;

  /**
   * 货架状态（0正常 1停用）
   */
  status?: string;

  /**
   * 货架编码
   */
  code?: string;

  /**
   * 仓库名称
   */
  depotName?: string;

    /**
     * 日期范围参数
     */
    params?: any;
}



