export interface ShopOrderVO {
  /**
   *
   */
  id: string | number;

  /**
   * 店铺id
   */
  shopId: string | number;

  /**
   * 店铺名称
   */
  shopName: string;

  /**
   * 地址
   */
  address: string;

  /**
   * 详细地址
   */
  addressMask: string;

  /**
   * 售后状态 0：无售后 2：买家申请退款，待商家处理 3：退货退款，待商家处理 4：商家同意退款，退款中 5：平台同意退款，退款中 6：驳回退款，待买家处理 7：已同意退货退款,待用户发货 8：平台处理中 9：平台拒绝退款，退款关闭 10：退款成功 11：买家撤销 12：买家逾期未处理，退款失败 13：买家逾期，超过有效期 14：换货补寄待商家处理 15：换货补寄待用户处理 16：换货补寄成功 17：换货补寄失败 18：换货补寄待用户确认完成 21：待商家同意维修 22：待用户确认发货 24：维修关闭 25：维修成功 27：待用户确认收货 31：已同意拒收退款，待用户拒收 32：补寄待商家发货 33：同意召回后退款，待商家召回
   */
  afterSalesStatus: number;

  /**
   * 买家留言信息
   */
  buyerMemo: string;

  /**
   * 成交状态：0：未成交、1：已成交、2：已取消
   */
  confirmStatus: number;

  /**
   * 成交时间
   */
  confirmTime: string;

  /**
   * 订单创建时间
   */
  createdTime: string;

  /**
   * 是否当日发货，1-是，0-否
   */
  deliveryOneDay: string;

  /**
   * 折扣金额，单位：元，折扣金额=平台优惠+商家优惠+团长免单优惠金额
   */
  discountAmount: string;

  /**
   * 多多支付立减金额，单位：元
   */
  duoDuoPayReduction: number;

  /**
   * 是否多多批发
   */
  duoduoWholesale: number;

  /**
   * 商品金额
   */
  goodsAmount: number;

  /**
   * 仓库编码
   */
  depotCode: string;

  /**
   * 仓库id
   */
  depotId: string | number;

  /**
   * 支付申报订单号
   */
  depotName: string;

  /**
   * 仓库类型，1：自有仓 2：订阅仓 两者都不是则传空
   */
  depotType: number;

  /**
   * 货品id
   */
  wareId: string | number;

  /**
   * 货品名称
   */
  wareName: string;

  /**
   * 货品编码
   */
  wareSn: string;

  /**
   * 货品类型（0：普通货品:1：组合货品）
   */
  wareType: string;

  /**
   * 订单编号
   */
  orderSn: string;

  /**
   * 订单状态
   */
  orderStatus: number;

  /**
   * 支付金额
   */
  payAmount: number;

  /**
   * 支付单号
   */
  payNo: number;

  /**
   * 支付时间
   */
  payTime: string;

  /**
   * 支付方式
   */
  payType: string;

  /**
   * 平台优惠金额
   */
  platformDiscount: number;

  /**
   * 邮费
   */
  postage: number;

  /**
   * 预售时间
   */
  preSaleTime: string;

  /**
   * 承诺送达时间
   */
  promiseDeliveryTime: string;

  /**
   * 省份
   */
  province: string;

  /**
   * 省份编码
   */
  provinceId: string | number;

  /**
   * 确认收货时间
   */
  receiveTime: string;
  /**
   * 市
   */
 city:string;

  /**
   * 市编码
   */
  cityId:string;

  /**
   * 收件人地址
   */
  receiverAddress: string;

  /**
   * 收件人地址
   */
  receiverAddressMask: string;

  /**
   * 收件人姓名
   */
  receiverName: string;

  /**
   * 收件人姓名
   */
  receiverNameMask: string;

  /**
   * 确认收货时间
   */
  receiverPhone: string;

  /**
   * 收件人手机号（打码）
   */
  receiverPhoneMask: string;

  /**
   * 售后状态
   */
  refundStatus: number;

  /**
   * 订单备注
   */
  remark: string;

  /**
   * 1-红色，2-黄色，3-绿色，4-蓝色，5-紫色
   */
  remarkTag: string;

  /**
   * 订单备注标记名称
   */
  remarkTagName: string;

  /**
   * 退货包运费，1:是，0:否
   */
  returnFreightPayer: number;

  /**
   * 订单审核状态（0-正常订单， 1-审核中订单）
   */
  riskControlStatus: number;

  /**
   * 是否门店自提
   */
  selfContained: number;

  /**
   * 商家优惠金额
   */
  sellerDiscount: number;

  /**
   * 缺货处理状态 -1:无缺货处理 0: 缺货待处理 1缺货已处理
   */
  stockOutHandleStatus: number;

  /**
   * 全国联保，1:是，0:否
   */
  supportNationwideWarranty: string | number;

  /**
   * 区，乡镇
   */
  town: string;

  /**
   * 区县编码
   */
  townId: string | number;

  /**
   * 快递单号
   */
  trackingNumber: string;

  /**
   * 以旧换新国家补贴金额，单位：元
   */
  tradeInNationalSubsidyAmount: string | number;

  /**
   * 订单类型 0-普通订单 ，1- 定金订单
   */
  tradeType: number;

  /**
   * 订单的更新时间
   */
  updatedAt: string;

  /**
   * 催发货时间
   */
  urgeShippingTime: string;

  /**
   * 预约配送日期
   */
  yypsDate: string;

  /**
   * 预约配送时段
   */
  yypsTime: string;

  /**
   * 合单ID2
   */
  openAddressId2: string | number;

  /**
   * 店铺状态（0正常 1停用）
   */
  status: string;

  /**
   * 订单异常类型状态
   */
  orderExceptionTypeList:string[];

  /**
   * 订单状态
   */
  orderSourceType:number;
  /**
   * 商品信息
   */
  orderItemList:OrderItem;
  /**
   * 货号
   */
  artNo:string;

}

export interface ShopOrderForm extends BaseEntity {
  /**
   *
   */
  id?: string | number;

  /**
   * 店铺id
   */
  shopId?: string | number;

  /**
   * 店铺名称
   */
  shopName?: string;

  /**
   * 地址
   */
  address?: string;

  /**
   * 详细地址
   */
  addressMask?: string;

  /**
   * 售后状态 0：无售后 2：买家申请退款，待商家处理 3：退货退款，待商家处理 4：商家同意退款，退款中 5：平台同意退款，退款中 6：驳回退款，待买家处理 7：已同意退货退款,待用户发货 8：平台处理中 9：平台拒绝退款，退款关闭 10：退款成功 11：买家撤销 12：买家逾期未处理，退款失败 13：买家逾期，超过有效期 14：换货补寄待商家处理 15：换货补寄待用户处理 16：换货补寄成功 17：换货补寄失败 18：换货补寄待用户确认完成 21：待商家同意维修 22：待用户确认发货 24：维修关闭 25：维修成功 27：待用户确认收货 31：已同意拒收退款，待用户拒收 32：补寄待商家发货 33：同意召回后退款，待商家召回
   */
  afterSalesStatus?: number;

  /**
   * 买家留言信息
   */
  buyerMemo?: string;

  /**
   * 成交状态：0：未成交、1：已成交、2：已取消
   */
  confirmStatus?: number;

  /**
   * 成交时间
   */
  confirmTime?: string;

  /**
   * 订单创建时间
   */
  createdTime?: string;

  /**
   * 是否当日发货，1-是，0-否
   */
  deliveryOneDay?: string;

  /**
   * 折扣金额，单位：元，折扣金额=平台优惠+商家优惠+团长免单优惠金额
   */
  discountAmount?: string;

  /**
   * 多多支付立减金额，单位：元
   */
  duoDuoPayReduction?: number;

  /**
   * 是否多多批发
   */
  duoduoWholesale?: number;

  /**
   * 商品金额
   */
  goodsAmount?: number;

  /**
   * 仓库编码
   */
  depotCode?: string;

  /**
   * 仓库id
   */
  depotId?: string | number;

  /**
   * 支付申报订单号
   */
  depotName?: string;

  /**
   * 仓库类型，1：自有仓 2：订阅仓 两者都不是则传空
   */
  depotType?: number;

  /**
   * 货品id
   */
  wareId?: string | number;

  /**
   * 货品名称
   */
  wareName?: string;

  /**
   * 货品编码
   */
  wareSn?: string;

  /**
   * 货品类型（0：普通货品:1：组合货品）
   */
  wareType?: string;

  /**
   * 订单编号
   */
  orderSn?: string;

  /**
   * 订单状态
   */
  orderStatus?: number;

  /**
   * 支付金额
   */
  payAmount?: number;

  /**
   * 支付单号
   */
  payNo?: number;

  /**
   * 支付时间
   */
  payTime?: string;

  /**
   * 支付方式
   */
  payType?: string;

  /**
   * 平台优惠金额
   */
  platformDiscount?: number;

  /**
   * 邮费
   */
  postage?: number;

  /**
   * 预售时间
   */
  preSaleTime?: string;

  /**
   * 承诺送达时间
   */
  promiseDeliveryTime?: string;

  /**
   * 省份
   */
  province?: string;

  /**
   * 省份编码
   */
  provinceId?: string | number;

  /**
   * 确认收货时间
   */
  receiveTime?: string;

  /**
   * 收件人地址
   */
  receiverAddress?: string;

  /**
   * 收件人地址
   */
  receiverAddressMask?: string;

  /**
   * 收件人姓名
   */
  receiverName?: string;

  /**
   * 收件人姓名
   */
  receiverNameMask?: string;

  /**
   * 确认收货时间
   */
  receiverPhone?: string;

  /**
   * 收件人手机号（打码）
   */
  receiverPhoneMask?: string;

  /**
   * 售后状态
   */
  refundStatus?: number;

  /**
   * 订单备注
   */
  remark?: string;

  /**
   * 1-红色，2-黄色，3-绿色，4-蓝色，5-紫色
   */
  remarkTag?: string;

  /**
   * 订单备注标记名称
   */
  remarkTagName?: string;

  /**
   * 退货包运费，1:是，0:否
   */
  returnFreightPayer?: number;

  /**
   * 订单审核状态（0-正常订单， 1-审核中订单）
   */
  riskControlStatus?: number;

  /**
   * 是否门店自提
   */
  selfContained?: number;

  /**
   * 商家优惠金额
   */
  sellerDiscount?: number;

  /**
   * 缺货处理状态 -1:无缺货处理 0: 缺货待处理 1缺货已处理
   */
  stockOutHandleStatus?: number;

  /**
   * 全国联保，1:是，0:否
   */
  supportNationwideWarranty?: string | number;

  /**
   * 区，乡镇
   */
  town?: string;

  /**
   * 区县编码
   */
  townId?: string | number;

  /**
   * 快递单号
   */
  trackingNumber?: string;

  /**
   * 以旧换新国家补贴金额，单位：元
   */
  tradeInNationalSubsidyAmount?: string | number;

  /**
   * 订单类型 0-普通订单 ，1- 定金订单
   */
  tradeType?: number;

  /**
   * 订单的更新时间
   */
  updatedAt?: string;

  /**
   * 催发货时间
   */
  urgeShippingTime?: string;

  /**
   * 预约配送日期
   */
  yypsDate?: string;

  /**
   * 预约配送时段
   */
  yypsTime?: string;

  /**
   * 合单ID2
   */
  openAddressId2?: string | number;

  /**
   * 店铺状态（0正常 1停用）
   */
  status?: string;

  /**
   * 订单异常类型状态
   */
  orderExceptionTypeList?:string[];

  /**
   * 订单状态
   */
  orderSourceType?:number;

  /**
   * 商品详细信息
   */
  orderItemList:OrderItem;

  /**
   * 货号
   */
  artNo:string;
}

export interface ShopOrderQuery extends PageQuery {

  /**
   * 店铺id
   */
  shopId?: string | number;

  /**
   * 店铺名称
   */
  shopName?: string;

  /**
   * 地址
   */
  address?: string;

  /**
   * 详细地址
   */
  addressMask?: string;

  /**
   * 售后状态 0：无售后 2：买家申请退款，待商家处理 3：退货退款，待商家处理 4：商家同意退款，退款中 5：平台同意退款，退款中 6：驳回退款，待买家处理 7：已同意退货退款,待用户发货 8：平台处理中 9：平台拒绝退款，退款关闭 10：退款成功 11：买家撤销 12：买家逾期未处理，退款失败 13：买家逾期，超过有效期 14：换货补寄待商家处理 15：换货补寄待用户处理 16：换货补寄成功 17：换货补寄失败 18：换货补寄待用户确认完成 21：待商家同意维修 22：待用户确认发货 24：维修关闭 25：维修成功 27：待用户确认收货 31：已同意拒收退款，待用户拒收 32：补寄待商家发货 33：同意召回后退款，待商家召回
   */
  afterSalesStatus?: number;

  /**
   * 买家留言信息
   */
  buyerMemo?: string;

  /**
   * 成交状态：0：未成交、1：已成交、2：已取消
   */
  confirmStatus?: number;

  /**
   * 成交时间
   */
  confirmTime?: string;

  /**
   * 订单创建时间
   */
  createdTime?: string;

  /**
   * 是否当日发货，1-是，0-否
   */
  deliveryOneDay?: string;

  /**
   * 折扣金额，单位：元，折扣金额=平台优惠+商家优惠+团长免单优惠金额
   */
  discountAmount?: string;

  /**
   * 多多支付立减金额，单位：元
   */
  duoDuoPayReduction?: number;

  /**
   * 是否多多批发
   */
  duoduoWholesale?: number;

  /**
   * 商品金额
   */
  goodsAmount?: number;

  /**
   * 仓库编码
   */
  depotCode?: string;

  /**
   * 仓库id
   */
  depotId?: string | number;

  /**
   * 支付申报订单号
   */
  depotName?: string;

  /**
   * 仓库类型，1：自有仓 2：订阅仓 两者都不是则传空
   */
  depotType?: number;

  /**
   * 货品id
   */
  wareId?: string | number;

  /**
   * 货品名称
   */
  wareName?: string;

  /**
   * 货品编码
   */
  wareSn?: string;

  /**
   * 货品类型（0：普通货品:1：组合货品）
   */
  wareType?: string;

  /**
   * 订单编号
   */
  orderSn?: string;

  /**
   * 订单状态
   */
  orderStatus?: number;

  /**
   * 支付金额
   */
  payAmount?: number;

  /**
   * 支付单号
   */
  payNo?: number;

  /**
   * 支付时间
   */
  payTime?: string;

  /**
   * 支付方式
   */
  payType?: string;

  /**
   * 平台优惠金额
   */
  platformDiscount?: number;

  /**
   * 邮费
   */
  postage?: number;

  /**
   * 预售时间
   */
  preSaleTime?: string;

  /**
   * 承诺送达时间
   */
  promiseDeliveryTime?: string;

  /**
   * 省份
   */
  province?: string;

  /**
   * 省份编码
   */
  provinceId?: string | number;

  /**
   * 确认收货时间
   */
  receiveTime?: string;

  /**
   * 收件人地址
   */
  receiverAddress?: string;

  /**
   * 收件人地址
   */
  receiverAddressMask?: string;

  /**
   * 收件人姓名
   */
  receiverName?: string;

  /**
   * 收件人姓名
   */
  receiverNameMask?: string;

  /**
   * 确认收货时间
   */
  receiverPhone?: string;

  /**
   * 收件人手机号（打码）
   */
  receiverPhoneMask?: string;

  /**
   * 售后状态
   */
  refundStatus?: number;

  /**
   * 1-红色，2-黄色，3-绿色，4-蓝色，5-紫色
   */
  remarkTag?: string;

  /**
   * 订单备注标记名称
   */
  remarkTagName?: string;

  /**
   * 退货包运费，1:是，0:否
   */
  returnFreightPayer?: number;

  /**
   * 订单审核状态（0-正常订单， 1-审核中订单）
   */
  riskControlStatus?: number;

  /**
   * 是否门店自提
   */
  selfContained?: number;

  /**
   * 商家优惠金额
   */
  sellerDiscount?: number;

  /**
   * 缺货处理状态 -1:无缺货处理 0: 缺货待处理 1缺货已处理
   */
  stockOutHandleStatus?: number;

  /**
   * 全国联保，1:是，0:否
   */
  supportNationwideWarranty?: string | number;

  /**
   * 区，乡镇
   */
  town?: string;

  /**
   * 区县编码
   */
  townId?: string | number;

  /**
   * 快递单号
   */
  trackingNumber?: string;

  /**
   * 以旧换新国家补贴金额，单位：元
   */
  tradeInNationalSubsidyAmount?: string | number;

  /**
   * 订单类型 0-普通订单 ，1- 定金订单
   */
  tradeType?: number;

  /**
   * 订单的更新时间
   */
  updatedAt?: string;

  /**
   * 催发货时间
   */
  urgeShippingTime?: string;

  /**
   * 预约配送日期
   */
  yypsDate?: string;

  /**
   * 预约配送时段
   */
  yypsTime?: string;

  /**
   * 合单ID2
   */
  openAddressId2?: string | number;

  /**
   * 店铺状态（0正常 1停用）
   */
  status?: string;

    /**
     * 日期范围参数
     */
    params?: any;

  /**
   * 开始时间
   */
    startTime?:string;


  /**
   * 结束时间
   */
    endTime?:string;
  /**
   * 结束时间
   */
  order_status?:string;
  /**
   * 结束时间
   */
  refund_status?:string;

  /**
   * 时间集合
   */
  dateTime?:string;
  //
  confirmTimeList: undefined,
  // startConfirmTime: undefined,
  // endConfirmTime: undefined,

  /**
   * 订单异常类型
   */
  orderExceptionTypeList?:string,
  /**
   * 订单来源
   */
  orderSourceType?:number,

  /**
   * 查询订单创建时间起始时间
   */
  createdTimeStart:undefined,

  /**
   * 查询订单创建时间截至时间
   */
  createdTimeEnd:undefined,

}

// 商品信息详细信息
export interface OrderItem{
  /**
   * 商品名称
   */
  goodsName:string,
  /**
   * 商品图片
   */
  goodsImg:string,

  /**
   * 商品价格（元）
   */
  goodsPrice:string,
  /**
   * 商品数量
   */

  goodsCount:string,
  /**
   * 货号
   */
  artNo:string,
}

// 定义单个配送方式类型
export interface ShippingMethod {
  methodId: string;
  methodName: string;
}

export interface ShopsShipments{
  orderSourceType?:number,
  shopId?: number,
  orderSn?: string,
  methodId?: string,
  trackingNumber?: string,
  userDefined?:string,
  moreShipmentNum?:string
};

export interface ShopListIds{
  shopIdList?:number[]
}
