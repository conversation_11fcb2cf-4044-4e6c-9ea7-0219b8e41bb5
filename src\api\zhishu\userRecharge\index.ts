import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { UserRechargeVO, UserRechargeForm, UserRechargeQuery } from '@/api/zhishu/userRecharge/types';

/**
 * 查询用户充值列表
 * @param query
 * @returns {*}
 */

export const listUserRecharge = (query?: UserRechargeQuery): AxiosPromise<UserRechargeVO[]> => {
  return request({
    url: '/zhishu/userRecharge/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询用户充值详细
 * @param id
 */
export const getUserRecharge = (id: string | number): AxiosPromise<UserRechargeVO> => {
  return request({
    url: '/zhishu/userRecharge/' + id,
    method: 'get'
  });
};

/**
 * 新增用户充值
 * @param data
 */
export const addUserRecharge = (data: UserRechargeForm) => {
  return request({
    url: '/zhishu/userRecharge',
    method: 'post',
    data: data
  });
};

export const userRecharge = (data: UserRechargeForm) => {
  return request({
    url: '/zhishu/userRecharge/userRecharge',
    method: 'post',
    data: data
  });
};

// 根据参数键名查询参数值
export function getConfigKey(configKey: string) {
  return request({
    url: '/zhishu/userRecharge/configKey/' + configKey,
    method: 'get'
  });
}

export const checkTask = (id) => {
  return request({
    url: '/zhishu/userRecharge/checkTask/'+id,
    method: 'get'
  });
};

export const editRechargeToError = (id) => {
  return request({
    url: '/zhishu/userRecharge/editRechargeToError/'+id,
    method: 'get'
  });
};

/**
 * 修改用户充值
 * @param data
 */
export const updateUserRecharge = (data: UserRechargeForm) => {
  return request({
    url: '/zhishu/userRecharge',
    method: 'put',
    data: data
  });
};

/**
 * 删除用户充值
 * @param id
 */
export const delUserRecharge = (id: string | number | Array<string | number>) => {
  return request({
    url: '/zhishu/userRecharge/' + id,
    method: 'delete'
  });
};
