import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { DepotOrderVO, DepotOrderForm, DepotOrderQuery } from '@/api/zhishu/depotOrder/types';

/**
 * 查询仓库订单信息列表
 * @param query
 * @returns {*}
 */

export const listDepotOrder = (query?: DepotOrderQuery): AxiosPromise<DepotOrderVO[]> => {
  return request({
    url: '/zhishu/depotOrder/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询仓库订单信息详细
 * @param id
 */
export const getDepotOrder = (id: string | number): AxiosPromise<DepotOrderVO> => {
  return request({
    url: '/zhishu/depotOrder/' + id,
    method: 'get'
  });
};

/**
 * 新增仓库订单信息
 * @param data
 */
export const addDepotOrder = (data: DepotOrderForm) => {
  return request({
    url: '/zhishu/depotOrder',
    method: 'post',
    data: data
  });
};

/**
 * 修改仓库订单信息
 * @param data
 */
export const updateDepotOrder = (data: DepotOrderForm) => {
  return request({
    url: '/zhishu/depotOrder',
    method: 'put',
    data: data
  });
};

/**
 * 删除仓库订单信息
 * @param id
 */
export const delDepotOrder = (id: string | number | Array<string | number>) => {
  return request({
    url: '/zhishu/depotOrder/' + id,
    method: 'delete'
  });
};

/**
 * 韵达发货获取订单号
 */
export const getMailNo = (id) =>{
  return request({
    url: '/zhishu/depotOrder/getMailNo?id=' + id,
    method: 'get'
  });
}

/**
 * 韵达获取pdf
 * @param id
 * @returns
 */
export const getPdf = (mailNo) =>{
  return request({
    url: '/zhishu/depotOrder/getPdf?mailNo=' + mailNo,
    method: 'get'
  });
}
  /**
 * 圆通发货获取订单号
 */
export const getMailNoByYuanTong = (id) => {
  return request({
    url: '/zhishu/depotOrder/getMailNoByYuanTong?id=' + id,
    method: 'get'
  });
}

/**
 * 圆通获取pdf
 * @param waybillNo
 * @returns
 */
export const getPdfByYuanTong = (waybillNo) => {
  // 确保参数作为字符串传递，避免被序列化为数组
  return request({
    url: `/zhishu/depotOrder/getPdfByYuanTong?waybillNo=`+waybillNo,
    method: 'get'
  });
}

/**
 * 申通获取运单号
 */
export const getMailNoByShenTong = (id) => {
  // 确保参数作为字符串传递，避免被序列化为数组
  return request({
    url: `/zhishu/depotOrder/getMailNoByShentong?id=`+id,
    method: 'get'
  });
}

/**
 * 申通获取pdf
 * @param waybillNo
 * @returns
 */
export const getPdfByShenTong = (waybillNo) => {
  return request({
    url: `/zhishu/depotOrder/getPdfByShenTong?waybillNo=`+waybillNo,
    method: 'get'
  });
}

/**
 * 中通获取运单号
 */
export const getMailNoByZhongTong = (id) => {
  // 确保参数作为字符串传递，避免被序列化为数组
  return request({
    url: `/zhishu/depotOrder/getMailNoByZhongTong?id=`+id,
    method: 'get'
  });
}

/**
 * 中通 获取pdf
 */
export const getPdfByZhongTong = (waybillNo) => {
  return request({
    url: `/zhishu/depotOrder/getPdfByZhongTong?waybillNo=`+waybillNo,
    method: 'get'
  });
}

