export interface ViolationVO {
  /**
   * 主键
   */
  id: string | number;

  /**
   * 类型  0 isbn  1 书名  2 作者  3 出版社
   */
  type: string;

  /**
   * 内容
   */
  content: string;

  /**
   * 发起人
   */
  userid: string | number;

  /**
   * 是否审核  0 未审核  1 已审核
   */
  review: string;

  /**
   * 状态（0正常 1停用）
   */
  status: string;

  remark: string;

  name: string;

  sort: string;
}

export interface ViolationForm extends BaseEntity {
  /**
   * 主键
   */
  id?: string | number;

  /**
   * 类型  0 isbn  1 书名  2 作者  3 出版社
   */
  type?: string;

  /**
   * 内容
   */
  content?: string;

  /**
   * 发起人
   */
  userid?: string | number;

  /**
   * 是否审核  0 未审核  1 已审核
   */
  review?: string;

  /**
   * 状态（0正常 1停用）
   */
  status?: string;

  remark?: string;

  name?: string;

  sort?: string;
}

export interface ViolationQuery extends PageQuery {

  /**
   * 类型  0 isbn  1 书名  2 作者  3 出版社
   */
  type?: string;

  /**
   * 内容
   */
  content?: string;

  /**
   * 发起人
   */
  userid?: string | number;

  /**
   * 是否审核  0 未审核  1 已审核
   */
  review?: string;

  /**
   * 状态（0正常 1停用）
   */
  status?: string;

    /**
     * 日期范围参数
     */
    params?: any;

    remark?: string;

    name?: string;

    sort?: string;
}



