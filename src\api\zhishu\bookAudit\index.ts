import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { BookAuditVO, BookAuditForm, BookAuditQuery, BookAudit } from '@/api/zhishu/bookAudit/types';
import { AuditParams } from '@/api/zhishu/audit/types';

/**
 * 查询图书审核管理列表
 * @param query
 * @returns {*}
 */

export const listBookAudit = (query?: BookAuditQuery): AxiosPromise<BookAuditVO[]> => {
  return request({
    url: '/zhishu/bookAudit/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询图书审核管理详细
 * @param id
 */
export const getBookAudit = (id: string | number): AxiosPromise<BookAuditVO> => {
  return request({
    url: '/zhishu/bookAudit/' + id,
    method: 'get'
  });
};

/**
 * 新增图书审核管理
 * @param data
 */
export const addBookAudit = (data: BookAuditForm) => {
  return request({
    url: '/zhishu/bookAudit',
    method: 'post',
    data: data
  });
};

/**
 * 修改图书审核管理
 * @param data
 */
export const updateBookAudit = (data: BookAuditForm) => {
  return request({
    url: '/zhishu/bookAudit',
    method: 'put',
    data: data
  });
};

/**
 * 删除图书审核管理
 * @param id
 */
export const delBookAudit = (id: string | number | Array<string | number>) => {
  return request({
    url: '/zhishu/bookAudit/' + id,
    method: 'delete'
  });
};

export const  UpdateBookStatus=(data:BookAudit)=>{
  return request({
    url: '/zhishu/bookAudit/updateStatus',
    method: 'put',
    data: data
  });
};
