<!doctype html>
<html>

<head>
  <meta charset="utf-8" />
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
  <meta name="renderer" content="webkit" />
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" />
  <link rel="icon" href="/favicon.ico" />
  <title>与书同行</title>
  <script src="https://pfile.pddpic.com/galerie-go/open_sdk/pc.202102201613.js" type="text/javascript"></script>
  <!-- 引入 JsBarcode -->
  <script src="https://cdn.jsdelivr.net/npm/jsbarcode@3.11.5/dist/JsBarcode.all.min.js"></script>
  <!-- 引入 pdf工具 -->
  <script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.4.120/pdf.min.js"></script>
  <!--[if lt IE 11
      ]><script>
        window.location.href = '/html/ie.html';
      </script><!
    [endif]-->
  <style>
    html,
    body,
    #app {
      height: 100%;
      margin: 0px;
      padding: 0px;
    }

    .chromeframe {
      margin: 0.2em 0;
      background: #ccc;
      color: #000;
      padding: 0.2em 0;
    }

    #loader-wrapper {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: 999999;
    }

    #loader {
      display: block;
      position: relative;
      left: 50%;
      top: 50%;
      width: 150px;
      height: 150px;
      margin: -75px 0 0 -75px;
      border-radius: 50%;
      border: 3px solid transparent;
      border-top-color: #fff;
      -webkit-animation: spin 2s linear infinite;
      -ms-animation: spin 2s linear infinite;
      -moz-animation: spin 2s linear infinite;
      -o-animation: spin 2s linear infinite;
      animation: spin 2s linear infinite;
      z-index: 1001;
    }

    #loader:before {
      content: '';
      position: absolute;
      top: 5px;
      left: 5px;
      right: 5px;
      bottom: 5px;
      border-radius: 50%;
      border: 3px solid transparent;
      border-top-color: #fff;
      -webkit-animation: spin 3s linear infinite;
      -moz-animation: spin 3s linear infinite;
      -o-animation: spin 3s linear infinite;
      -ms-animation: spin 3s linear infinite;
      animation: spin 3s linear infinite;
    }

    #loader:after {
      content: '';
      position: absolute;
      top: 15px;
      left: 15px;
      right: 15px;
      bottom: 15px;
      border-radius: 50%;
      border: 3px solid transparent;
      border-top-color: #fff;
      -moz-animation: spin 1.5s linear infinite;
      -o-animation: spin 1.5s linear infinite;
      -ms-animation: spin 1.5s linear infinite;
      -webkit-animation: spin 1.5s linear infinite;
      animation: spin 1.5s linear infinite;
    }

    @-webkit-keyframes spin {
      0% {
        -webkit-transform: rotate(0deg);
        -ms-transform: rotate(0deg);
        transform: rotate(0deg);
      }

      100% {
        -webkit-transform: rotate(360deg);
        -ms-transform: rotate(360deg);
        transform: rotate(360deg);
      }
    }

    @keyframes spin {
      0% {
        -webkit-transform: rotate(0deg);
        -ms-transform: rotate(0deg);
        transform: rotate(0deg);
      }

      100% {
        -webkit-transform: rotate(360deg);
        -ms-transform: rotate(360deg);
        transform: rotate(360deg);
      }
    }

    #loader-wrapper .loader-section {
      position: fixed;
      top: 0;
      width: 51%;
      height: 100%;
      background: #7171c6;
      z-index: 1000;
      -webkit-transform: translateX(0);
      -ms-transform: translateX(0);
      transform: translateX(0);
    }

    #loader-wrapper .loader-section.section-left {
      left: 0;
    }

    #loader-wrapper .loader-section.section-right {
      right: 0;
    }

    .loaded #loader-wrapper .loader-section.section-left {
      -webkit-transform: translateX(-100%);
      -ms-transform: translateX(-100%);
      transform: translateX(-100%);
      -webkit-transition: all 0.7s 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
      transition: all 0.7s 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
    }

    .loaded #loader-wrapper .loader-section.section-right {
      -webkit-transform: translateX(100%);
      -ms-transform: translateX(100%);
      transform: translateX(100%);
      -webkit-transition: all 0.7s 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
      transition: all 0.7s 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
    }

    .loaded #loader {
      opacity: 0;
      -webkit-transition: all 0.3s ease-out;
      transition: all 0.3s ease-out;
    }

    .loaded #loader-wrapper {
      visibility: hidden;
      -webkit-transform: translateY(-100%);
      -ms-transform: translateY(-100%);
      transform: translateY(-100%);
      -webkit-transition: all 0.3s 1s ease-out;
      transition: all 0.3s 1s ease-out;
    }

    .no-js #loader-wrapper {
      display: none;
    }

    .no-js h1 {
      color: #222222;
    }

    #loader-wrapper .load_title {
      font-family: 'Open Sans';
      color: #fff;
      font-size: 19px;
      width: 100%;
      text-align: center;
      z-index: 9999999999999;
      position: absolute;
      top: 60%;
      opacity: 1;
      line-height: 30px;
    }

    #loader-wrapper .load_title span {
      font-weight: normal;
      font-style: italic;
      font-size: 13px;
      color: #fff;
      opacity: 0.5;
    }
  </style>
</head>

<body>
  <div id="app">
    <div id="loader-wrapper">
      <div id="loader"></div>
      <div class="loader-section section-left"></div>
      <div class="loader-section section-right"></div>
      <div class="load_title">正在加载系统资源，请耐心等待</div>
    </div>
  </div>
  <script type="module" src="/src/main.ts"></script>
  <script>
    // 设置超时检测
    setTimeout(function () {
      // 如果5秒后页面仍在加载中，尝试恢复会话
      if (document.querySelector('#loader-wrapper')) {
        console.log('页面加载时间过长，尝试恢复会话...');

        // 获取URL参数或localStorage中的token
        const urlParams = new URLSearchParams(window.location.search);
        const accessToken = urlParams.get('accessToken') || localStorage.getItem('accessToken') || localStorage.getItem('pddToken');

        if (accessToken) {
          console.log('找到token，正在设置cookie并刷新页面...');

          // 设置多个cookie以确保兼容性
          document.cookie = `accessToken=${accessToken}; path=/; max-age=86400; secure`;
          document.cookie = `pddToken=${accessToken}; path=/; max-age=86400; secure`;
          document.cookie = `pati=${accessToken}; path=/; secure`;

          // 同时更新localStorage
          localStorage.setItem('accessToken', accessToken);
          localStorage.setItem('pddToken', accessToken);

          // 检查是否在注册页面
          if (window.location.pathname.includes('/register')) {
            console.log('当前在注册页面，正在处理PDD参数...');

            // 获取其他PDD参数
            const pddMallId = urlParams.get('pddMallId') || localStorage.getItem('pddMallId');
            const pddMallName = urlParams.get('pddMallName') || localStorage.getItem('pddMallName');
            const type = urlParams.get('type') || localStorage.getItem('pddType');
            const skuSpec = urlParams.get('skuSpec') || localStorage.getItem('skuSpec');

            // 保存到localStorage
            if (pddMallId) localStorage.setItem('pddMallId', pddMallId);
            if (pddMallName) {
              try {
                const decodedName = decodeURIComponent(pddMallName);
                localStorage.setItem('pddMallName', decodedName);
              } catch (e) {
                localStorage.setItem('pddMallName', pddMallName);
              }
            }
            if (type) localStorage.setItem('pddType', type);
            if (skuSpec) {
              try {
                const decodedSpec = decodeURIComponent(skuSpec);
                localStorage.setItem('skuSpec', decodedSpec);
              } catch (e) {
                localStorage.setItem('skuSpec', skuSpec);
              }
            }
          }

          // 刷新页面
          window.location.reload();
        } else {
          console.log('未找到token，尝试重定向到登录页...');

          // 如果不在登录或注册页面，重定向到登录页
          if (!window.location.pathname.includes('/login') && !window.location.pathname.includes('/register')) {
            window.location.href = '/login';
          } else {
            // 如果已经在登录或注册页面，只需刷新
            window.location.reload();
          }
        }
      }
    }, 5000);

    // 添加全局错误处理
    window.addEventListener('error', function (e) {
      console.error('捕获到页面错误:', e);

      // 记录错误并尝试恢复
      const accessToken = localStorage.getItem('accessToken') || localStorage.getItem('pddToken');
      if (accessToken) {
        document.cookie = `accessToken=${accessToken}; path=/; secure`;
        document.cookie = `pddToken=${accessToken}; path=/; secure`;
        console.log('尝试恢复会话...');
      }
    });
  </script>
</body>

</html>