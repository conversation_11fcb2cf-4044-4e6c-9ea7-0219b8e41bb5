import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { ShopDetailVO, ShopDetailForm, ShopDetailQuery } from '@/api/zhishu/shopDetail/types';

/**
 * 查询商品详情列表
 * @param query
 * @returns {*}
 */

export const listShopDetail = (query?: ShopDetailQuery): AxiosPromise<ShopDetailVO[]> => {
  return request({
    url: '/zhishu/shopDetail/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询商品详情详细
 * @param id
 */
export const getShopDetail = (id: string | number): AxiosPromise<ShopDetailVO> => {
  return request({
    url: '/zhishu/shopDetail/' + id,
    method: 'get'
  });
};

/**
 * 新增商品详情
 * @param data
 */
export const addShopDetail = (data: ShopDetailForm) => {
  return request({
    url: '/zhishu/shopDetail',
    method: 'post',
    data: data
  });
};

/**
 * 修改商品详情
 * @param data
 */
export const updateShopDetail = (data: ShopDetailForm) => {
  //换算重量和标准本数
  // data.bookWeight = parseFloat(data.bookWeight+"") * 100;
  // data.standardNumber = parseFloat(data.standardNumber+"") * 100;
  return request({
    url: '/zhishu/shopDetail',
    method: 'put',
    data: data
  });
};

/**
 * 删除商品详情
 * @param id
 */
export const delShopDetail = (id: string | number | Array<string | number>) => {
  return request({
    url: '/zhishu/shopDetail/' + id,
    method: 'delete'
  });
};
