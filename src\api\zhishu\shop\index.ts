import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { ShopVO, ShopForm, ShopQuery, saveArtNo, WlnShopList, EncrypData, ShopMap } from '@/api/zhishu/shop/types';
import { ShelvesVO } from '@/api/zhishu/shelves/types';
import { FreightVO } from '@/api/zhishu/freight/types';

/**
 * 查询店铺主表列表
 * @param query
 * @returns {*}
 */

export const listShop = (query?: ShopQuery): AxiosPromise<ShopVO[]> => {
  return request({
    url: '/zhishu/shop/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询店铺数据集合
 * @returns
 */
export const getListShop = () => {
  return request({
    url: '/zhishu/shop/getList',
    method: 'get'
  });
};

/**
 * 获取商品核价文件下载链接
 * @returns
 */
export const getVerifyPriceUrl = (shopId) => {
  return request({
    url: '/huidiao/pdd/getVerifyPriceUrl?shopId=' + shopId,
    method: 'get'
  });
};

/**
 * 查询店铺主表详细
 * @param id
 */
export const getShop = (id: string | number): AxiosPromise<ShopVO> => {
  return request({
    url: '/zhishu/shop/' + id,
    method: 'get'
  });
};

/**
 * 新增店铺主表
 * @param data
 */
export const addShop = (data: ShopForm) => {
  return request({
    url: '/zhishu/shop',
    method: 'post',
    data: data
  });
};

/**
 * 修改店铺主表
 * @param data
 */
export const updateShop = (data: ShopForm) => {
  return request({
    url: '/zhishu/shop',
    method: 'put',
    data: data
  });
};

/**
 * 删除店铺主表
 * @param id
 */
export const delShop = (id: string | number | Array<string | number>) => {
  return request({
    url: '/zhishu/shop/' + id,
    method: 'delete'
  });
};

// 前往拼多多授权页面
export function toPddGetCode(id) {
  return request({
    url: '/huidiao/pdd/toPddGetCode?id=' + id,
    method: 'get'
  });
}

var pageCode = '';
var pati = '';

async function getPageCodeAndPati(mallId) {
  try {
    // 注意：这里必须 await request
    pageCode = await request({
      url: '/huidiao/pdd/getPageCode/' + mallId,
      method: 'get'
    });

    await PDD_OPEN_init({
      code: pageCode
    });

    pati = await window.PDD_OPEN_getPati();
  } catch (e) {
    console.log(e);
  }
}

// 获取商品运费模板接口
export async function getLogisticsTemplate(shopId, mallId) {
  await getPageCodeAndPati(mallId);
  return request({
    url: '/huidiao/pdd/getLogisticsTemplate/' + shopId,
    method: 'get',
    headers: {
      'X-PDD-Pati': pati,
      'X-PDD-PageCode': pageCode
    }
  });
}

// 前往孔夫子授权页面
export function toKongfzGetCode(id) {
  return request({
    url: '/api/kongfz/toKongfzGetCode?id=' + id,
    method: 'get'
  });
}

export function synchronizationGoods(shopId: number | string, shopType: number) {
  return request({
    url: '/zhishu/shopGoods/synchronizationGoods',
    method: 'get',
    params: {
      shopId,
      shopType
    }
  });
}

export const artNoRuleSave = (data: saveArtNo) => {
  return request({
    url: '/zhishu/shopGoods/saveShopGoodsInDb',
    method: 'post',
    data: data
  });
};

export const getShe = (id: string | number): AxiosPromise<ShelvesVO> => {
  return request({
    url: '/shelves/shelves/sheNamelist/' + id,
    method: 'get'
  });
};

export const getFre = (id: string | number): AxiosPromise<FreightVO> => {
  return request({
    url: '/shelves/shelves/freNamelist/' + id,
    method: 'get'
  });
};

//获取万里牛店铺列表
export const getWlnShopList = (page: number, limit: number, shopId: number, shopNick: string): AxiosPromise<WlnShopList[]> => {
  return request({
    url: '/zhishu/shop/getWlnShopList',
    method: 'get',
    params: {
      // 关键修改：将参数放在params中
      page,
      limit,
      shopId,
      shopNick
    }
  });
};

//万里牛店铺授权
export const authorizationWln = async (shopId: number, shopName: string, shopNick: string): AxiosPromise<boolean> => {
  return request({
    url: '/zhishu/shop/authorizationWln',
    method: 'get',
    params: {
      shopId,
      shopName,
      shopNick
    }
  });
};


/**
 * 加密接口
 * @param data
 */
export const encrypUrlMenthod=async (data:EncrypData):AxiosPromise<ShopMap>=>{
  return request({
    url: '/zhishu/shop/encryption',
    method: 'post',
    data: data
  });
}
