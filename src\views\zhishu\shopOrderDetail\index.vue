<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="店铺id" prop="shopId">
              <el-input v-model="queryParams.shopId" placeholder="请输入店铺id" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="店铺名称" prop="shopName">
              <el-input v-model="queryParams.shopName" placeholder="请输入店铺名称" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="订单id" prop="orderId">
              <el-input v-model="queryParams.orderId" placeholder="请输入订单id" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="商品数量" prop="goodsCount">
              <el-input v-model="queryParams.goodsCount" placeholder="请输入商品数量" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="商品编码" prop="goodsId">
              <el-input v-model="queryParams.goodsId" placeholder="请输入商品编码" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="商品图片" prop="goodsImg">
              <el-input v-model="queryParams.goodsImg" placeholder="请输入商品图片" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="商品名称" prop="goodsName">
              <el-input v-model="queryParams.goodsName" placeholder="请输入商品名称" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="商品单件 单价：元" prop="goodsPrice">
              <el-input v-model="queryParams.goodsPrice" placeholder="请输入商品单件 单价：元" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="商品规格" prop="goodsSpec">
              <el-input v-model="queryParams.goodsSpec" placeholder="请输入商品规格" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="商品维度外部编码，注意：编辑商品后必须等待商品审核通过后方可生效，订单中商品信息为交易快照的商品信息" prop="outerGoodsId">
              <el-input v-model="queryParams.outerGoodsId" placeholder="请输入商品维度外部编码，注意：编辑商品后必须等待商品审核通过后方可生效，订单中商品信息为交易快照的商品信息" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="sku维度商家外部编码，注意：编辑商品后必须等待商品审核通过后方可生效，订单中商品信息为交易快照的商品信息" prop="outerId">
              <el-input v-model="queryParams.outerId" placeholder="请输入sku维度商家外部编码，注意：编辑商品后必须等待商品审核通过后方可生效，订单中商品信息为交易快照的商品信息" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="商品sku编码" prop="skuId">
              <el-input v-model="queryParams.skuId" placeholder="请输入商品sku编码" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['zhishu:shopOrderDetail:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['zhishu:shopOrderDetail:edit']">修改</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['zhishu:shopOrderDetail:remove']">删除</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['zhishu:shopOrderDetail:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="shopOrderDetailList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="" align="center" prop="id" v-if="true" />
        <el-table-column label="店铺id" align="center" prop="shopId" />
        <el-table-column label="店铺名称" align="center" prop="shopName" />
        <el-table-column label="订单id" align="center" prop="orderId" />
        <el-table-column label="商品数量" align="center" prop="goodsCount" />
        <el-table-column label="商品编码" align="center" prop="goodsId" />
        <el-table-column label="商品图片" align="center" prop="goodsImg" />
        <el-table-column label="商品名称" align="center" prop="goodsName" />
        <el-table-column label="商品单件 单价：元" align="center" prop="goodsPrice" />
        <el-table-column label="商品规格" align="center" prop="goodsSpec" />
        <el-table-column label="商品维度外部编码，注意：编辑商品后必须等待商品审核通过后方可生效，订单中商品信息为交易快照的商品信息" align="center" prop="outerGoodsId" />
        <el-table-column label="sku维度商家外部编码，注意：编辑商品后必须等待商品审核通过后方可生效，订单中商品信息为交易快照的商品信息" align="center" prop="outerId" />
        <el-table-column label="商品sku编码" align="center" prop="skuId" />
        <el-table-column label="店铺状态" align="center" prop="status" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['zhishu:shopOrderDetail:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['zhishu:shopOrderDetail:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改订单详情对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body>
      <el-form ref="shopOrderDetailFormRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="店铺id" prop="shopId">
          <el-input v-model="form.shopId" placeholder="请输入店铺id" />
        </el-form-item>
        <el-form-item label="店铺名称" prop="shopName">
          <el-input v-model="form.shopName" placeholder="请输入店铺名称" />
        </el-form-item>
        <el-form-item label="订单id" prop="orderId">
          <el-input v-model="form.orderId" placeholder="请输入订单id" />
        </el-form-item>
        <el-form-item label="商品数量" prop="goodsCount">
          <el-input v-model="form.goodsCount" placeholder="请输入商品数量" />
        </el-form-item>
        <el-form-item label="商品编码" prop="goodsId">
          <el-input v-model="form.goodsId" placeholder="请输入商品编码" />
        </el-form-item>
        <el-form-item label="商品图片" prop="goodsImg">
          <el-input v-model="form.goodsImg" placeholder="请输入商品图片" />
        </el-form-item>
        <el-form-item label="商品名称" prop="goodsName">
          <el-input v-model="form.goodsName" placeholder="请输入商品名称" />
        </el-form-item>
        <el-form-item label="商品单件 单价：元" prop="goodsPrice">
          <el-input v-model="form.goodsPrice" placeholder="请输入商品单件 单价：元" />
        </el-form-item>
        <el-form-item label="商品规格" prop="goodsSpec">
          <el-input v-model="form.goodsSpec" placeholder="请输入商品规格" />
        </el-form-item>
        <el-form-item label="商品维度外部编码，注意：编辑商品后必须等待商品审核通过后方可生效，订单中商品信息为交易快照的商品信息" prop="outerGoodsId">
          <el-input v-model="form.outerGoodsId" placeholder="请输入商品维度外部编码，注意：编辑商品后必须等待商品审核通过后方可生效，订单中商品信息为交易快照的商品信息" />
        </el-form-item>
        <el-form-item label="sku维度商家外部编码，注意：编辑商品后必须等待商品审核通过后方可生效，订单中商品信息为交易快照的商品信息" prop="outerId">
          <el-input v-model="form.outerId" placeholder="请输入sku维度商家外部编码，注意：编辑商品后必须等待商品审核通过后方可生效，订单中商品信息为交易快照的商品信息" />
        </el-form-item>
        <el-form-item label="商品sku编码" prop="skuId">
          <el-input v-model="form.skuId" placeholder="请输入商品sku编码" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="ShopOrderDetail" lang="ts">
import { listShopOrderDetail, getShopOrderDetail, delShopOrderDetail, addShopOrderDetail, updateShopOrderDetail } from '@/api/zhishu/shopOrderDetail';
import { ShopOrderDetailVO, ShopOrderDetailQuery, ShopOrderDetailForm } from '@/api/zhishu/shopOrderDetail/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const shopOrderDetailList = ref<ShopOrderDetailVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const shopOrderDetailFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: ShopOrderDetailForm = {
  id: undefined,
  shopId: undefined,
  shopName: undefined,
  orderId: undefined,
  goodsCount: undefined,
  goodsId: undefined,
  goodsImg: undefined,
  goodsName: undefined,
  goodsPrice: undefined,
  goodsSpec: undefined,
  outerGoodsId: undefined,
  outerId: undefined,
  skuId: undefined,
  status: undefined,
}
const data = reactive<PageData<ShopOrderDetailForm, ShopOrderDetailQuery>>({
  form: {...initFormData},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    shopId: undefined,
    shopName: undefined,
    orderId: undefined,
    goodsCount: undefined,
    goodsId: undefined,
    goodsImg: undefined,
    goodsName: undefined,
    goodsPrice: undefined,
    goodsSpec: undefined,
    outerGoodsId: undefined,
    outerId: undefined,
    skuId: undefined,
    status: undefined,
    params: {
    }
  },
  rules: {
    id: [
      { required: true, message: "不能为空", trigger: "blur" }
    ],
    shopId: [
      { required: true, message: "店铺id不能为空", trigger: "blur" }
    ],
    shopName: [
      { required: true, message: "店铺名称不能为空", trigger: "blur" }
    ],
    orderId: [
      { required: true, message: "订单id不能为空", trigger: "blur" }
    ],
    goodsCount: [
      { required: true, message: "商品数量不能为空", trigger: "blur" }
    ],
    goodsId: [
      { required: true, message: "商品编码不能为空", trigger: "blur" }
    ],
    goodsImg: [
      { required: true, message: "商品图片不能为空", trigger: "blur" }
    ],
    goodsName: [
      { required: true, message: "商品名称不能为空", trigger: "blur" }
    ],
    goodsPrice: [
      { required: true, message: "商品单件 单价：元不能为空", trigger: "blur" }
    ],
    goodsSpec: [
      { required: true, message: "商品规格不能为空", trigger: "blur" }
    ],
    outerGoodsId: [
      { required: true, message: "商品维度外部编码，注意：编辑商品后必须等待商品审核通过后方可生效，订单中商品信息为交易快照的商品信息不能为空", trigger: "blur" }
    ],
    outerId: [
      { required: true, message: "sku维度商家外部编码，注意：编辑商品后必须等待商品审核通过后方可生效，订单中商品信息为交易快照的商品信息不能为空", trigger: "blur" }
    ],
    skuId: [
      { required: true, message: "商品sku编码不能为空", trigger: "blur" }
    ],
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询订单详情列表 */
const getList = async () => {
  loading.value = true;
  const res = await listShopOrderDetail(queryParams.value);
  shopOrderDetailList.value = res.rows;
  total.value = res.total;
  loading.value = false;
}

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
}

/** 表单重置 */
const reset = () => {
  form.value = {...initFormData};
  shopOrderDetailFormRef.value?.resetFields();
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
}

/** 多选框选中数据 */
const handleSelectionChange = (selection: ShopOrderDetailVO[]) => {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = "添加订单详情";
}

/** 修改按钮操作 */
const handleUpdate = async (row?: ShopOrderDetailVO) => {
  reset();
  const _id = row?.id || ids.value[0]
  const res = await getShopOrderDetail(_id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = "修改订单详情";
}

/** 提交按钮 */
const submitForm = () => {
  shopOrderDetailFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateShopOrderDetail(form.value).finally(() =>  buttonLoading.value = false);
      } else {
        await addShopOrderDetail(form.value).finally(() =>  buttonLoading.value = false);
      }
      proxy?.$modal.msgSuccess("操作成功");
      dialog.visible = false;
      await getList();
    }
  });
}

/** 删除按钮操作 */
const handleDelete = async (row?: ShopOrderDetailVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除订单详情编号为"' + _ids + '"的数据项？').finally(() => loading.value = false);
  await delShopOrderDetail(_ids);
  proxy?.$modal.msgSuccess("删除成功");
  await getList();
}

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download('zhishu/shopOrderDetail/export', {
    ...queryParams.value
  }, `shopOrderDetail_${new Date().getTime()}.xlsx`)
}

onMounted(() => {
  getList();
});
</script>
