import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { TaskVO, TaskForm, TaskQuery } from '@/api/zhishu/task/types';
import exp from 'constants';

/**
 * 查询任务列表列表
 * @param query
 * @returns {*}
 */

export const listTask = (query?: TaskQuery): AxiosPromise<TaskVO[]> => {
  return request({
    url: '/zhishu/task/list',
    method: 'get',
    params: query
  });
};

export const logsTask = (id) =>{
  return request({
    url: '/zhishu/task/logsList',
    method: 'get',
    params: {
      "id":id
    }
  });
}

export const logsMsg = (id) =>{
  return request({
    url: '/zhishu/task/logsMsg',
    method: 'get',
    params: {
      "id":id
    }
  });
}

export const logsDetailTask = (data) => {
  return request({
    url: '/zhishu/task/logsDetailList/'+data.taskId+"/"+data.shopId,
    method: 'get'
  });
}

export const downloadLog = (fileName) => {
  return request({
    url: '/zhishu/task/downloadLogs/'+fileName,
    method: 'get'
  });
}

//校验文件是否上传
export const checkFile = () => {
  return request({
    url: '/zhishu/task/checkFile',
    method: 'get'
  });
}
//删除缓存文件
export const delFile = () => {
  return request({
    url: '/zhishu/task/delFile',
    method: 'get'
  });
}


/**
 * 暂停/恢复/中止接口
 */
export const editRunningTaskStatus = (taskId,status) => {
  return request({
    url: '/zhishu/task/editRunningTask',
    method: 'post',
    data: {
      "taskId":taskId,
      "status":status
    }
  });
}

/**
 * 暂停线程
 */
export const pauseThread = (threadId,id) =>{
  return request({
    url: '/zhishu/task/pauseThread/'+threadId+"/"+id,
    method: 'get'
  });
}

/**
 * 恢复线程
 */
export const continueThread = (threadId,id) =>{
  return request({
    url: '/zhishu/task/continueThread/'+threadId+"/"+id,
    method: 'get'
  });
}

/**
 * 查询任务列表详细
 * @param id
 */
export const getTask = (id: string | number): AxiosPromise<TaskVO> => {
  return request({
    url: '/zhishu/task/' + id,
    method: 'get'
  });
};

/**
 * 新增任务列表
 * @param data
 */
export const addTask = (data) => {
  return request({
    url: '/zhishu/task',
    method: 'post',
    data: data
  });
};

/**
 * 同步仓库
 */
export const tbStock = (data) => {
  return request({
    url: '/zhishu/task/tbStock',
    method: 'post',
    data: data
  });
}

/**
 * 修改任务列表
 * @param data
 */
export const updateTask = (data: TaskForm) => {
  return request({
    url: '/zhishu/task',
    method: 'put',
    data: data
  });
};

/**
 * 删除任务列表
 * @param id
 */
export const delTask = (id: string | number | Array<string | number>) => {
  return request({
    url: '/zhishu/task/' + id,
    method: 'delete'
  });
};


/**
 * 停止任务
 */
export const  stopTask = (taskId) => {
  return request({
    url: '/zhishu/task/stopTask',
    params:{
      "taskId":taskId
    },
    method: 'post'
  });
};

