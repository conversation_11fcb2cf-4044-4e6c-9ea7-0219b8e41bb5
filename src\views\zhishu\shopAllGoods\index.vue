<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter"
                :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="商品编号" prop="itemNumber">
              <el-input v-model="queryParams.itemNumber" placeholder="请输入商品编码" clearable
                        @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="商品名称" prop="goodsName">
              <el-input v-model="queryParams.goodsName" placeholder="请输入商品名称" clearable
                        @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="货号" prop="artNo">
              <el-input v-model="queryParams.artNo" placeholder="请输入货号" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="原始货号" prop="originalArtNo">
              <el-input v-model="queryParams.originalArtNo" placeholder="请输入原始货号" clearable
                        @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="ISBN" prop="isbn">
              <el-input v-model="queryParams.isbn" style="width: 300px" placeholder="请输入ISBN" clearable @keyup.enter="handleQuery">
                <template #append>
                  <el-select v-model="isIsbnChange"  clearable placeholder="有无ISBN" style="width: 110px" @change="handleLocationTypeChange">
                    <el-option v-for="item in isbnSelect" :key="item.value" :label="item.label" :value="item.value"/>
                  </el-select>
                </template>
              </el-input>
            </el-form-item>
            <el-form-item label="品相" prop="conditionCode">
<!--              <el-input v-model="queryParams.conditionCode" placeholder="请输入品相" clearable-->
<!--                        @keyup.enter="handleQuery" />-->
              <el-select
                v-model="queryParams.conditionCode"
                filterable
                placeholder="请选择品相"
                style="width: 240px"
              >
                <el-option
                  v-for="item in t_condition_code"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>

            <el-form-item label="货区名称" prop="depotId">
              <el-select v-model="selectedId" value-key="id" placeholder="请选择一级货区" :reserve-keyword="false"
                         :loading="loading" clearable filterable @update:model-value="handleDepotChange">
                <el-option v-for="item in depotList" :key="item.id" :label="item.name+item.unit" :value="item" />
              </el-select>
            </el-form-item>

            <el-form-item label="发货地" prop="address">
              <el-select v-model="deliveryId" value-key="id" placeholder="请选择发货地" :reserve-keyword="false"
                         clearable filterable :loading="loading" @update:model-value="handleDistChange">
                <el-option v-for="item in districtList" :key="item.id" :label="item.name" :value="item" />
              </el-select>
            </el-form-item>


            <el-form-item label="书图片" prop="bookPic">
              <el-select
                v-model="queryParams.IsBookPicNull"
                placeholder="请选择图片状态"
                clearable
                filterable
                @change="handleQuery"
              >
                <el-option
                  label="有图片"
                  value="1"
                  :title="'book_pic字段不为空的记录'"
                />
                <el-option
                  label="无图片"
                  value="0"
                  :title="'book_pic字段为空的记录'"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="商品售价" prop="price">
              <div class="condition-filter">
                <!-- 选择运算符 -->
                <el-select
                  v-model="Min1Price"
                  placeholder="运算符"
                  style="width: 100px; margin-right: 10px;"
                >
                  <el-option label="大于" value=">"></el-option>
                  <el-option label="大于等于" value=">="></el-option>
                  <el-option label="等于" value="="></el-option>
                  <el-option label="小于" value="<"></el-option>
                  <el-option label="小于等于" value="<="></el-option>
                </el-select>
                <!-- 输入数值 -->
                <el-input-number
                  v-model="Max1Price"
                  :min="0"
                  :max="10000000"
                  controls-position="right"
                ></el-input-number>
              </div>
            </el-form-item>
<!--            <el-form-item label="商品售价" prop="price">-->
<!--              <el-input-number-->
<!--                v-model="Min1Price"-->
<!--                :min="0"-->
<!--                :max="10000000"-->
<!--                placeholder="最小值"-->
<!--                controls-position="right"-->
<!--              ></el-input-number>-->
<!--              <span class="range-separator">至</span>-->
<!--              <el-input-number-->
<!--                v-model="Max1Price"-->
<!--                :min="0"-->
<!--                :max="10000000"-->
<!--                placeholder="最大值"-->
<!--                controls-position="right"-->
<!--              ></el-input-number>-->
<!--            </el-form-item>-->
            <el-form-item label="库存" prop="price">
              <div class="condition-filter">
                <!-- 选择运算符 -->
                <el-select
                  v-model="Min2Inventory"
                  placeholder="运算符"
                  style="width: 100px; margin-right: 10px;"
                >
                  <el-option label="大于" value=">"></el-option>
                  <el-option label="大于等于" value=">="></el-option>
                  <el-option label="等于" value="="></el-option>
                  <el-option label="小于" value="<"></el-option>
                  <el-option label="小于等于" value="<="></el-option>
                </el-select>
                <!-- 输入数值 -->
                <el-input-number
                  v-model="Max2Inventory"
                  :min="0"
                  :max="10000000"
                  controls-position="right"
                ></el-input-number>
              </div>
            </el-form-item>

            <!--            <el-form-item label="库存" prop="inventory">-->
<!--              <el-input-number-->
<!--                v-model="Min2Inventory"-->
<!--                :min="0"-->
<!--                :max="10000000"-->
<!--                placeholder="最小值"-->
<!--                controls-position="right"-->
<!--              ></el-input-number>-->
<!--              <span class="range-separator">至</span>-->
<!--              <el-input-number-->
<!--                v-model="Max2Inventory"-->
<!--                :min="0"-->
<!--                :max="10000000"-->
<!--                placeholder="最大值"-->
<!--                controls-position="right"-->
<!--              ></el-input-number>-->
<!--            </el-form-item>-->
            <!--            <el-form-item label="可见" prop="isQueryGoods">-->
            <!--              <el-select v-model="isQueryGoods"  value-key="id" :disabled="true" placeholder="请选择可见内容" :reserve-keyword="false"  clearable filterable :loading="loading" @change="ItCanBeSeenNR">-->
            <!--                <el-option v-for="item in is_query_all_goods" :key="item.value" :label="item.label"  :value="item.value"   />-->
            <!--              </el-select>-->
            <!--            </el-form-item>-->
<!--            <el-form-item label="加入分销" prop="isJoinDistribution">-->
<!--              <el-select v-model="queryParams.isJoinDistribution" value-key="id" placeholder="请选择是否加入分销"-->
<!--                         :reserve-keyword="false" clearable filterable :loading="loading">-->
<!--                <el-option v-for="item in is_join_distribution" :key="item.value" :label="item.label"-->
<!--                           :value="item.value" />-->
<!--              </el-select>-->
<!--            </el-form-item>-->

            <!--仅自已货区(默认)，全部-->

            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>


    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <!--          <el-col :span="1.5">-->
          <!--            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['zhishu:shopGoods:add']">新增</el-button>-->
          <!--          </el-col>-->
          <!--          <el-col :span="1.5">-->
          <!--            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['zhishu:shopGoods:edit']">修改</el-button>-->
          <!--          </el-col>-->
          <!--          <el-col :span="1.5">-->
          <!--            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['zhishu:shopGoods:remove']">删除</el-button>-->
          <!--          </el-col>-->
          <!--          <el-col :span="1.5">-->
          <!--            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['zhishu:shopGoods:export']">导出</el-button>-->
          <!--          </el-col>-->

          <!--   导出模板按钮-->
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Histogram" @click="handleDownload">导出模板</el-button>
          </el-col>
          <!--   导出模板按钮-->
          <!--   重写导入按钮-->
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Upload" @click="dialogVisible = true">导入</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Upload" @click="goodsAddBtn()" :disabled="multiple"
                       v-hasPermi="['zhishu:shopGoods:goodsAdd']">发布
            </el-button>
          </el-col>


          <el-upload
            ref="uploadRef"
            class="upload-demo"
            accept=".xlsx, .xls"
            :headers="headers"
            :action="`${uploadAction}?depotId=${selectedDepotId}`"
            :on-success="onSuccess"
            :on-error="onError"
            :limit="3"
            :data="uploadForm.data"
          >
          </el-upload>

          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>
      <el-table v-loading="loading" :data="shopGoodsList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <!--        <el-table-column label="Id" align="center" prop="id"  width="100" :show-overflow-tooltip="true">-->
        <!--          <template #default="{ row }">-->
        <!--            <div class="truncate-cell">  &lt;!&ndash; 文本截断容器 &ndash;&gt;-->
        <!--              {{ row.id }}-->
        <!--            </div>-->
        <!--          </template>-->
        <!--        </el-table-column>-->
        <el-table-column label="商品编号" align="left" prop="itemNumber" width="110" />
        <el-table-column label="缩略图" align="left" prop="goodsName" width="70" :show-overflow-tooltip="true">
          <template #default="{ row }">
            <el-image
              style="width: 30px;height: 30px"
              :src="row.bookPic"
              fit="scale-down"
              :preview-src-list="[currentPreviewImg]"
              preview-teleported
              @click="handlePreview(row.bookPic)"
            >
              <template #error>
                <div class="image-slot">
                  暂无
                </div>
              </template>
            </el-image>
          </template>
        </el-table-column>
        <el-table-column label="商品名称" align="left" prop="goodsName" width="270" :show-overflow-tooltip="true">
          <template #default="{ row }">
            <div class="truncate-cell">  <!-- 文本截断容器 -->
              {{ row.goodsName }}
            </div>
          </template>
        </el-table-column>

        <el-table-column label="货号(新货号前五位代表一\二\三区)" align="left" prop="artNo" width="250"
                         :show-overflow-tooltip="true">
          <template #default="{ row }">
            <div class="truncate-cell">  <!-- 文本截断容器 -->
              新货号:<span style="color: #b729e7">{{ row.artNo }}</span>
              <br>
              原货号:{{ row.originalArtNo }}
            </div>
          </template>
        </el-table-column>
        <!--        <el-table-column label="原始货号" align="left" prop="originalArtNo" width="200" :show-overflow-tooltip="true">-->
        <!--          <template #default="{ row }">-->
        <!--            <div class="truncate-cell">  &lt;!&ndash; 文本截断容器 &ndash;&gt;-->
        <!--              {{ row.originalArtNo }}-->
        <!--            </div>-->
        <!--          </template>-->
        <!--        </el-table-column>-->
        <el-table-column label="isbn" align="left" prop="isbn" width="130" />
        <el-table-column label="品相" align="left" prop="conditionCode" width="80">
          <template #default="{ row }">
            <span v-if="row.conditionCode==='A'">一品</span>
            <span v-else-if="row.conditionCode==='B'">二品</span>
            <span v-else-if="row.conditionCode==='C'">三品</span>
            <span v-else-if="row.conditionCode==='D'">四品</span>
            <span v-else-if="row.conditionCode==='E'">五品</span>
            <span v-else-if="row.conditionCode==='F'">六品</span>
            <span v-else-if="row.conditionCode==='G'">六五品</span>
            <span v-else-if="row.conditionCode==='H'">七品</span>
            <span v-else-if="row.conditionCode==='I'">七五品</span>
            <span v-else-if="row.conditionCode==='J'">八品</span>
            <span v-else-if="row.conditionCode==='K'">八五品</span>
            <span v-else-if="row.conditionCode==='L'">九品</span>
            <span v-else-if="row.conditionCode==='M'">九五品</span>
            <span v-else-if="row.conditionCode==='N'">全新</span>
          </template>
        </el-table-column>
        <el-table-column label="登录账号" align="left" prop="phoneNumberMark" width="110" />

        <el-table-column label="商品售价" align="left" prop="price" :show-overflow-tooltip="true" width="90">
          <template #default="{ row }">
            {{ ((row.price / 100).toFixed(2)) }}
          </template>
        </el-table-column>

        <el-table-column label="一级货区" align="left" prop="depotName" :show-overflow-tooltip="true" width="90" />

        <el-table-column label="发货地址" align="left" prop="deliveryAddress" :show-overflow-tooltip="true" width="190">
          <template #default="{ row }">
            <!--            <div class="flex gap-2">-->
            {{ row.deliveryAddress }}
            <el-tag type="danger" @click="shippingPop(row)">
              <span v-if="row.templateMinPrice!=null"> 首费:{{ row.templateMinPrice }}</span>
              <span v-else> 首费:0</span>
            </el-tag>
            <!--            </div>-->
          </template>
        </el-table-column>

        <el-table-column label="库存" align="left" prop="inventory" width="60" />

        <el-table-column label="入库时间" align="left" prop="createTime" :show-overflow-tooltip="true">
          <template #default="{ row }">
            <div class="truncate-cell">  <!-- 文本截断容器 -->
              {{ row.createTime }}
            </div>
          </template>
        </el-table-column>

        <!--        <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="100">-->
        <!--          <template #default="scope">-->
        <!--            <el-tooltip content="修改" placement="top">-->
        <!--              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['zhishu:shopGoods:edit']"></el-button>-->
        <!--            </el-tooltip>-->
        <!--            <el-tooltip content="删除" placement="top">-->
        <!--              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['zhishu:shopGoods:remove']"></el-button>-->
        <!--            </el-tooltip>-->
        <!--          </template>-->
        <!--        </el-table-column>-->
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
                  v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>


    <!-- 添加或修改商品信息对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="1400px" append-to-body>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form ref="shopGoodsFormRef" :model="form" :rules="rulesGoods" label-width="110px">
            <!-- 使用嵌套栅格系统 -->
            <el-row :gutter="10">
              <!-- 第一组字段 -->
              <el-col :span="12">
                <el-form-item label="一级货区名称" prop="depotIds" label-width="110">
                  <el-select v-model="selectedId" :disabled="!dialog.isAdd" value-key="id" placeholder="请选择一级货区"
                             :reserve-keyword="false" :get-option-key="getOptionKey" clearable filterable
                             style="width: 100%" :loading="loading" @update:model-value="handleDepotChange">
                    <el-option v-for="item in depotList" :key="item.id" :label="item.name+''+item.unit"
                               :disabled="!dialog.isAdd" :value="item" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="ISBN编码" prop="isbn">
                  <el-input v-model="form.isbn" placeholder="请输入内容ISBN编码" :disabled="!dialog.isAdd" />
                </el-form-item>
              </el-col>
              <!-- 第十组字段 -->
              <el-col :span="12">
                <el-form-item label="品相" prop="conditionCode">
                  <el-select v-model="conditionCodeId" :disabled="!dialog.isAdd" value-key="id" placeholder="请选择品相"
                             :reserve-keyword="false" :get-option-key="getOptionKey" clearable filterable
                             style="width: 100%" :loading="loading" @update:model-value="handleconditionCodeChange">
                    <el-option v-for="item in conditionCodeTypes" :key="item.value" :label="item.label"
                               :value="item.label" :disabled="!dialog.isAdd" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="标准售价" prop="price">
                  <el-input v-model="form.price" placeholder="请输入标准售价">
                    <template #append>元</template>
                  </el-input>
                </el-form-item>
              </el-col>
              <!-- 第二组字段 -->
              <el-col :span="12">
                <el-form-item label="包册数" prop="booksNumber">
                  <el-input v-model="form.booksNumber" placeholder="请输入包册数" :disabled="!dialog.isAdd">
                    <template #append>册</template>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="页数" prop="pages">
                  <el-input v-model="form.pages" placeholder="请输入页数" :disabled="!dialog.isAdd">
                    <template #append>页</template>
                  </el-input>
                </el-form-item>
              </el-col>
              <!-- 第三组字段 -->
              <el-col :span="12">
                <el-form-item label="是否是套餐" prop="isPackage">
                  <el-radio-group v-model="form.isPackage">
                    <el-radio :value="'0'" border size="large">套装</el-radio>
                    <el-radio :value="'1'" border size="large">非套装</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="商品定价" prop="fixPrice">
                  <el-input v-model="form.fixPrice" placeholder="请输入商品定价">
                    <template #append>元</template>
                  </el-input>
                </el-form-item>
              </el-col>
              <!-- 第四组字段 -->
              <el-col :span="12">
                <el-form-item label="出版社名称" prop="publicationName">
                  <el-input v-model="form.publicationName" placeholder="请输入出版社名称" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="出版时间" prop="publicationTime">
                  <el-date-picker
                    v-model="form.publicationTime"
                    type="datetime"
                    placeholder="选择日期时间"
                    format="YYYY-MM-DD HH:mm:ss"
                    value-format="YYYY-MM-DD HH:mm:ss"
                  />
                </el-form-item>
              </el-col>
              <!-- 第五组字段 -->
              <el-col :span="12">
                <el-form-item label="作者" prop="author">
                  <el-input v-model="form.author" placeholder="请输入作者" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="地区" prop="region">
                  <el-input v-model="form.region" placeholder="请输入地区" />
                </el-form-item>
              </el-col>
              <!-- 第六组字段 -->
              <el-col :span="12">
                <el-form-item label="译者" prop="translator">
                  <el-input v-model="form.translator" placeholder="请输入译者" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="编者" prop="editor">
                  <el-input v-model="form.editor" placeholder="请输入编者" />
                </el-form-item>
              </el-col>
              <!-- 第七组字段 -->
              <el-col :span="12">
                <el-form-item label="书籍开本" prop="booklets">
                  <el-input v-model="form.booklets" placeholder="请输入书籍开本" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="字数" prop="wordCount">
                  <el-input v-model="form.wordCount" placeholder="请输入字数" />
                </el-form-item>
              </el-col>
              <!-- 第八组字段 -->
              <el-col :span="12">
                <el-form-item label="装帧类型" prop="bindType">
                  <el-input v-model="form.bindType" placeholder="请输入装帧类型" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="包装方式" prop="packing" label-width="88">
                  <el-radio-group v-model="form.packing">
                    <el-radio :value="'0'" border size="large">礼盒装</el-radio>
                    <el-radio :value="'1'" border size="large">普通包装</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
              <!-- 第九组字段 -->
              <el-col :span="12">
                <el-form-item label="商品编号" prop="itemNumber">
                  <el-input v-model="form.itemNumber" placeholder="请输入商品编号" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="产品编码" prop="productId">
                  <el-input v-model="form.productId" placeholder="请输入产品编码" />
                </el-form-item>
              </el-col>
              <!-- 第十一组字段 -->
              <el-col :span="12">
                <el-form-item label="库存" prop="stock">
                  <el-input v-model="form.inventory" placeholder="请输入" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="备注" prop="remark">
                  <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-col>

        <!-- 右侧图片上传区域 -->
        <el-col :span="12">
          <el-card shadow="hover" header="商品图片上传">
            <el-upload
              list-type="picture-card"
              :limit="10"
              :file-list="[]"
              :action="uploadiMage"
              accept=".jpg,.png"
              :on-success="handleSuccess"
              :before-upload="beforeUpload"
            >
              <el-icon>
                <Plus />
              </el-icon>
              <div>点击上传</div>
            </el-upload>
            <!-- 独立展示区（可选） -->
            <!--            image-preview-->
            <div class="dual-column-preview">
              <h4>已上传图片（共 {{ fileList.length }} 张）：</h4>
              <div class="image-grid" v-if="fileList.length">
                <div v-for="(file, index) in fileList" :key="file.uid" class="grid-item">
                  <el-image
                    :src="file.url"
                    fit="cover"
                    :preview-src-list="previewList"
                    :initial-index="index">
                    <template #placeholder>
                      <div class="image-loading">加载中...</div>
                    </template>
                  </el-image>
                  <div class="meta-info">
                    <span class="file-name">{{ file.name }}</span>
                    <el-button
                      size="mini"
                      type="danger"
                      @click="removeFile(index)"
                      circle>
                      <el-icon>
                        <Delete />
                      </el-icon>
                    </el-button>
                  </div>
                </div>
              </div>
              <div v-else class="empty-tip">
                <el-empty description="暂无待上传图片" :image-size="80" />
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!--    导入弹窗事件-->
    <el-dialog v-model="dialogVisible"
               title="选择货区"
               width="30%"
               @close="dialogVisible=false">
      <el-form ref="shelvesFormRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="一级货区名称" prop="depotId" label-width="100">
          <el-select v-model="selectedId"
                     value-key="id" placeholder="请选择一级货区"
                     :reserve-keyword="false"
                     :get-option-key="getOptionKey"
                     clearable filterable style="width: 100%"
                     :loading="loading">
            <el-option v-for="item in depotList" :key="item.id" :label="item.name+''+item.unit" :value="item" />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>

        <el-button @click="cancel">取消</el-button>
        <el-button type="primary" @click="triggerFileSelect">确定</el-button>

      </template>

    </el-dialog>

    <el-dialog :title="dialog.shopChooseTitle" v-model="dialog.shopChoose" width="600px" append-to-body>
<!--      <el-form ref="taskFormRef" :model="formShops" :rules="rulesRelease" label-width="80px" style="height:100px">-->
<!--        <el-form-item label="店铺选择:" prop="shopIds" label-width="100px" style="width: 100%">-->
<!--          <el-checkbox-group v-model="formShops.shopIds">-->
<!--            <el-checkbox v-for="item in shopList" :key="item.value" :label="item.value">-->
<!--              {{ item.label }}-->
<!--            </el-checkbox>-->
<!--          </el-checkbox-group>-->
<!--        </el-form-item>-->
<!--      </el-form>-->
      <el-form ref="taskFormRef" :model="formShops"  label-width="90px" style="height:100px">
        <el-form-item label="孔夫子:" prop="shopIds" label-width="100px" style="width: 100%">
          <!-- 孔夫子（整行） -->
          <el-checkbox-group v-model="formShops.shopIds" style="flex: 1;">
            <el-checkbox
              v-for="item in kfzShopList"
              :key="item.value"
              :label="item.value"
              style="margin-right: 15px;"
            >
              {{ item.label }}
            </el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="拼多多:" prop="shopIds" label-width="100px" style="width: 100%">
          <!-- 拼多多（整行） -->
          <el-checkbox-group v-model="formShops.shopIds" style="flex: 1;">
            <el-checkbox
              v-for="item in pddShopList"
              :key="item.value"
              :label="item.value"
              style="margin-right: 15px;"
            >
              {{ item.label }}
            </el-checkbox>
          </el-checkbox-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitFormToShopChoose">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
    <!--    导入弹窗事件-->


    <!--    物流模版-->
    <el-dialog title="运费模板展示" v-model="freightDialog.visible" width="1000px" append-to-body>
      <div class="freight-template-container">
        <el-form :model="form.value" label-width="80px" disabled>
          <el-form-item label="计价方式">
            <el-radio-group v-model="PricingMethod">
              <el-radio :value="'weight'" border size="large">按重量</el-radio>
              <el-radio :value="'book'" border size="large">按标准本数（图书专用）</el-radio>
              <el-radio :value="'piece'" border size="large">按件数</el-radio>
              <el-radio :value="'custom'" border size="large">单独设置运费</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item>
            <el-table :data="deliveryRanges" style="width: 100%" border>
              <el-table-column label="首费（元）" width="120">
                <template #default="{ row }">
                  <el-input v-model="row.firstFee" type="number" placeholder="请输入首费" />
                </template>
              </el-table-column>
              <el-table-column label="运送范围" width="780">
                <template #default="{ row }">
                  {{ row.region }}
                </template>
              </el-table-column>
            </el-table>
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <!--          <el-button :loading="buttonLoading" type="primary" @click="saveFreightTemplate">确定</el-button>-->
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>


  </div>
</template>

<script setup name="ShopGoods" lang="ts">
import {
  listShopGoods,
  getShopGoods,
  delShopGoods,
  addShopGoods,
  updateShopGoods,
  addGoods
} from '@/api/zhishu/shopGoods';
import { getListShop } from '@/api/zhishu/shop';
import { ShopGoodsVO, ShopGoodsQuery, ShopGoodsForm } from '@/api/zhishu/shopGoods/types';
import { globalHeaders } from '@/utils/request';
import { DepotVO } from '@/api/zhishu/depot/types';
import { depotNameList } from '@/api/zhishu/shelves';
import { ElMessage } from "element-plus";
import { getPatiAndPageCode } from '@/api/zhishu/pdd';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const shopGoodsList = ref<ShopGoodsVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const headers = ref(globalHeaders());

const queryFormRef = ref<ElFormInstance>();
const shopGoodsFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: '',
  isAdd: true
});

// 模拟运送范围数据
const deliveryRanges = ref([]);
// 运费模板对话框状态
const freightDialog = reactive({
  visible: false,
  title: '运费模板设置'
});

// 添加弹窗控制属性
const dialogVisible = ref(false);
const shelvesFormRef = ref<ElFormInstance>();

interface DepotOption {
  id: string;
  name: string;
}

const selectedId = shallowRef<DepotOption | null>(null);// 严格匹配选项类型

const depotList = ref<DepotVO[]>([]);

const selectedDepotId = ref(null);

const baseUrl = import.meta.env.VITE_APP_BASE_API;

const uploadiMage = ref(baseUrl + '/zhishu/shopGoods/image');
const uploadAction = ref(baseUrl + '/zhishu/shopGoods/importExcel');

const currentPreviewImg = ref(''); // 仅存储当前点击的图片

// 最小商品售价
const Min1Price = ref();
// 最大商品售价
const Max1Price = ref();
// 最小库存
const Min2Inventory = ref();
// 最大库存
const Max2Inventory = ref();


// 运送方式
const PricingMethod = ref('');

// 运送方式弹窗
const shippingPop = async (row: DepotVO) => {
  // 根据模版id查询数据
  // console.log(row.templateId)
  if (row.templateId != null) {
    const res = await getFreInfo(row.templateId);
    // 数字编码对应运送方式
    const pricingMethodMap = {
      0: 'weight',
      1: 'book',
      2: 'piece',
      3: 'custom'
    };
    PricingMethod.value = pricingMethodMap[res.pricingMethod];
    // 获取运送范围
    if (res.shippingRange != null) {
      const parsedData = JSON.parse(res.shippingRange);
      deliveryRanges.value = transformData(parsedData);
    } else {
    }
    freightDialog.visible = true;
  } else {
    ElMessage.warning('该商品无运费模版');
  }
};

const transformData = (dataRange) => {
  const result = [];
  for (const region in dataRange) {
    const [firstWeight, firstFee, continueWeight, continueFee] = dataRange[region];
    result.push({
      region,
      firstWeight: parseFloat(firstWeight) || 1.0,
      firstFee,
      continueWeight: parseFloat(continueWeight) || 1.0,
      continueFee
    });
  }
  return result;
};
const { is_query_all_goods, is_join_distribution,t_condition_code }
  = toRefs<any>(proxy?.useDict('is_query_all_goods', 'is_join_distribution','t_condition_code'));


// 商品售价条件判断
const chageNumber = async () => {
  if(Max1Price.value!=null){
    if(Min1Price.value==null){
      ElMessage.warning('请选择商品售价运算符')
    }
  }
};
// 库存条件判断
const handleNumber = async () => {
  if(Max2Inventory.value!=null){
    if(Min2Inventory.value==null){
      ElMessage.warning('请选择库存运算符')
    }
  }
};


// 点击事件处理
const handlePreview = (imgUrl) => {
  currentPreviewImg.value = imgUrl;
};

// 添加弹窗控制属性

// 修复显示问题的关键配置
const getOptionKey = (item: DepotVO) => item.id;// 精确匹配逻辑
// 修复显示问题的关键配置

// 获取el-upload引用
const uploadRef = ref(null);
// 获取el-upload引用

// 用户点击确定触发导入事件
const triggerFileSelect = () => {
  dialogVisible.value = false;
  if (selectedId.value) {
    selectedDepotId.value = selectedId.value.id;
    uploadForm.data = selectedDepotId.value;
  }
  uploadRef.value.$el.querySelector('input[type = file]').click();
};
// 用户点击确定触发导入事件

//仓库数据加载
const loadData = async () => {
  loading.value = true;
  try {
    const res = await depotNameListAll();
    depotList.value = res.rows;
    // return res.rows  || []
  } catch (error) {
  } finally {
    loading.value = false;
  }
};
const depotid = ref(0);

const token = localStorage.getItem('token');


// 图片上传前校验
const beforeUpload = (file) => {
  const isImage = ['image/jpeg', 'image/png', 'image/webp'].includes(file.type);
  const isLt50M = file.size / 1024 / 1024 < 50;

  if (!isImage) ElMessage.error(' 仅支持JPEG/PNG/WEBP格式');
  if (!isLt50M) ElMessage.error(' 图片大小不能超过50MB');

  return isImage && isLt50M;
};
// 成功回调（含CDN地址处理）
const handleSuccess = (res, file) => {
  fileList.value.push(res.data);
  // 转换为逗号分隔字符串（兼容旧系统）
  const imageUrls = fileList.value
    .map(file => file.url)
    .filter(url => url) // 过滤空值
    .join(',');
  form.value.bookPic = imageUrls;
};

// 新增商品信息规则
const rulesGoods = reactive({
    depotIds: [
      {
        required: true,
        message: '请选择一级货区',
        trigger: ['blur', 'change'],
        validator: (rule, value, callback) => {
          depotid.value = Number(selectedId.value.id);
          console.log('仓库id:', depotid.value);
          if (depotid.value == 0) {
            callback(new Error('depotid'));
          } else {
            callback();
          }
        }
      }
    ],
    isbn: [
      {
        required: true,
        len: 13,
        message: 'ISBN必须为13位数字',
        trigger: 'blur'

      },
      {
        pattern: /^[0-9]{13}$/,
        message: '请输入有效的13位ISBN数字',
        trigger: 'blur'
      }
    ],
    conditionCode: [
      { required: true, message: '品相不能为空', trigger: 'blur' }
    ]
  }
);

const conditionCodeId = ref<string>(null); // 严格匹配选项类型


// 类型选项配置
const conditionCodeTypes = [
  { value: '1', label: '一品' },
  { value: '2', label: '二品' },
  { value: '3', label: '三品' },
  { value: '4', label: '四品' },
  { value: '5', label: '五品' },
  { value: '6', label: '六品' },
  { value: '6.5', label: '六五品' },
  { value: '7', label: '七品' },
  { value: '7.5', label: '七五品' },
  { value: '8', label: '八品' },
  { value: '8.5', label: '八五品' },
  { value: '9', label: '九品' },
  { value: '9.5', label: '九五品' },
  { value: '10', label: '全新' }

];

const handleconditionCodeChange = async (val) => {
  form.value.conditionCode = val;
};

const publicationTime = ref('2025-04-26 15:30:00'); // 默认当前时间

// 新增图片处理
import { ref, computed } from 'vue';
import { Plus } from '@element-plus/icons-vue';
import { getFreInfo, getProvinces } from '@/api/zhishu/district';
import { depotNameListAll } from '@/api/zhishu/depot';
import { TDistrictVo } from '@/api/zhishu/district/types';

interface UploadFile {
  uid: string;
  name: string;
  url: string;
  raw?: File;
}

const fileList = ref<UploadFile[]>([]);

// 生成预览大图列表
const previewList = computed(() => {
  return fileList.value.map(file => file.url);
});
const removeFile = (index) => {
  // 基础删除逻辑
  fileList.value.splice(index, 1);

  // 扩展功能：释放Blob内存（如果使用URL.createObjectURL ）
  if (fileList.value[index]?.url?.startsWith('blob:')) {
    URL.revokeObjectURL(fileList.value[index].url);
  }

  // 可选：显示删除反馈
  ElMessage.success(` 已删除第 ${index + 1} 张图片`);
};

const isQueryGoods = ref('全部');

// 是否查询全部信息事件
const ItCanBeSeenNR = async (val) => {
  console.log(val);
  queryParams.value.isQueryAllGoods = val;
};


// 仓库数据加载

//回传参数
const uploadForm = reactive({
  data: selectedId.value
});
//回传参数

const initFormData: ShopGoodsForm = {
  id: undefined,
  itemNumber: undefined,
  goodsName: undefined,
  depotId: undefined,
  isbn: undefined,
  conditionCode: undefined,
  artNo: undefined,
  price: undefined,
  unit: undefined,
  remark: undefined,
  productId: undefined,
  inventory: undefined,
  bookPic: undefined,
  booksNumber: undefined,
  pages: undefined,
  isPackage: undefined,
  publicationName: undefined,
  publicationTime: undefined,
  author: undefined,
  region: undefined,
  translator: undefined,
  editor: undefined,
  booklets: undefined,
  wordCount: undefined,
  bindType: undefined,
  packing: undefined,
  IsBookPicNull: undefined,
  originalArtNo: undefined,
  minprice: undefined,
  maxprice: undefined,
  mininventory: undefined,
  maxinventory: undefined,
  deliveryAddress: undefined,
  templateId: undefined,
  pricingMethod: undefined,
  templateMinPrice: undefined,
  isQueryAllGoods: undefined,
  phoneNumberMark: undefined
};
const data = reactive<PageData<ShopGoodsForm, ShopGoodsQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    goodsNo: undefined,
    goodsName: undefined,
    artNo: undefined,
    unit: undefined,
    productId: undefined,
    bookPic: undefined,
    IsBookPicNull: undefined,
    booksNumber: undefined,
    pages: undefined,
    isPackage: undefined,
    publicationName: undefined,
    publicationTime: undefined,
    author: undefined,
    region: undefined,
    translator: undefined,
    editor: undefined,
    booklets: undefined,
    wordCount: undefined,
    bindType: undefined,
    packing: undefined,
    originalArtNo: undefined,
    minprice: undefined,
    maxprice: undefined,
    mininventory: undefined,
    maxinventory: undefined,
    deliveryAddress: undefined,
    templateId: undefined,
    pricingMethod: undefined,
    templateMinPrice: undefined,
    isQueryAllGoods: undefined,
    isJoinDistribution: undefined,
    depotId: undefined,
    address: undefined,
    isIsbnChange:undefined,
    params: {}
  },
  rules: {
    id: [
      { required: true, message: 'id不能为空', trigger: 'blur' }
    ]
  }
});

const { queryParams, form, rules } = toRefs(data);

const initFormShops = ref({
  shopIds: undefined
});

const formShops = ref({
  shopIds: undefined
});

const isIsbnChange=ref();

const isbnSelect=[
  { value: '0', label: '无' },
  { value: '1', label: '有' }
];

const handleLocationTypeChange=(values) => {
  queryParams.value.isExistIsbn=values;
}




const handleDepotChange = (val: DepotOption | null) => {
  queryParams.value.depotId = val?.id || null;
};


interface DepotOption {
  id: string;
  name: string;
}

const deliveryId = shallowRef<DepotOption | null>(null);// 严格匹配选项类型


const handleDistChange = (val: DepotOption | null) => {
  queryParams.value.address = val?.name || null;
};


/** 查询商品信息列表 */
const getList = async () => {
  loading.value = true;
  // 搜索框中标准售价的限制条件
  // if (Max1Price.value !== null && Max1Price.value !== '') {
    queryParams.value.minprice = Min1Price.value;
    queryParams.value.maxprice = Max1Price.value;
  // }
  // 搜索框中库存的限制条件
  // if (Max2Inventory.value !== null && Max2Inventory.value !== '') {
    queryParams.value.mininventory = Min2Inventory.value;
    queryParams.value.maxinventory = Max2Inventory.value;
  // }
  // 当查询时，默认为1
  if (queryParams.value.isQueryAllGoods == null) {
    queryParams.value.isQueryAllGoods = 2;
  }
  const res = await listShopGoods(queryParams.value);

  shopGoodsList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
  dialogVisible.value = false;
  dialog.shopChoose = false;
  freightDialog.visible = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  shopGoodsFormRef.value?.resetFields();
  //选择仓库表单重置
  shelvesFormRef.value?.resetFields();
  selectedId.value = null;
  //选择仓库表单重置
};

/** 搜索按钮操作 */
const handleQuery = () => {
  chageNumber();
  handleNumber();
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  Min1Price.value = null;
  Max1Price.value = null;
  Min2Inventory.value = null;
  Max2Inventory.value = null;
  queryParams.value.isJoinDistribution = null;
  queryParams.value.isQueryAllGoods = 2;
  queryParams.value.IsBookPicNull = null;
  queryParams.value.depotId = null;
  selectedId.value = null;
  deliveryId.value = null;
  queryParams.value.address = null;
  queryParams.value.minprice = null;
  queryParams.value.maxprice = null;
  queryParams.value.mininventory = null;
  queryParams.value.maxinventory = null;
  queryParams.value.isExistIsbn=null;
  isIsbnChange.value=null;
  isQueryGoods.value = '全部';
  handleQuery();
};

//选中的商品信息列表
let goodsAddBookList = [];
/** 多选框选中数据 */
const handleSelectionChange = (selection: ShopGoodsVO[]) => {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
  //记录选中商品信息
  goodsAddBookList = selection.map(item => [item.id,item.isbn,item.goodsName,item.price,item.inventory,item.conditionCode,item.artNo,item.templateMinPrice]);
};

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.isAdd = true;
  dialog.title = '添加商品信息';
};

/** 修改按钮操作 */
const handleUpdate = async (row?: ShopGoodsVO) => {
  reset();
  const _id = row?.id || ids.value[0];
  const res = await getShopGoods(_id);
  console.log(res);
  // 回传参数
  selectedId.value = {
    name: res.data.depotName,
    id: res.data.depotId
  };
  form.value.fixPrice = res.data.fixPrice;
  form.value.productId = res.data.productId;//产品编码
  form.value.inventory = res.data.inventory;
  form.value.itemNumber = res.data.itemNumber;
  form.value.goodsName = res.data.goodsName;
  form.value.price = res.data.price;
  form.value.remark = res.data.remark;
  form.value.isbn = res.data.isbn;
  dialog.visible = true;
  conditionCodeId.value = res.data.conditionCode;
  form.value.conditionCode = res.data.conditionCode;
  form.value.id = res.data.id;
  dialog.isAdd = false;
  dialog.title = '修改商品信息';
};


/** 提交按钮 */
const submitForm = () => {
  shopGoodsFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      console.log(form.value.id);
      if (form.value.id) {
        await updateShopGoods(form.value).finally(() => buttonLoading.value = false);
      } else {
        console.log(form.value);
        await addShopGoods(form.value).finally(() => buttonLoading.value = false);
        fileList.value = [];
      }
      proxy?.$modal.msgSuccess('操作成功');
      dialog.visible = false;
      await getList();
    }
  });
};

/** 发布商品提交按钮 */
const submitFormToShopChoose = async () => {
  const headers = getPatiAndPageCode();
  console.log(headers,"===================");

  if(formShops.value.shopIds == undefined){
    proxy?.$modal.msgError('请选择店铺');
    return false;
  }
  buttonLoading.value = true;
  //封装数据
  const data = {
    'bookList': goodsAddBookList,
    'shopIds': formShops.value.shopIds
  };
  await addGoods(data);
  proxy?.$modal.msgSuccess('操作成功');
  dialog.shopChoose = false;
  buttonLoading.value = false;
};

/** 删除按钮操作 */
const handleDelete = async (row?: ShopGoodsVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除商品信息编号为"' + _ids + '"的数据项？').finally(() => loading.value = false);
  await delShopGoods(_ids);
  proxy?.$modal.msgSuccess('删除成功');
  await getList();
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download('zhishu/shopGoods/export', {
    ...queryParams.value
  }, `shopGoods_${new Date().getTime()}.xlsx`);
};

//导出模板事件
/** 导出模板按钮操作 */
const handleDownload = () => {
  ElMessageBox.confirm('确定要下载模板文件吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'info'
  }).then(() => {
    // 创建模板数据
    downloadTemplate();
    ElMessage.success('模板下载成功');
  }).catch(() => {
    // 取消下载
    ElMessage.info('已取消下载');
  });
};

const downloadTemplate = () => {
  //模板放置路径public/templates/*.xlsx
  const fileUrl = '/templates/GoodsInfoTemplate.xlsx';
  const link = document.createElement('a');
  link.href = fileUrl;
  link.download = '商品信息模板.xlsx'; // 设置下载文件名
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};
//导出模板事件

/** 导入成功 */
const onSuccess = (response) => {
  loading.value = false;
  if (response.code === 200) {
    ElMessage.success('导入成功');
    //刷新列表
    getList();
    reset();
  } else {
    ElMessage.error(response.message || '导入失败');
  }
};

/** 发布商品 */
const goodsAddBtn = () => {
  formShops.value = { ...initFormShops };
  dialog.shopChoose = true;
  dialog.shopChooseTitle = '选择发布商品的店铺';
};

/** 导入失败 */
const onError = (err) => {
  loading.value = false;
  ElMessage.error('导入失败: ' + (err.message || '服务器错误'));
  reset();
};

/** 获取店铺信息 */
const shopList = [];
const pddShopList=[];
const kfzShopList=[];
const getShopList = async () => {
  const res = await getListShop();
  console.log(res)
  if (res.length == 0) {
    shopList.length = 0;
  } else {
    for (var i = 0; i < res.length; i++) {
      // shopList.push({
      //   value: res[i].id,
      //   label: res[i].shopName,
      //    type:res[i].shopType
      // });
      if(res[i].shopType==="1"){
        pddShopList.push(
          {
            value: res[i].id,
            label: res[i].shopName,
          }
        )
      }else if(res[i].shopType==="2"){
        kfzShopList.push(
          {
            value: res[i].id,
            label: res[i].shopName,
          }
        )
      }
    }
  }
}


// /** 获取店铺信息 */
// const shopList = [];
// const getShopList = async () => {
//   const res = await getListShop();
//   if (res.length == 0) {
//     shopList.length = 0;
//   } else {
//     for (var i = 0; i < res.length; i++) {
//       shopList.push({
//         value: res[i].id,
//         label: res[i].shopName
//       });
//     }
//   }
// }

interface DepotOption {
  id: string;
  name: string;
}

const districtId = shallowRef<DepotOption | null>(null);// 严格匹配选项类型

const districtList = ref<TDistrictVo[]>([]);


const getDistrict = async () => {
  const res = await getProvinces();
  console.log(res);
  districtList.value = res.data;
  console.log(districtList.value);
};

onMounted(() => {
  getList();
  loadData();
  getDistrict();
  getShopList();
});
</script>

<style>
/* 文本溢出处理 */
.truncate-cell {
  width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.dual-column-preview {
  margin-top: 20px;
  padding: 12px;
  background: #0a0a0a;
  border-radius: 4px;
}

.image-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(110px, 1fr));
  gap: 16px;
}

.grid-item {
  position: relative;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  overflow: hidden;
  transition: all 0.3s;
}

.grid-item:hover {
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.grid-item :deep(.el-image) {
  width: 100%;
  height: 140px;
  display: block;
}

.meta-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px;
  background: white;
}

.file-name {
  flex: 1;
  font-size: 12px;
  overflow: hidden;
  color: #0a0a0a;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.image-loading {
  @apply w-full h-full flex items-center justify-center text-sm text-gray-400;
}

.image-slot {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  background: var(--el-fill-color-light);
  color: var(--el-text-color-secondary);
  font-size: 10px;
}
</style>
