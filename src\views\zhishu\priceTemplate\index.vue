<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter"
                :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="价格模板名称" prop="templateName">
              <el-input v-model="queryParams.templateName" placeholder="请输入价格模板名称" clearable
                        @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="增加比例" prop="proportion">
              <el-input v-model="queryParams.proportion" placeholder="请输入增加比例" clearable
                        @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="增加金额" prop="addAmount">
              <el-input v-model="queryParams.addAmount" placeholder="请输入增加金额" clearable
                        @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['zhishu:priceTemplate:add']">
              新增
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()"
                       v-hasPermi="['zhishu:priceTemplate:edit']"
            >修改
            </el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()"
                       v-hasPermi="['zhishu:priceTemplate:remove']"
            >删除
            </el-button
            >
          </el-col>
          <!-- <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['zhishu:priceTemplate:export']">导出</el-button>
          </el-col> -->
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="priceTemplateList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="主键" align="center" prop="id" v-if="true" />
        <el-table-column label="价格模板名称" align="center" prop="templateName" />
        <el-table-column label="价格类型" align="center" prop="priceType">
          <template #default="scope">
            <dict-tag :options="t_price_type" :value="scope.row.priceType" />
          </template>
        </el-table-column>
        <!--                <el-table-column label="比例(%)" align="center" prop="proportion" />-->
<!--        <el-table-column label="金额(单位:元)" align="center" prop="addAmount" />-->
        <!-- <el-table-column label="价格模板状态" align="center" prop="status" /> -->
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)"
                         v-hasPermi="['zhishu:priceTemplate:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)"
                         v-hasPermi="['zhishu:priceTemplate:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
                  v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>


    <!-- 添加或修改价格模板对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" @close="resetFormData" width="980px" append-to-body>
      <el-form ref="priceTemplateFormRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="模板名称" prop="templateName" label-width="100px">
          <el-input v-model="form.templateName" style="width: 300px" placeholder="请输入模板名称" />
        </el-form-item>
        <div style="margin: -18px 0px 14px 101px; color: #999">填写模板名称，之后将会在店铺设置中选择此处的价格模板</div>
        <el-form-item label="价格类型" prop="status" label-width="100px">
          <el-radio-group v-model="form.priceType">
            <el-radio v-for="dict in t_price_type" :key="dict.value" :value="dict.value">{{ dict.label }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <div style="margin: -18px 0px 14px 101px; color: #999">书价就是库存里的商品价格</div>
        <div style="margin: -18px 0px 14px 101px; color: #999">总价就是书价+仓库运费模板设置的运费</div>
        <el-form-item label="价格区间" prop="highPrice" label-width="100px" style="width: 100%">
          <el-row>
            <el-col :span="11">
              <el-input-number v-model="form.lowPrice" placeholder="请输入最小值" :min="0" :max="100"
                               controls-position="right" size="large" style="width: 100%" />
            </el-col>
            <el-col :span="1"></el-col>
            <el-col :span="11">
              <el-input-number
                v-model="form.highPrice"
                :min="0"
                controls-position="right"
                size="large"
                placeholder="请输入最大值"
                style="width: 100%"
              />
            </el-col>
          </el-row>
        </el-form-item>
        <div style="margin: -18px 0px 14px 101px; color: #999">比如填写5和10，那么低于5和高于10直接过滤掉</div>

        <el-table :data="form.priceAdjustments" border style="width: 100%" :cell-style="() => ({ padding: '2px' })">
          <!-- 价格区间下限列 -->
          <el-table-column label="价格区间下限(元)" align="center" width="200">
            <template #default="scope">
              <el-form-item
                :prop="`priceAdjustments.${scope.$index}.minPrice`"
                :rules="[
                 { required: true, message: '请输入最小价格', trigger: 'blur' },
                { validator: validateMinPrice(scope.$index), trigger: 'blur' }]"
                style="margin:10px 10px 10px -75px ;  padding-left: 0; width: 140%"
              >
                <el-input
                  v-model="scope.row.minPrice"
                  placeholder="请输入最小价格"
                  style="width: 100%"
                >
                  <template #append>元</template>
                </el-input>
              </el-form-item>
            </template>
          </el-table-column>

          <!-- 价格区间上限列 -->
          <el-table-column label="价格区间上限(元)" align="center" width="200">
            <template #default="scope">
              <el-form-item
                :prop="`priceAdjustments.${scope.$index}.maxPrice`"
                :rules="[
          { required: true, message: '请输入最大价格', trigger: 'blur' },
          { validator: validateMaxPrice(scope.$index), trigger: 'blur' }
        ]"
                style="margin:10px 10px 10px -75px ;  padding-left: 0; width: 150%"
              >
                <el-input
                  v-model="scope.row.maxPrice"
                  placeholder="请输入最大价格"
                  style="width: 100%"
                >
                 <template #append>元</template>
                </el-input>
              </el-form-item>
            </template>
          </el-table-column>

          <!-- 加点百分比列 -->
          <el-table-column label="加点(%)" align="center" width="200">
            <template #default="scope">
              <el-form-item
                :prop="`priceAdjustments.${scope.$index}.adjustPercent`"
                :rules="[
                 { required: true, message: '请输入加点百分比', trigger: 'blur' },
                   { validator: validateAdjustPercent, trigger: 'blur' }
                    ]"
                style="margin:10px 10px 10px -75px ;  padding-left: 0; width: 150%"
              >
                <el-input
                  v-model="scope.row.adjustPercent"
                  placeholder="请输入百分比"
                  style="width: 100%"
                >
                  <template #append>
                    %
                  </template>
                </el-input>
              </el-form-item>
            </template>
          </el-table-column>

          <!-- 加价列 -->
          <el-table-column label="加价(元)" align="center" width="200">
            <template #default="scope">
              <el-form-item
                :prop="`priceAdjustments.${scope.$index}.adjustAmount`"
                :rules="[
                { required: true, message: '请输入加价金额', trigger: 'blur' },
                { validator: validateAdjustAmount, trigger: 'blur' }
                  ]"
                style="margin:10px 10px 10px -75px ;  padding-left: 0; width: 150%"
              >
                <el-input
                  v-model="scope.row.adjustAmount"
                  placeholder="请输入固定价格"
                  style="width: 100%"
                >
                  <template #append>
                      元
                  </template>
                </el-input>
              </el-form-item>
            </template>
          </el-table-column>

          <!-- 操作列 -->
          <el-table-column label="操作" align="center" width="120">
            <template #default="scope">
              <el-button
                v-if="scope.$index > 0"
                type="danger"
                icon="Minus"
                circle
                @click="removeAdjustment(scope.$index)"
              />
              <el-button
                v-if="scope.$index === form.priceAdjustments.length - 1 && form.priceAdjustments.length < 30"
                type="primary"
                icon="Plus"
                circle
                @click="addAdjustment"
              />
            </template>
          </el-table-column>
        </el-table>
        <h3 style="color: red">
          注意：只填一行则全部根据该区间走
        </h3>
        <h3 style="color: orange">
          示例：成本*加点(%)+成本+加价(元)
        </h3>
        <h3 style="color: orange">
          例如：5*10%+5+20=25.5(元)
        </h3>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="PriceTemplate" lang="ts">
import {
  listPriceTemplate,
  getPriceTemplate,
  delPriceTemplate,
  addPriceTemplate,
  updatePriceTemplate
} from '@/api/zhishu/priceTemplate';
import { PriceTemplateVO, PriceTemplateQuery, PriceTemplateForm } from '@/api/zhishu/priceTemplate/types';
import { ElMessage } from 'element-plus';
import { Message } from '@element-plus/icons-vue';

const a = ref('');


const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { t_price_type } = toRefs<any>(proxy?.useDict('t_price_type'));


const priceTemplateList = ref<PriceTemplateVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const priceTemplateFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

// const initFormData: PriceTemplateForm = {
//   id: undefined,
//   templateName: undefined,
//   priceType:'0',
//   proportion: 0,
//   addAmount: 0,
//   status: undefined
// };
// 初始化表单数据时添加 priceAdjustments 数组
const initFormData: PriceTemplateForm = {
  id: undefined,
  templateName: undefined,
  priceType: '0',
  proportion: 0,
  addAmount: 0,
  status: undefined,
  highPrice: 99999,
  lowPrice: 0.01,
  priceAdjustments: [
    {
      minPrice: 0.01,
      maxPrice: 1,
      adjustPercent: undefined,
      adjustAmount: undefined
    }
  ],
  rangePrice: undefined
};

// 验证最小值
const validateLowPrice = (rule: any, value: number, callback: any) => {
  if (value === null || value === undefined) {
    callback(new Error('最小值不能为空'));
  } else if (form.value.highPrice !== null && form.value.highPrice !== undefined && value >= form.value.highPrice) {
    callback(new Error('最小值必须小于最大值'));
  } else {
    callback();
  }
};

// 验证最大值
const validateHighPrice = (rule: any, value: number, callback: any) => {
  if (value === null || value === undefined) {
    callback(new Error('最大值不能为空'));
  } else if (form.value.lowPrice !== null && form.value.lowPrice !== undefined && value <= form.value.lowPrice) {
    // callback(new Error('最大值必须大于最小值'));
    ElMessage.warning("价格区间最大值必须大于最小值");
  } else {
    callback();
  }
};


const data = reactive<PageData<PriceTemplateForm, PriceTemplateQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    templateName: undefined,
    priceType: undefined,
    proportion: undefined,
    addAmount: undefined,
    status: undefined,
    params: {}
  },
  rules: {
    id: [{ required: true, message: '主键不能为空', trigger: 'blur' }],
    templateName: [{ required: true, message: '模板名称不能为空', trigger: 'blur' }],
    lowPrice: [
      { required: true, message: '最小值不能为空', trigger: 'blur' },
      { validator: validateLowPrice, trigger: 'blur' }
    ],
    highPrice: [
      { required: true, message: '最大值不能为空', trigger: 'blur' },
      { validator: validateHighPrice, trigger: 'blur' }
    ]

  }
});

const { queryParams, form, rules } = toRefs(data);


// 添加调整项
const addAdjustment = () => {
  const lastItem = form.value.priceAdjustments[form.value.priceAdjustments.length - 1];
  form.value.priceAdjustments.push({
    minPrice: lastItem.maxPrice ? Number(lastItem.maxPrice) + 0.01 : 0, // 关键修复
    maxPrice: undefined,
    adjustPercent: undefined,
    adjustAmount: undefined
  });
};

// 移除调整项
const removeAdjustment = (index: number) => {
  form.value.priceAdjustments.splice(index, 1);
};

// 验证最小价格
const validateMinPrice = (index: number) => {
  // return (rule: any, value: number, callback: any) => {
  //   if (index > 0 && value <= form.value.priceAdjustments[index - 1].maxPrice) {
  //     ElMessage.warning('区间不能重复');
  //   } else {
  //     callback();
  //   }
  // };
  return (rule: any, value: number, callback: any) => {
    if (value < 0.01) {
      ElMessage.warning("价格区间下限不能小于0.01元");
      return;
    }
    if (index > 0 && value <= form.value.priceAdjustments[index - 1].maxPrice) {
          ElMessage.warning('区间不能重复');
      return;
    }
    callback();
  };
};


// 验证最大价格
const validateMaxPrice = (index: number) => {
  return (rule: any, value: number, callback: any) => {
    const currentRow = form.value.priceAdjustments[index];

    // 确保是数字比较
    const minPrice = Number(currentRow.minPrice);
    const maxPrice = Number(value);

    if (!maxPrice && maxPrice !== 0) {  // 检查是否为有效数字（包括0）
      callback(new Error('请输入最大价格'));
    } else if (maxPrice <= minPrice) {
      ElMessage.warning('价格区间上限要大于价格下限!');
    } else {
      callback();
    }
  };
};
const validateAdjustPercent = async (rule, value, callback) => {
  if (value === undefined || value === null) {
    callback(new Error('请输入加点百分比'));
  } else if (value < 0) {
    callback(new Error('请输入有效的正数百分比'));
  } else {
    callback();
  }
};
const validateAdjustAmount = async (rule, value, callback) => {
  if (value === undefined || value === null) {
    callback(new Error('请输入加价金额'));
  } else if (isNaN(value) || value < 0) {
    callback(new Error('请输入有效的正数金额'));
  } else {
    callback();
  }
};

// const resetFormData = async () => {
//   form.value.priceAdjustments.length = 1;
//   this.form = {
//     templateName: '',
//     priceType: '',
//     lowPrice: 0,
//     highPrice: 0,
//     priceAdjustments: [
//       {
//         minPrice: 0,
//         maxPrice: 1,
//         adjustPercent: undefined,
//         adjustAmount: undefined
//       }
//     ]
//   };
// };

const resetFormData = async () => {
  form.value = { ...initFormData };
  form.value.priceAdjustments.length = 1;

};


/** 查询价格模板列表 */
const getList = async () => {
  loading.value = true;
  const res = await listPriceTemplate(queryParams.value);
  priceTemplateList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

const returnNumber = (num) => {
  return (Number(form.value.proportion) / 100 * num + num + Number(form.value.addAmount)).toFixed(2);
};
/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  priceTemplateFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: PriceTemplateVO[]) => {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = '添加价格模板';
};

/** 修改按钮操作 */
const handleUpdate = async (row?: PriceTemplateVO) => {
  reset();
  const _id = row?.id || ids.value[0];
  const res = await getPriceTemplate(_id);
  console.log(res, 'asjhdjahdakjhk');


  if(res.data.lowPrice!=null&&res.data.highPrice!=null&&res.data.rangePrice!=null){
    res.data.lowPrice /= 100;
    res.data.highPrice /= 100;
// 1. 解析 JSON 字符串
    const rangePriceDat1a = JSON.parse(res.data.rangePrice);
    console.log(rangePriceDat1a);
    const rangePriceData = JSON.parse(res.data.rangePrice).map(item => ({
      ...item,
      minPrice: item.minPrice / 100,          // 分 → 元
      maxPrice: item.maxPrice / 100,          // 分 → 元
      adjustAmount: item.adjustAmount / 100,  // 分 → 元
      adjustPercent: item.adjustPercent ? Number(item.adjustPercent) : 0 // 字符串转数字（可选）
    }));
    // 转换为数组
    const priceAdjustments: {
      minPrice?: number;
      maxPrice?: number;
      adjustPercent?: number;
      adjustAmount?: number;
    }[] = Object.values(rangePriceData);
    res.data.priceAdjustments=priceAdjustments;
    res.data.rangePrice = priceAdjustments;
  }
  Object.assign(form.value, res.data);
  console.log(form.value)
  dialog.visible = true;
  dialog.title = '修改价格模板';
};

/** 提交按钮 */
const submitForm = () => {
  priceTemplateFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
        form.value.lowPrice *= 100;
        form.value.highPrice *= 100;
        // 处理 priceAdjustments 数组
        form.value.priceAdjustments = form.value.priceAdjustments.map(item => ({
          ...item,
          minPrice: item.minPrice * 100,
          maxPrice: item.maxPrice * 100,
          adjustAmount: item.adjustAmount * 100
        }));
      form.value.rangePrice = JSON.stringify(form.value.priceAdjustments);
      buttonLoading.value = true;
      if (form.value.id) {
        await updatePriceTemplate(form.value).finally(() => (buttonLoading.value = false));
      } else {
        await addPriceTemplate(form.value).finally(() => (buttonLoading.value = false));
      }
      proxy?.$modal.msgSuccess('操作成功');
      dialog.visible = false;
      await getList();
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row?: PriceTemplateVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除价格模板编号为"' + _ids + '"的数据项？').finally(() => (loading.value = false));
  await delPriceTemplate(_ids);
  proxy?.$modal.msgSuccess('删除成功');
  await getList();
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    'zhishu/priceTemplate/export',
    {
      ...queryParams.value
    },
    `priceTemplate_${new Date().getTime()}.xlsx`
  );
};

onMounted(() => {
  getList();
});
</script>


<style scoped lang="scss">
.el-input-number .el-input__inner {
  text-align: left !important;
}

.titleCss {
  text-align: center;
  height: 30px;
  line-height: 30px;
  width: 150px;
}

.titleCss2 {
  float: left;
  text-align: right;
  height: 30px;
  line-height: 30px;
  width: 100px;
}

.textCss {
  text-align: left;
  height: 30px;
  line-height: 30px;
}
</style>
