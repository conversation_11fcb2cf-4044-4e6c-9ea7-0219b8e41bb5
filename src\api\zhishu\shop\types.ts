export interface ShopVO {
  /**
   *
   */
  id: string | number;

  /**
   * 店铺类型  1 拼多多
   */
  shopType: string;

  /**
   * 分组
   */
  shopGroup: string;

  /**
   * 店铺名称
   */
  shopName: string;

  /**
   * 是否授权  0未授权 1已授权 2已过期
   */
  shopAuthorize: string;

  /**
   * 到期时间
   */
  expirationTime: string;

  /**
   * 店铺key
   */
  shopKey: string;

  /**
   * token
   */
  token: string;

  /**
   * 店铺状态（0正常 1停用）
   */
  status: string;

  /**
   * 第三方平台账号
   */
  account: string;

  /**
   * 第三方平台密码
   */
  password: string;

  /**
   * 自动发布
   */
  autoAdd: string;

  /**
   * 是否同步订单（0关闭 1开启）
   */
  isSynOrder: string;

}

export interface ShopForm extends BaseEntity {
  /**
   *
   */
  id?: string | number;

  /**
   * 店铺类型  1 拼多多
   */
  shopType?: string;

  /**
   * 分组
   */
  shopGroup?: string;

  /**
   * 店铺名称
   */
  shopName?: string;

  /**
   * 是否授权  0未授权 1已授权 2已过期
   */
  shopAuthorize?: string;

  /**
   * 到期时间
   */
  expirationTime?: string;

  /**
   * 店铺key
   */
  shopKey?: string;

  /**
   * token
   */
  token?: string;

  /**
   * 店铺状态（0正常 1停用）
   */
  status?: string;

  /**
   * 第三方平台账号
   */
  account?: string;

  /**
   * 第三方平台密码
   */
  password?: string;

  /**
   * 是否存在待同步商品文件
   */
  hasSyncFile?: string;

  /**
   * 是否同步订单（0关闭 1开启）
   */
  isSynOrder?: string;
}

export interface ShopQuery extends PageQuery {

  /**
   * 店铺类型  1 拼多多
   */
  shopType?: string;

  /**
   * 分组
   */
  shopGroup?: string;

  /**
   * 店铺名称
   */
  shopName?: string;

  /**
   * 是否授权  0未授权 1已授权 2已过期
   */
  shopAuthorize?: string;

  /**
   * 到期时间
   */
  expirationTime?: string;

  /**
   * 店铺key
   */
  shopKey?: string;

  /**
   * token
   */
  token?: string;

  /**
   * 店铺状态（0正常 1停用）
   */
  status?: string;

  /**
   * 日期范围参数
   */
  params?: any;
  /**
   * 第三方平台账号
   */
  account?: string;

  /**
   * 第三方平台密码
   */
  password?: string;
  /**
   * 是否存在待同步商品文件
   */
  hasSyncFile?: string;
}


export interface ShopDetailVO {
  /**
   * 主键
   */
  id: string | number;

  /**
   * 店铺id
   */
  shopId: string | number;

  /**
   * 物流运费模板id
   */
  templateId: string | number;

  /**
   * 高价
   */
  highPrice: number;

  /**
   * 低价
   */
  lowPrice: number;

  /**
   * 销售模板id
   */
  saleTemplateId: string | number;

  /**
   * 是否开启7天无理由  0关闭 1开启
   */

  sevenDays: string;

  /**
   * 是否开始二手  0关闭  1开启
   */
  isSecondHand: string;

  /**
   * 轮播数量开启  0关闭 1开启
   */
  carouselNum: string;

  /**
   * 是否开启详情图  0关闭 1开启
   */
  isDetailsImg: string;

  /**
   * 是否开启目录详情  0关闭 1开启
   */
  isCatalogueDetails: string;

  /**
   * 是否开启信息图片 0关闭 1开启
   */
  isInformationImg: string;

  /**
   * 标题前缀
   */
  titlePrefix: string;

  /**
   * 标题后缀
   */
  titleSuffix: string;

  /**
   * 标题过滤
   */
  titleFilter: string;

  /**
   * 货号前缀
   */
  itemNumberPrefix: string;

  /**
   * 标题组成
   */
  titleConsistOf: string;

  /**
   * 间隔字符  0无间隔 1空格
   */
  spaceCharacter: string;

  /**
   * 是否开启自动截断  0关闭 1开启
   */
  autoTruncation: string;

  /**
   * 我的设置
   */
  mySetUp: string;

  /**
   * 系统设置
   */
  systemSetUp: string;

  /**
   * 过滤套装 0否 1是
   */
  filterSuit: string;

  /**
   * 删除保护 0关闭  1开启
   */
  isDelProtect: string;

  /**
   * 下架保护 0关闭 1开启
   */
  isRemoveProtect: string;

  /**
   * 图书定价 0关闭 1开启
   */
  bootPrice: string;

  /**
   * 自有图片 0关闭 1开启
   */
  ownPictures: string;

}

export interface ShopDetailForm extends BaseEntity {
  /**
   * 主键
   */
  id?: string | number;

  /**
   * 店铺id
   */
  shopId?: string | number;

  /**
   * 物流运费模板id
   */
  templateId?: string | number;

  /**
   * 高价
   */
  highPrice?: number;

  /**
   * 低价
   */
  lowPrice?: number;

  /**
   * 销售模板id
   */
  saleTemplateId?: string | number;

  /**
   * 是否开启7天无理由  0关闭 1开启
   */

  sevenDays?: string;

  /**
   * 是否开始二手  0关闭  1开启
   */
  isSecondHand?: string;

  /**
   * 轮播数量开启  0关闭 1开启
   */
  carouselNum?: string;

  /**
   * 是否开启详情图  0关闭 1开启
   */
  isDetailsImg?: string;

  /**
   * 是否开启目录详情  0关闭 1开启
   */
  isCatalogueDetails?: string;

  /**
   * 是否开启信息图片 0关闭 1开启
   */
  isInformationImg?: string;

  /**
   * 标题前缀
   */
  titlePrefix?: string;

  /**
   * 标题后缀
   */
  titleSuffix?: string;

  /**
   * 标题过滤
   */
  titleFilter?: string;

  /**
   * 货号前缀
   */
  itemNumberPrefix?: string;

  /**
   * 标题组成
   */
  titleConsistOf?: string;

  /**
   * 间隔字符  0无间隔 1空格
   */
  spaceCharacter?: string;

  /**
   * 是否开启自动截断  0关闭 1开启
   */
  autoTruncation?: string;

  /**
   * 我的设置
   */
  mySetUp?: string;

  /**
   * 系统设置
   */
  systemSetUp?: string;

  /**
   * 过滤套装 0否 1是
   */
  filterSuit?: string;

  /**
   * 删除保护 0关闭  1开启
   */
  isDelProtect?: string;

  /**
   * 下架保护 0关闭 1开启
   */
  isRemoveProtect?: string;

  /**
   * 图书定价 0关闭 1开启
   */
  bootPrice?: string;

  /**
   * 自有图片 0关闭 1开启
   */
  ownPictures?: string;

}

export interface ShopDetailQuery extends PageQuery {

  /**
   * 店铺id
   */
  shopId?: string | number;

  /**
   * 物流运费模板id
   */
  templateId?: string | number;

  /**
   * 高价
   */
  highPrice?: number;

  /**
   * 低价
   */
  lowPrice?: number;

  /**
   * 销售模板id
   */
  saleTemplateId?: string | number;

  /**
   * 是否开启7天无理由  0关闭 1开启
   */

  sevenDays?: string;

  /**
   * 是否开始二手  0关闭  1开启
   */
  isSecondHand?: string;

  /**
   * 轮播数量开启  0关闭 1开启
   */
  carouselNum?: string;

  /**
   * 是否开启详情图  0关闭 1开启
   */
  isDetailsImg?: string;

  /**
   * 是否开启目录详情  0关闭 1开启
   */
  isCatalogueDetails?: string;

  /**
   * 是否开启信息图片 0关闭 1开启
   */
  isInformationImg?: string;

  /**
   * 标题前缀
   */
  titlePrefix?: string;

  /**
   * 标题后缀
   */
  titleSuffix?: string;

  /**
   * 标题过滤
   */
  titleFilter?: string;

  /**
   * 货号前缀
   */
  itemNumberPrefix?: string;

  /**
   * 标题组成
   */
  titleConsistOf?: string;

  /**
   * 间隔字符  0无间隔 1空格
   */
  spaceCharacter?: string;

  /**
   * 是否开启自动截断  0关闭 1开启
   */
  autoTruncation?: string;

  /**
   * 我的设置
   */
  mySetUp?: string;

  /**
   * 系统设置
   */
  systemSetUp?: string;

  /**
   * 过滤套装 0否 1是
   */
  filterSuit?: string;

  /**
   * 删除保护 0关闭  1开启
   */
  isDelProtect?: string;

  /**
   * 下架保护 0关闭 1开启
   */
  isRemoveProtect?: string;

  /**
   * 图书定价 0关闭 1开启
   */
  bootPrice?: string;

  /**
   * 自有图片 0关闭 1开启
   */
  ownPictures?: string;

    /**
     * 日期范围参数
     */
    params?: any;
}

export interface saveArtNo{
  saveArtNoRule:number|string,
  shopId:number|string,
  freightIdList:number[],
  taskId:number,
}


export interface  WlnShopList{
  // 万里牛店铺名称
  shopName:string;
  // 万里牛系统ID
  shopNick:string;
}

/**
 * 解密需要的数据
 */
export interface  EncrypData{
  dataUrl?:string;  //数据文件网络访问链接
  shopPrice?:number; //店铺优惠
  maxPrice?:number;  //利润区间最大
  minPrice?:number;  //利润区间最小
  shopId?:number;    //店铺Id
  shopType?:number   //店铺类型
}
export type ShopMap = Record<string, string>;
