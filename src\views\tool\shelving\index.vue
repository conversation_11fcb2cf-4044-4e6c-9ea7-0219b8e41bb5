<template>
    <div class="p-2 bg-gray-50 min-h-screen flex justify-center">
        <div class="w-full">
            <el-card shadow="hover" class="mb-2 bg-white rounded-lg">
                <template #header>
                    <div class="card-header">
                        <span class="font-bold text-lg text-gray-700">商品信息</span>
                    </div>
                </template>
                <el-form ref="formRef" :model="formData" label-width="120px" class="px-2">
                    <!-- 商品条码 -->
                    <el-form-item label="商品条码" prop="barcode" class="mb-2">
                        <el-input
                                v-model="formData.barcode"
                                placeholder="请输入商品条码"
                                ref="barcodeInput"
                                @keyup.enter="handleBarcodeInput"
                                :loading="loading"
                                class="w-full max-w-md"
                        />
                    </el-form-item>

                    <!-- 库号 -->
                    <el-form-item label="库号" prop="warehouseCode" class="mb-2">
                        <div class="flex gap-1 items-center" style="width: 446px">
                            <!-- 第一部分（2字符） -->
                            <el-input style="width: 150px"
                                    v-model="warehouseParts[0]"
                                    :maxlength="2"
                                    class="w-12 text-center"
                                    @input="handleWarehouseInput(0)"
                                    @keydown.delete="handleBackspace(0)"
                                    ref="part1"
                            />

                            <!-- 第二部分（2字符） -->
                            <el-input style="width: 150px"
                                    v-model="warehouseParts[1]"
                                    :maxlength="2"
                                    class="w-12 text-center"
                                    @input="handleWarehouseInput(1)"
                                    @keydown.delete="handleBackspace(1)"
                                    ref="part2"
                            />

                            <!-- 第三部分（1字符） -->
                            <el-input style="width: 150px"
                                    v-model="warehouseParts[2]"
                                    :maxlength="1"
                                    class="w-8 text-center"
                                    @input="handleWarehouseInput(2)"
                                    @keydown.delete="handleBackspace(2)"
                                    ref="part3"
                            />

                            <!-- 第四部分（商品条码） -->
                            <el-input
                                    v-model="warehouseParts[3]"
                                    disabled
                                    class="w-52"
                            />
                        </div>
                    </el-form-item>

                    <!-- 商品名称 -->
                    <el-form-item label="商品名称" prop="name" class="mb-2">
                        <el-input v-model="formData.name" class="w-full max-w-md" />
                    </el-form-item>

                    <!-- 价格 -->
                    <el-form-item label="价格" prop="price" class="mb-2">
                        <el-input-number v-model="formData.price" :precision="2" class="w-full max-w-md" />
                    </el-form-item>

                    <!-- 库存数量 -->
                    <el-form-item label="库存数量" prop="stock" class="mb-2">
                        <el-input-number v-model="formData.stock" :min="1" class="w-full max-w-md" />
                    </el-form-item>

                    <!-- 品相 -->
                    <el-form-item label="品相" prop="conditionCode" class="mb-2">
                        <el-select v-model="formData.conditionCode" placeholder="请选择品相" class="w-full max-w-md">
                            <el-option
                                    v-for="option in conditionCodeOptions"
                                    :key="option.value"
                                    :label="option.label"
                                    :value="option.value"
                            />
                        </el-select>
                    </el-form-item>
                </el-form>
            </el-card>

            <!-- 商品查询结果列表 -->
            <el-card shadow="hover" class="mb-2 bg-white rounded-lg">
                <template #header>
                    <div class="card-header">
                        <span class="font-bold text-lg text-gray-700">查询结果</span>
                    </div>
                </template>
                <el-table :data="productList" class="w-full">
                    <el-table-column prop="name" label="商品名称" >
                        <template #default="{row}">
                            <el-input v-model="row.name" disabled/>
                        </template>
                    </el-table-column>

                    <el-table-column prop="barcode" label="商品条码">
                        <template #default="{row}">
                            <el-input v-model="row.barcode" disabled />
                        </template>
                    </el-table-column>

                    <el-table-column prop="price" label="价格">
                        <template #default="{row}">
                            <el-input-number v-model="row.price" :precision="2" />
                        </template>
                    </el-table-column>

                    <el-table-column prop="stock" label="库存数量">
                        <template #default="{row}">
                            <el-input-number v-model="row.stock" :min="1" />
                        </template>
                    </el-table-column>

                    <el-table-column prop="conditionCode" label="品相">
                        <template #default="{row}">
                            <el-select v-model="row.conditionCode" disabled>
                                <el-option
                                        v-for="option in conditionCodeOptions"
                                        :key="option.value"
                                        :label="option.label"
                                        :value="option.value"
                                />
                            </el-select>
                        </template>
                    </el-table-column>

                    <el-table-column prop="warehouseCode" label="库号" />

                    <el-table-column label="操作" width="120">
                        <template #default="{row}">
                            <el-button
                                    type="primary"
                                    size="small"
                                    @click="handleUpdateProduct(row)"
                            >
                                保存
                            </el-button>
                        </template>
                    </el-table-column>
                </el-table>
            </el-card>

            <!-- 商品拍照 -->
            <el-card shadow="hover" class="mb-2 bg-white rounded-lg">
                <template #header>
                    <div class="card-header">
                        <span class="font-bold text-lg text-gray-700">商品拍照（需拍摄4张）</span>
                        <span class="text-sm text-gray-500">{{ photos.length }}/4</span>
                    </div>
                </template>
                <div class="grid grid-cols-4 gap-1 p-2">
                    <div v-for="(photo, index) in photos" :key="index" class="relative aspect-square">
                        <img :src="photo.preview" class="w-full h-full object-cover rounded-lg shadow-sm hover:shadow-md transition-shadow" />
                        <el-icon class="absolute top-1 right-1 cursor-pointer text-white bg-red-500 rounded-full p-1 hover:bg-red-600" @click="removePhoto(index)">
                            <CloseBold />
                        </el-icon>
                    </div>
                    <div
                            v-for="i in 4 - photos.length"
                            :key="'empty-'+i"
                            class="border-2 border-dashed border-gray-300 aspect-square flex items-center justify-center cursor-pointer rounded-lg hover:border-gray-400 transition-colors"
                            @click="openCamera"
                    >
                        <el-icon class="text-2xl text-gray-400"><Plus /></el-icon>
                    </div>
                </div>
            </el-card>

            <!-- 按钮 -->
            <div class="flex justify-center gap-2 mt-2">
                <el-button type="primary" :disabled="!canSubmit" @click="handleSubmit" class="px-6 py-2">
                    确认入库
                </el-button>
                <el-button @click="handleReset" class="px-6 py-2">重置</el-button>
            </div>

            <!-- 摄像头弹窗 -->
            <el-dialog v-model="cameraVisible" title="拍照" width="500px" class="rounded-lg">
                <video ref="videoRef" autoplay class="w-full rounded-lg"></video>
                <template #footer>
                    <el-button @click="cameraVisible = false" class="px-6 py-2">取消</el-button>
                    <el-button type="primary" @click="capturePhoto" class="px-6 py-2">拍照</el-button>
                </template>
            </el-dialog>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, onUnmounted, nextTick } from 'vue'
import { ElMessage, ElMessageBox, type FormInstance } from 'element-plus'
import { Plus, CloseBold } from '@element-plus/icons-vue'
import {getProductByBarcode, submitProduct} from "@/api/tool/shelving";

interface ProductInfo {
    id: string
    name: string
    warehouseCode: string
    barcode: string
    price: number
    stock: number
    conditionCode: number
}

interface PhotoItem {
    file: File
    preview: string
}

const formRef = ref<FormInstance>()
const videoRef = ref<HTMLVideoElement>()
const barcodeInput = ref<HTMLInputElement>()
const cameraVisible = ref(false)
const productInfo = ref<ProductInfo | null>(null)
const photos = ref<PhotoItem[]>([])
const productList = ref<ProductInfo[]>([]) // 商品查询结果列表
const warehouseInputs = ref<HTMLInputElement[]>([])
// 输入框ref数组
const part1 = ref()
const part2 = ref()
const part3 = ref()

// 品相选项
const conditionCodeOptions = [
    { value: 100, label: '全新' },
    { value: 95, label: '九五品' },
    { value: 90, label: '九品' },
    { value: 85, label: '八五品' },
    { value: 80, label: '八品' },
    { value: 75, label: '七五品' },
    { value: 70, label: '七品' },
    { value: 65, label: '六五品' },
    { value: 60, label: '六品' },
    { value: 50, label: '五品' },
    { value: 40, label: '四品' },
    { value: 30, label: '三品' },
    { value: 20, label: '二品' },
    { value: 10, label: '一品' }
]

// 初始化库号部分（前三个可编辑，第四个固定）
const warehouseParts = ref(['', '', '', ''])


const formData = reactive({
    id: '', // 商品ID
    name: '',
    barcode: '',
    warehouseCode: computed(() => warehouseParts.value.join('-')),
    price: 0,
    stock: 1,
    conditionCode: 100,
    updateFlag: false
})
// 更新商品信息
const handleUpdateProduct = async (product: ProductInfo) => {
    try {
        // 构建与入库相同的表单数据结构
        const updateForm = {
            id: product.id,
            name: product.name,
            barcode: product.barcode,
            warehouseCode: product.warehouseCode,
            price: product.price,
            stock: product.stock,
            conditionCode: product.conditionCode,
            updateFlag: true
        };
        // 创建FormData（保持与submit相同的结构）
        const form = new FormData()
        form.append('data', JSON.stringify(updateForm))

        // 调用统一的提交接口，通过操作类型区分
        await submitProduct(form, [])

        ElMessage.success('商品信息更新成功')
        handleReset()
    } catch (error) {
        ElMessage.error('更新失败')
    }
}

// 处理库号输入
const handleWarehouseInput = async (index: number) => {
    await nextTick() // 等待DOM更新

    // 定义跳转规则
    const jumpMap = [
        { max: 2, next: part2 }, // 第一部分填满跳转第二部分
        { max: 2, next: part3 }, // 第二部分填满跳转第三部分
        { max: 1, next: null }  // 第三部分填满不跳转
    ]

    // 当前输入框内容长度
    const currentLength = warehouseParts.value[index].length

    // 当输入达到最大长度且存在下一个输入框时
    if (currentLength === jumpMap[index].max && jumpMap[index].next) {
        // 获取实际input元素
        const nextInput = jumpMap[index].next.value?.$el?.querySelector('input')
        nextInput?.focus()
    }
}
const handleBackspace = (index: number) => {
    if (warehouseParts.value[index].length === 0 && index > 0) {
        const prevInput = [part1, part2][index - 1]?.value?.$el?.querySelector('input')
        prevInput?.focus()
    }
}
const validateWarehouseCode = () => {
    return (
        warehouseParts.value[0].length === 2 &&
        warehouseParts.value[1].length === 2 &&
        warehouseParts.value[2].length === 1
    )
}
// 计算是否可提交
const canSubmit = computed(() => {
    return validateWarehouseCode() &&
        formData.warehouseCode &&
        formData.name &&
        formData.conditionCode &&
        photos.value.length === 4
})

// 进入页面时自动聚焦到库号的第一个输入框
onMounted(() => {
    if (warehouseInputs.value.length > 0) {
        warehouseInputs.value[0].focus()
    }
})

// 处理条码输入
const handleBarcodeInput = async () => {
    if (!formData.barcode) return

    try {
        // 调用商品查询接口，传入库号和商品条码
        const res = await getProductByBarcode(formData.barcode)
        if (res.data.length > 0) {
            // 只有一条数据，直接回显
            const product = res.data[0]
            formData.id = product.id
            formData.name = product.name
            // 同步商品条码到库号第四部分
            warehouseParts.value[3] = product.barcode
            productList.value = res.data.filter(item => item.stock !== 0)
            ElMessage.success('商品信息加载成功')
            nextTick(() => {
                // 聚焦到库号第一个输入框
                part1.value?.focus()
            })
        }else{
            ElMessage.success('没有查到对应书籍')
            handleReset()
        }
    } catch (error) {
        barcodeInput.value?.focus()
    }
}


// 打开摄像头
let mediaStream: MediaStream | null = null
const openCamera = async () => {
    try {
        cameraVisible.value = true
        await nextTick()

        mediaStream = await navigator.mediaDevices.getUserMedia({
            video: { facingMode: 'environment' }
        })
        if (videoRef.value) {
            videoRef.value.srcObject = mediaStream
        }
    } catch (error) {
        ElMessage.error('无法访问摄像头')
        cameraVisible.value = false
    }
}

// 拍照
const capturePhoto = () => {
    if (!videoRef.value) return

    const canvas = document.createElement('canvas')
    canvas.width = videoRef.value.videoWidth
    canvas.height = videoRef.value.videoHeight

    const ctx = canvas.getContext('2d')
    if (ctx) {
        ctx.drawImage(videoRef.value, 0, 0, canvas.width, canvas.height)

        canvas.toBlob((blob) => {
            if (blob) {
                const file = new File([blob], `photo_${Date.now()}.jpg`, {
                    type: 'image/jpeg'
                })

                photos.value.push({
                    file,
                    preview: URL.createObjectURL(blob)
                })

                cameraVisible.value = false
                stopCamera()
            }
        }, 'image/jpeg', 0.9)
    }
}

// 停止摄像头
const stopCamera = () => {
    if (mediaStream) {
        mediaStream.getTracks().forEach(track => track.stop())
        mediaStream = null
    }
}

// 删除照片
const removePhoto = (index: number) => {
    photos.value.splice(index, 1)
}

// 提交处理
const handleSubmit = async () => {
    try {
        const formValid = await formRef.value?.validate()
        if (!formValid) return

        const form = new FormData()
        form.append('data', JSON.stringify(formData))
        photos.value.forEach(photo => {
            form.append('photos', photo.file)
        })

        // 调用入库接口，同时上传商品信息和照片
        await submitProduct(form, photos.value);
        ElMessage.success('商品入库成功')
        handleReset()
    } catch (error) {
        ElMessage.error('入库操作失败')
    }
}

// 重置表单
const handleReset = () => {
    formRef.value?.resetFields()
    formData.id = ''
    formData.conditionCode = 100
    productInfo.value = null
    photos.value = []
    warehouseParts.value = Array(6).fill('')
    productList.value = []
    warehouseParts.value = ['', '', '', '']
    if (warehouseInputs.value.length > 0) {
        warehouseInputs.value[0].focus()
    }
    barcodeInput.value?.focus()
}

onUnmounted(() => {
    stopCamera()
})
</script>

<style scoped>
.card-header {
    @apply flex items-center justify-between;
}
</style>