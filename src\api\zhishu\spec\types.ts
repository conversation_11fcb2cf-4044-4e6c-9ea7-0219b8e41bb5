export interface SpecVO {
  /**
   * 主键
   */
  id: string | number;

  /**
   * 店铺id
   */
  shopId: string | number;

  /**
   * 规格类型id
   */
  specTypeId: string | number;

  /**
   * 规格类型名称
   */
  specTypeName: string;

  /**
   * 规格组成 0 自定义 1 ISBN 2 书名 3货号
   */
  specCompose: string;

  /**
   * 自定义规格名称
   */
  specName: string;

  /**
   * 自定义规格前缀
   */
  specPrefix: string;

  /**
   * 自定义规格后缀
   */
  specSuffix: string;

  /**
   * 规格编码组成 0 自定义 1 ISBN 2 书名 3货号
   */
  specCodeCompose: string;

  /**
   * 自定义规格编码前缀
   */
  specCodePrefix: string;

  /**
   * 自定义规格编码后缀
   */
  specCodeSuffix: string;

  /**
   * SKU水印图片路径
   */
  specSyUrl: string;

  /**
   * 状态（0正常 1停用）
   */
  status: string;

}

export interface SpecForm extends BaseEntity {
  /**
   * 主键
   */
  id?: string | number;

  /**
   * 店铺id
   */
  shopId?: string | number;

  /**
   * 规格类型id
   */
  specTypeId?: string | number;

  /**
   * 规格类型名称
   */
  specTypeName?: string;

  /**
   * 规格组成 0 自定义 1 ISBN 2 书名 3货号
   */
  specCompose?: string;

  /**
   * 自定义规格名称
   */
  specName?: string;

  /**
   * 自定义规格前缀
   */
  specPrefix?: string;

  /**
   * 自定义规格后缀
   */
  specSuffix?: string;

  /**
   * 规格编码组成 0 自定义 1 ISBN 2 书名 3货号
   */
  specCodeCompose?: string;

  /**
   * 自定义规格编码前缀
   */
  specCodePrefix?: string;

  /**
   * 自定义规格编码后缀
   */
  specCodeSuffix?: string;

  /**
   * SKU水印图片路径
   */
  specSyUrl?: string;

  /**
   * 状态（0正常 1停用）
   */
  status?: string;

}

export interface SpecQuery extends PageQuery {

  /**
   * 店铺id
   */
  shopId?: string | number;

  /**
   * 规格类型id
   */
  specTypeId?: string | number;

  /**
   * 规格类型名称
   */
  specTypeName?: string;

  /**
   * 规格组成 0 自定义 1 ISBN 2 书名 3货号
   */
  specCompose?: string;

  /**
   * 自定义规格名称
   */
  specName?: string;

  /**
   * 自定义规格前缀
   */
  specPrefix?: string;

  /**
   * 自定义规格后缀
   */
  specSuffix?: string;

  /**
   * 规格编码组成 0 自定义 1 ISBN 2 书名 3货号
   */
  specCodeCompose?: string;

  /**
   * 自定义规格编码前缀
   */
  specCodePrefix?: string;

  /**
   * 自定义规格编码后缀
   */
  specCodeSuffix?: string;

  /**
   * SKU水印图片路径
   */
  specSyUrl?: string;

  /**
   * 状态（0正常 1停用）
   */
  status?: string;

    /**
     * 日期范围参数
     */
    params?: any;
}



