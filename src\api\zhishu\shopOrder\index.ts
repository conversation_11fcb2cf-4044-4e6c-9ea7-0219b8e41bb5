import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import {
  ShopOrderVO,
  ShopOrderForm,
  ShopOrderQuery,
  ShippingMethod,
  ShopsShipments, ShopListIds
} from '@/api/zhishu/shopOrder/types';
import { ShopForm } from '@/api/zhishu/shop/types';

/**
 * 查询订单列表
 * @param query
 * @returns {*}
 */

export const listShopOrder = (query?: ShopOrderQuery): AxiosPromise<ShopOrderVO[]> => {
  return request({
    url: '/zhishu/shopOrder/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询订单详细
 * @param id
 */
export const getShopOrder = (id: string | number): AxiosPromise<ShopOrderVO> => {
  return request({
    url: '/zhishu/shopOrder/' + id,
    method: 'get'
  });
};

/**
 * 新增订单
 * @param data
 */
export const addShopOrder = (data: ShopOrderForm) => {
  return request({
    url: '/zhishu/shopOrder',
    method: 'post',
    data: data
  });
};

/**
 * 修改订单
 * @param data
 */
export const updateShopOrder = (data: ShopOrderForm) => {
  return request({
    url: '/zhishu/shopOrder',
    method: 'put',
    data: data
  });
};

/**
 * 删除订单
 * @param id
 */
export const delShopOrder = (id: string | number | Array<string | number>) => {
  return request({
    url: '/zhishu/shopOrder/' + id,
    method: 'delete'
  });
};

/**
 * 查询订单 和同步数据订单
 * @param id
 */
export const getPddShopOrder = (id: string | number | Array<string | number>) => {
  return request({
    url: '/zhishu/shopOrder/' + id,
    method: 'delete'
  });
};

/**
 * 获取配送方式列表
 * @param shopId
 * @param orderSourceType
 */
export const getShopShi = (shopId: number, orderSourceType: number): AxiosPromise<ShippingMethod[]> => {
  return request({
    url: '/zhishu/shopOrder/delivery/methodList',
    method: 'get',
    params: {
      shopId,
      orderSourceType
    }
  });
};

/**
 * 订单发货
 * @param data
 */
export const ShopShipments = (data: ShopsShipments) => {
  return request({
    url: '/zhishu/shopOrder/delivery',
    method: 'post',
    data: data
  });
};

/**
 * 下发任务给仓库
 */
export const checkInfo = (id) => {
  return request({
    url: '/zhishu/shopOrder/checkInfo?orderId='+id,
    method: 'post'
  });
}
export const syncKfzHistoryOrder=(days:number,data:ShopListIds)=>{
  return request({
    url: '/zhishu/shopOrder/syncKfzHistoryOrder?days='+days,
    method: 'post',
    data: data
  });
};

/**
 * 更新店铺订单同步状态
 * @param data 包含shopId和isSynOrder的对象
 */
export const updateIsSynOrder = (data: { shopId: string | number; isSynOrder: number }) => {
  return request({
    url: `/zhishu/shopOrder/updateIsSynOrder?shopId=${data.shopId}&isSynOrder=${data.isSynOrder}`,
    method: 'get'
  });
};
