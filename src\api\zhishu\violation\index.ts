import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { ViolationVO, ViolationForm, ViolationQuery } from '@/api/zhishu/violation/types';

/**
 * 查询违规列表
 * @param query
 * @returns {*}
 */

export const listViolation = (query?: ViolationQuery): AxiosPromise<ViolationVO[]> => {
  return request({
    url: '/zhishu/violation/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询违规详细
 * @param id
 */
export const getViolation = (id: string | number): AxiosPromise<ViolationVO> => {
  return request({
    url: '/zhishu/violation/' + id,
    method: 'get'
  });
};

/**
 * 新增违规
 * @param data
 */
export const addViolation = (data: ViolationForm) => {
  return request({
    url: '/zhishu/violation',
    method: 'post',
    data: data
  });
};

/**
 * 修改违规
 * @param data
 */
export const updateViolation = (data: ViolationForm) => {
  return request({
    url: '/zhishu/violation',
    method: 'put',
    data: data
  });
};

/**
 * 删除违规
 * @param id
 */
export const delViolation = (id: string | number | Array<string | number>) => {
  return request({
    url: '/zhishu/violation/' + id,
    method: 'delete'
  });
};

/**
 * 修改任务审核状态
 * @param id 
 * @param type 
 * @returns 
 */
export const editType = (ids,type) => {
  return request({
    url: '/zhishu/violation/editType',
    method: 'post',
    data:{
      "ids":ids,
      "type":type
    }
  });
}
