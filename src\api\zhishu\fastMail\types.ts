export interface FastMailVO {
  /**
   * 主键
   */
  id: string | number;

  /**
   * 类型 1 韵达
   */
  type: string;

  /**
   * 账号
   */
  partnerId: string | number;

  /**
   * 联调密码
   */
  secret: string;

  /**
   * 创建时间
   */
  createTime: string;

  /**
   * 更新时间
   */
  updateTime: string;

  /**
   * 状态（0正常 1停用）
   */
  status: string;

}

export interface FastMailForm extends BaseEntity {
  /**
   * 主键
   */
  id?: string | number;

  /**
   * 类型 1 韵达
   */
  type?: string;

  /**
   * 账号
   */
  partnerId?: string | number;

  /**
   * 联调密码
   */
  secret?: string;

  /**
   * 状态（0正常 1停用）
   */
  status?: string;

}

export interface FastMailQuery extends PageQuery {

  /**
   * 类型 1 韵达
   */
  type?: string;

  /**
   * 账号
   */
  partnerId?: string | number;

  /**
   * 联调密码
   */
  secret?: string;

  /**
   * 创建时间
   */
  createTime?: string;

  /**
   * 更新时间
   */
  updateTime?: string;

  /**
   * 状态（0正常 1停用）
   */
  status?: string;

    /**
     * 日期范围参数
     */
    params?: any;
}



