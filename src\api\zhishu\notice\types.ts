export interface NoticeVO {
  /**
   * 主键
   */
  id: string | number;

  /**
   * 发件人
   */
  sender: string;

  /**
   * 收件人
   */
  recipient: string;

  /**
   * 消息
   */
  content: string;

  /**
   * 状态 0 未读  1 已读
   */
  status: string;

  /**
   * 创建时间
   */
  createTime: string;

}

export interface NoticeForm extends BaseEntity {
  /**
   * 主键
   */
  id?: string | number;

  /**
   * 发件人
   */
  sender?: string;

  /**
   * 收件人
   */
  recipient?: string;

  /**
   * 消息
   */
  content?: string;

  /**
   * 状态 0 未读  1 已读
   */
  status?: string;

}

export interface NoticeQuery extends PageQuery {

  /**
   * 发件人
   */
  sender?: string;

  /**
   * 收件人
   */
  recipient?: string;

  /**
   * 消息
   */
  content?: string;

  /**
   * 状态 0 未读  1 已读
   */
  status?: string;

  /**
   * 创建时间
   */
  createTime?: string;

    /**
     * 日期范围参数
     */
    params?: any;
}



