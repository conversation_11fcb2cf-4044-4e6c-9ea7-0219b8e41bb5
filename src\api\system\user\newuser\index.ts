// import {<PERSON>pt<PERSON>reeVO, DeptVO} from './../dept/types';
// import { RoleVO } from '@/api/system/role/types';
// import request from '@/utils/request';
// import { AxiosPromise } from 'axios';
// import { UserForm, UserQuery, UserVO, UserInfoVO } from './types';
// import { parseStrEmpty } from '@/utils/ruoyi';
//addNewUser
import { NewUserForm } from '@/api/system/user/newuser/types';
import request from '@/utils/request';

export const addNewUser = (data: NewUserForm) => {
  return request({
    url: '/system/newUser',
    method: 'post',
    data: data
  });
};
