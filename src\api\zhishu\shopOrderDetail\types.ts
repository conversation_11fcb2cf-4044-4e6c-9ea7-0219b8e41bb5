export interface ShopOrderDetailVO {
  /**
   * 
   */
  id: string | number;

  /**
   * 店铺id
   */
  shopId: string | number;

  /**
   * 店铺名称
   */
  shopName: string;

  /**
   * 订单id
   */
  orderId: string | number;

  /**
   * 商品数量
   */
  goodsCount: number;

  /**
   * 商品编码
   */
  goodsId: string | number;

  /**
   * 商品图片
   */
  goodsImg: string;

  /**
   * 商品名称
   */
  goodsName: string;

  /**
   * 商品单件 单价：元
   */
  goodsPrice: string;

  /**
   * 商品规格
   */
  goodsSpec: string;

  /**
   * 商品维度外部编码，注意：编辑商品后必须等待商品审核通过后方可生效，订单中商品信息为交易快照的商品信息
   */
  outerGoodsId: string | number;

  /**
   * sku维度商家外部编码，注意：编辑商品后必须等待商品审核通过后方可生效，订单中商品信息为交易快照的商品信息
   */
  outerId: string | number;

  /**
   * 商品sku编码
   */
  skuId: string | number;

  /**
   * 店铺状态（0正常 1停用）
   */
  status: string;

}

export interface ShopOrderDetailForm extends BaseEntity {
  /**
   * 
   */
  id?: string | number;

  /**
   * 店铺id
   */
  shopId?: string | number;

  /**
   * 店铺名称
   */
  shopName?: string;

  /**
   * 订单id
   */
  orderId?: string | number;

  /**
   * 商品数量
   */
  goodsCount?: number;

  /**
   * 商品编码
   */
  goodsId?: string | number;

  /**
   * 商品图片
   */
  goodsImg?: string;

  /**
   * 商品名称
   */
  goodsName?: string;

  /**
   * 商品单件 单价：元
   */
  goodsPrice?: string;

  /**
   * 商品规格
   */
  goodsSpec?: string;

  /**
   * 商品维度外部编码，注意：编辑商品后必须等待商品审核通过后方可生效，订单中商品信息为交易快照的商品信息
   */
  outerGoodsId?: string | number;

  /**
   * sku维度商家外部编码，注意：编辑商品后必须等待商品审核通过后方可生效，订单中商品信息为交易快照的商品信息
   */
  outerId?: string | number;

  /**
   * 商品sku编码
   */
  skuId?: string | number;

  /**
   * 店铺状态（0正常 1停用）
   */
  status?: string;

}

export interface ShopOrderDetailQuery extends PageQuery {

  /**
   * 店铺id
   */
  shopId?: string | number;

  /**
   * 店铺名称
   */
  shopName?: string;

  /**
   * 订单id
   */
  orderId?: string | number;

  /**
   * 商品数量
   */
  goodsCount?: number;

  /**
   * 商品编码
   */
  goodsId?: string | number;

  /**
   * 商品图片
   */
  goodsImg?: string;

  /**
   * 商品名称
   */
  goodsName?: string;

  /**
   * 商品单件 单价：元
   */
  goodsPrice?: string;

  /**
   * 商品规格
   */
  goodsSpec?: string;

  /**
   * 商品维度外部编码，注意：编辑商品后必须等待商品审核通过后方可生效，订单中商品信息为交易快照的商品信息
   */
  outerGoodsId?: string | number;

  /**
   * sku维度商家外部编码，注意：编辑商品后必须等待商品审核通过后方可生效，订单中商品信息为交易快照的商品信息
   */
  outerId?: string | number;

  /**
   * 商品sku编码
   */
  skuId?: string | number;

  /**
   * 店铺状态（0正常 1停用）
   */
  status?: string;

    /**
     * 日期范围参数
     */
    params?: any;
}



