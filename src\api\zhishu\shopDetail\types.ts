export interface ShopDetailVO {
  /**
   * 主键
   */
  id: string | number;

  /**
   * 店铺id
   */
  shopId: string | number;

  /**
   * 物流运费模板id
   */
  templateId: string | number;

  /**
   * 高价
   */
  highPrice: number;

  /**
   * 低价
   */
  lowPrice: number;

  /**
   * 销售模板id
   */
  saleTemplateId: string | number;

  /**
   * 是否开启7天无理由  0关闭 1开启
   */

  sevenDays: string;

  /**
   * 是否开始二手  0关闭  1开启
   */
  isSecondHand: string;

  /**
   * 轮播数量开启  0关闭 1开启
   */
  carouselNum: string;

  /**
   * 是否开启详情图  0关闭 1开启
   */
  isDetailsImg: string;

  /**
   * 是否开启目录详情  0关闭 1开启
   */
  isCatalogueDetails: string;

  /**
   * 是否开启信息图片 0关闭 1开启
   */
  isInformationImg: string;

  /**
   * 标题前缀
   */
  titlePrefix: string;

  /**
   * 标题后缀
   */
  titleSuffix: string;

  /**
   * 标题过滤
   */
  titleFilter: string;

  /**
   * 货号前缀
   */
  itemNumberPrefix: string;

  /**
   * 标题组成
   */
  titleConsistOf: string;

  /**
   * 间隔字符  0无间隔 1空格
   */
  spaceCharacter: string;

  /**
   * 是否开启自动截断  0关闭 1开启
   */
  autoTruncation: string;

  /**
   * 我的设置
   */
  mySetUp: string;

  /**
   * 系统设置
   */
  systemSetUp: string;

  /**
   * 过滤套装 0否 1是
   */
  filterSuit: string;

  /**
   * 删除保护 0关闭  1开启
   */
  isDelProtect: string;

  /**
   * 下架保护 0关闭 1开启
   */
  isRemoveProtect: string;

  /**
   * 图书定价 0关闭 1开启
   */
  bootPrice: string;

  /**
   * 自有图片 0关闭 1开启
   */
  ownPictures: string;

  /**
   * 默认库存
   */
  stockDeff: number;

  /**
   * 两件折扣
   */
  twoDiscount: number;

  /**
   * 是否预售 0非预售 1定时预售 2 时段预售
   */
  presale: string;

  /**
   * 假一赔十 0 关闭 1开启
   */
  fake: string;

  /**
   * 发货时间 0 24小时  1 48小时
   */
  deliveryTime: string;

  /**
   * 白底图 0关闭 1开启
   */
  whitePicture: string;

  /**
   * 水印位置  0全部  1第一张
   */
  watermarkPosition: string;

  /**
   * 无图过滤 0关闭 1开启
   */
  filtering: string;

  /**
   * 最低价定价 0关闭 1开启
   */
  lowerPrice: string;

  /**
   * 最低定价折扣
   */
  lowerPriceDiscount: string;

  /**
   * 拼单模板id
   */
  spellTemplateId: string | number;

  /**
   * 常驻地区id
   */
  districtId: string | number;

  /**
     * 默认品相
     */
  conditionDef: string | number;

  /**
   * 品相描述
   */
 conditionDes: string;

  /**
   * 推荐语
   */
  recommend: string;

  /**
   * 是否包邮
   */
  isParcel: string;

  /**
   * 图书模板
   */
  bookTemplate: string;

  /**
   * 图书重量
   */
  bookWeight: string | number;

  /**
   *  标准本数
   */
  standardNumber: string | number;

  /**
   * 店铺类型
   */
  shopType:string;

      /**
     * 是否自动上传  0 否 1 是
     */
  autoAdd:string;
}

export interface ShopDetailForm extends BaseEntity {
  /**
   * 主键
   */
  id?: string | number;

  /**
   * 店铺id
   */
  shopId?: string | number;

  /**
   * 物流运费模板id
   */
  templateId?: string | number;

  /**
   * 高价
   */
  highPrice?: number;

  /**
   * 低价
   */
  lowPrice?: number;

  /**
   * 销售模板id
   */
  saleTemplateId?: string | number;

  /**
   * 是否开启7天无理由  0关闭 1开启
   */

  sevenDays?: string;

  /**
   * 是否开始二手  0关闭  1开启
   */
  isSecondHand?: string;

  /**
   * 轮播数量开启  0关闭 1开启
   */
  carouselNum?: string;

  /**
   * 是否开启详情图  0关闭 1开启
   */
  isDetailsImg?: string;

  /**
   * 是否开启目录详情  0关闭 1开启
   */
  isCatalogueDetails?: string;

  /**
   * 是否开启信息图片 0关闭 1开启
   */
  isInformationImg?: string;

  /**
   * 标题前缀
   */
  titlePrefix?: string;

  /**
   * 标题后缀
   */
  titleSuffix?: string;

  /**
   * 标题过滤
   */
  titleFilter?: string;

  /**
   * 货号前缀
   */
  itemNumberPrefix?: string;

  /**
   * 标题组成
   */
  titleConsistOf?: string;

  /**
   * 间隔字符  0无间隔 1空格
   */
  spaceCharacter?: string;

  /**
   * 是否开启自动截断  0关闭 1开启
   */
  autoTruncation?: string;

  /**
   * 我的设置
   */
  mySetUp?: string;

  /**
   * 系统设置
   */
  systemSetUp?: string;

  /**
   * 过滤套装 0否 1是
   */
  filterSuit?: string;

  /**
   * 删除保护 0关闭  1开启
   */
  isDelProtect?: string;

  /**
   * 下架保护 0关闭 1开启
   */
  isRemoveProtect?: string;

  /**
   * 图书定价 0关闭 1开启
   */
  bootPrice?: string;

  /**
   * 自有图片 0关闭 1开启
   */
  ownPictures?: string;

  /**
   * 默认库存
   */
  stockDeff?: number;

  /**
   * 两件折扣
   */
  twoDiscount?: number;

  /**
   * 是否预售 0非预售 1定时预售 2 时段预售
   */
  presale?: string;

  /**
   * 假一赔十 0 关闭 1开启
   */
  fake?: string;

  /**
   * 发货时间 0 24小时  1 48小时
   */
  deliveryTime?: string;

  /**
   * 白底图 0关闭 1开启
   */
  whitePicture?: string;

  /**
   * 水印位置  0全部  1第一张
   */
  watermarkPosition?: string;

  /**
   * 无图过滤 0关闭 1开启
   */
  filtering?: string;

  /**
   * 最低价定价 0关闭 1开启
   */
  lowerPrice?: string;

  /**
   * 最低定价折扣
   */
  lowerPriceDiscount?: string;

  /**
   * 拼单模板id
   */
  spellTemplateId?: string | number;

  /**
   * 常驻地区id
   */
  districtId?: string | number;

  /**
     * 默认品相
     */
  conditionDef?: string | number;

  /**
   * 品相描述
   */
 conditionDes?: string;

  /**
   * 推荐语
   */
  recommend?: string;

  /**
   * 是否包邮
   */
  isParcel?: string;

  /**
   * 图书模板
   */
  bookTemplate?: string;

  /**
   * 图书重量
   */
  bookWeight?: string | number;

  /**
   *  标准本数
   */
  standardNumber?: string | number;

  /**
   * 店铺类型
   */
  shopType?:string;

   /**
     * 是否自动上传  0 否 1 是
     */
   autoAdd?:string;
}

export interface ShopDetailQuery extends PageQuery {

  /**
   * 店铺id
   */
  shopId?: string | number;

  /**
   * 物流运费模板id
   */
  templateId?: string | number;

  /**
   * 高价
   */
  highPrice?: number;

  /**
   * 低价
   */
  lowPrice?: number;

  /**
   * 销售模板id
   */
  saleTemplateId?: string | number;

  /**
   * 是否开启7天无理由  0关闭 1开启
   */

  sevenDays?: string;

  /**
   * 是否开始二手  0关闭  1开启
   */
  isSecondHand?: string;

  /**
   * 轮播数量开启  0关闭 1开启
   */
  carouselNum?: string;

  /**
   * 是否开启详情图  0关闭 1开启
   */
  isDetailsImg?: string;

  /**
   * 是否开启目录详情  0关闭 1开启
   */
  isCatalogueDetails?: string;

  /**
   * 是否开启信息图片 0关闭 1开启
   */
  isInformationImg?: string;

  /**
   * 标题前缀
   */
  titlePrefix?: string;

  /**
   * 标题后缀
   */
  titleSuffix?: string;

  /**
   * 标题过滤
   */
  titleFilter?: string;

  /**
   * 货号前缀
   */
  itemNumberPrefix?: string;

  /**
   * 标题组成
   */
  titleConsistOf?: string;

  /**
   * 间隔字符  0无间隔 1空格
   */
  spaceCharacter?: string;

  /**
   * 是否开启自动截断  0关闭 1开启
   */
  autoTruncation?: string;

  /**
   * 我的设置
   */
  mySetUp?: string;

  /**
   * 系统设置
   */
  systemSetUp?: string;

  /**
   * 过滤套装 0否 1是
   */
  filterSuit?: string;

  /**
   * 删除保护 0关闭  1开启
   */
  isDelProtect?: string;

  /**
   * 下架保护 0关闭 1开启
   */
  isRemoveProtect?: string;

  /**
   * 图书定价 0关闭 1开启
   */
  bootPrice?: string;

  /**
   * 自有图片 0关闭 1开启
   */
  ownPictures?: string;

  /**
   * 默认库存
   */
  stockDeff?: number;

  /**
   * 两件折扣
   */
  twoDiscount?: number;

  /**
   * 是否预售 0非预售 1定时预售 2 时段预售
   */
  presale?: string;

  /**
   * 假一赔十 0 关闭 1开启
   */
  fake?: string;

  /**
   * 发货时间 0 24小时  1 48小时
   */
  deliveryTime?: string;

  /**
   * 白底图 0关闭 1开启
   */
  whitePicture?: string;

  /**
   * 水印位置  0全部  1第一张
   */
  watermarkPosition?: string;

  /**
   * 无图过滤 0关闭 1开启
   */
  filtering?: string;

  /**
   * 最低价定价 0关闭 1开启
   */
  lowerPrice?: string;

  /**
   * 最低定价折扣
   */
  lowerPriceDiscount?: string;

  /**
   * 拼单模板id
   */
  spellTemplateId: string | number;

  /**
   * 日期范围参数
   */
  params?: any;

  /**
   * 常驻地区id
   */
  districtId?: string | number;

  /**
     * 默认品相
     */
  conditionDef?: string | number;

  /**
   * 品相描述
   */
 conditionDes?: string;

  /**
   * 推荐语
   */
  recommend?: string;

  /**
   * 是否包邮
   */
  isParcel?: string;

  /**
   * 图书模板
   */
  bookTemplate?: string;

  /**
   * 图书重量
   */
  bookWeight?: string | number;

  /**
   *  标准本数
   */
  standardNumber?: string | number;

  /**
   * 店铺类型
   */
  shopType?:string;

  /**
   * 是否自动上传  0 否 1 是
   */
  autoAdd?:string;
}



