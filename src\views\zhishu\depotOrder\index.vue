<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="处理人" prop="userId">
              <el-input v-model="queryParams.userId" placeholder="请输入处理人" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="商品信息" prop="itemList">
              <el-input v-model="queryParams.itemList" placeholder="请输入商品信息" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="创建者" prop="createBy">
              <el-input v-model="queryParams.createBy" placeholder="请输入创建者" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="创建时间" prop="createTime">
              <el-date-picker clearable
                v-model="queryParams.createTime"
                type="date"
                value-format="YYYY-MM-DD"
                placeholder="请选择创建时间"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="4">
            <el-button
              type="primary"
              :disabled="multiple"
              @click="showBatchExpressDialog"
            >批量打单</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>
      <el-table v-loading="loading"  :default-sort="{ prop: 'confirmTime', order: 'ascending' }" style="height: 600px" :data="depotOrderList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="货号" align="center" prop="orderSn" width="200px">
          <template #default="scope">
            {{JSON.parse(scope.row.itemList).itemSn}}
          </template>
        </el-table-column>
        <el-table-column label="ISBN" align="center" prop="orderSn" width="140px">
          <template #default="scope">
            {{JSON.parse(scope.row.itemList).isbn}}
          </template>
        </el-table-column>
        <el-table-column label="商品名称" align="center" prop="orderSn" width="300px">
          <template #default="scope">
            {{JSON.parse(scope.row.itemList).itemName}}
          </template>
        </el-table-column>
        <el-table-column label="快递单号" align="center" prop="trackingNumber" width="150px"/>
        <el-table-column label="订单状态" align="center" prop="orderStatus"  width="120px">
          <template #default="scope">
            <dict-tag :options="t_order_status" :value="scope.row.orderStatus" />
          </template>
        </el-table-column>

        <el-table-column label="收件人"  align="center" prop="receiverName" width="160px" />
        <el-table-column label="收件地址"  align="center" prop="receiverAddress" width="250px" />


        <el-table-column label="成交状态"  align="center" prop="confirmStatus" width="120px">
          <template #default="scope">
            <dict-tag :options="t_confirm_status" :value="scope.row.confirmStatus" />
          </template>
        </el-table-column>
        <el-table-column label="成交时间" sortable align="center" prop="confirmTime" width="160px" />
         <el-table-column label="售后状态" align="center" prop="afterSalesStatus" width="120px">
          <template #default="scope">
            <dict-tag :options="order_after_sales_status" :value="scope.row.afterSalesStatus" />
          </template>
        </el-table-column>
        <el-table-column label="支付金额(元)" align="center" prop="payAmount" width="100px"/>
        <el-table-column label="订单来源" align="center" prop="orderSourceType">
          <template #default="scope">
            <span v-if="scope.row.orderSourceType===1">拼多多</span>
            <span v-else-if="scope.row.orderSourceType===2">孔夫子</span>
          </template>
        </el-table-column>
        <el-table-column label="订单创建时间" align="center" prop="createdTime" width="150"/>
        <el-table-column label="订单类型" align="center" prop="tradeType">
          <template #default="scope">
           <span v-if="scope.row.tradeType===0">普通订单</span>
            <span v-else-if="scope.row.tradeType===1">定金订单</span>
          </template>
        </el-table-column>
        <el-table-column label="订单审核状态" align="center" prop="riskControlStatus" width="120px">
          <template #default="scope">
            <dict-tag :options="t_risk_control_status" :value="scope.row.riskControlStatus" />
          </template>
        </el-table-column>
        <el-table-column label="买家留言信息" align="center" prop="buyerMemo" width="150px"/>
        <el-table-column label="折扣金额" align="center" prop="discountAmount"/>
        <el-table-column label="支付详情" align="left" prop="duoduoWholesale" width="200">
          <template #default="scope">
            单号: <span>{{scope.row.payNo}}</span><br>
            方式: <span>{{scope.row.payType}}</span><br>
            金额: <span>{{scope.row.payAmount}}</span><br>
            时间: <span>{{scope.row.payTime}}</span>
          </template>
        </el-table-column>
        <el-table-column label="平台优惠金额" align="center" prop="platformDiscount"/>
        <el-table-column label="邮费" align="center" prop="postage"/>
        <el-table-column label="预售时间" align="center" prop="preSaleTime"/>
        <el-table-column label="承诺送达时间" align="center" prop="promiseDeliveryTime"/>
        <el-table-column label="退货包运费" align="center" prop="returnFreightPayer">
          <template #default="scope">
            <span v-if="scope.row.returnFreightPayer===0">否</span>
            <span v-else-if="scope.row.returnFreightPayer===1">是</span>
            <span v-else>未设置</span>
          </template>
        </el-table-column>
        <el-table-column label="订单的更新时间" align="center" prop="updatedAt"/>
        <el-table-column label="催发货时间" align="center" prop="urgeShippingTime"/>
        <el-table-column label="预约配送日期" align="center" prop="yypsDate"/>
        <el-table-column label="预约配送时段" align="center" prop="yypsTime"/>
        <el-table-column label="仓库信息" align="left" prop="shopName" width="150px">
          <template #default="scope">
            名称：<span>{{scope.row.depotName}}</span>
            <br>
            类型：<span v-if="scope.row.depotType===1">自有仓</span>
            <span v-else-if="scope.row.depotType===2">订阅仓</span>
            <span v-else>订阅仓</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="160px" fixed="right">
          <template #default="scope">
            <el-tooltip content="生成电子面单并发货" placement="top">
              <el-button type="primary" :disabled="scope.row.orderStatus!==1||displayStatus" @click="showExpressDialog(scope.row, 'ship')">发货</el-button>
            </el-tooltip>
            <el-tooltip content="打印快递单" placement="top">
              <el-button type="primary" :disabled="scope.row.orderStatus!==2||displayStatus" @click="showExpressDialog(scope.row, 'print')">打印</el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>

    <!-- 添加或修改仓库订单信息对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body>
      <el-form ref="depotOrderFormRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="店铺订单id" prop="shopOrderId">
          <el-input v-model="form.shopOrderId" placeholder="请输入店铺订单id" />
        </el-form-item>
        <el-form-item label="处理人" prop="userId">
          <el-input v-model="form.userId" placeholder="请输入处理人" />
        </el-form-item>
        <el-form-item label="商品信息" prop="itemList">
            <el-input v-model="form.itemList" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 快递公司选择对话框 -->
    <el-dialog title="选择快递公司" v-model="expressDialog.visible" width="400px" append-to-body>
      <el-form>
        <el-form-item label="快递公司">
          <el-select v-model="expressType" placeholder="请选择快递公司">
            <el-option label="韵达快递" value="1"></el-option>
            <el-option label="圆通快递" value="2"></el-option>
            <el-option label="申通快递" value="3"></el-option>
            <el-option label="中通快递" value="4"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="confirmExpressSelection">确 定</el-button>
          <el-button @click="expressDialog.visible = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 批量打单快递公司选择对话框 -->
    <el-dialog title="批量打单" v-model="batchExpressDialog.visible" width="400px" append-to-body>
      <el-form>
        <el-form-item label="快递公司">
          <el-select v-model="expressType" placeholder="请选择快递公司">
            <el-option label="韵达快递" value="1"></el-option>
            <el-option label="圆通快递" value="2"></el-option>
            <el-option label="申通快递" value="3"></el-option>
            <el-option label="中通快递" value="4"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="打印模式">
          <el-radio-group v-model="batchPrintMode">
            <el-radio :label="1">预览</el-radio>
            <el-radio :label="2">直接打印</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="confirmBatchPrint">确 定</el-button>
          <el-button @click="batchExpressDialog.visible = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="DepotOrder" lang="ts">
import { listDepotOrder, getDepotOrder, delDepotOrder, addDepotOrder, updateDepotOrder, getMailNo, getPdf, getMailNoByYuanTong, getPdfByYuanTong, getMailNoByShenTong, getPdfByShenTong, getMailNoByZhongTong, getPdfByZhongTong } from '@/api/zhishu/depotOrder';
import { DepotOrderVO, DepotOrderQuery, DepotOrderForm } from '@/api/zhishu/depotOrder/types';
import { createPrintTask } from '@/api/zhishu/daYin';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const {
  t_order_status,
  order_after_sales_status,
  t_confirm_status,
  t_risk_control_status,
  t_yes_no
} = toRefs<any>(
  proxy?.useDict(
    't_order_status',
    'order_after_sales_status',
    't_confirm_status',
    't_risk_control_status',
    't_yes_no'
  )
);

const depotOrderList = ref<DepotOrderVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const displayStatus = ref(false); // 修复displayStatus未定义的错误
const expressType = ref('1'); // 1:韵达 2:圆通 3: 申通
const currentRow = ref(null); // 当前操作的行
const actionType = ref(''); // 'ship' 或 'print'
const batchPrintMode = ref(1); // 1: 预览 2: 直接打印
const selectedRows = ref<DepotOrderVO[]>([]); // 选中的行数据

const queryFormRef = ref<ElFormInstance>();
const depotOrderFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const expressDialog = reactive({
  visible: false
});

const batchExpressDialog = reactive({
  visible: false
});

const initFormData: DepotOrderForm = {
  id: undefined,
  shopOrderId: undefined,
  userId: undefined,
  itemList: undefined,
  status: undefined,
  shopId: undefined,
  shopName: undefined,
  address: undefined,
  addressMask: undefined,
  afterSalesStatus: undefined,
  buyerMemo: undefined,
  confirmStatus: undefined,
  confirmTime: undefined,
  createdTime: undefined,
  deliveryOneDay: undefined,
  discountAmount: undefined,
  duoDuoPayReduction: undefined,
  duoduoWholesale: undefined,
  goodsAmount: undefined,
  depotCode: undefined,
  depotId: undefined,
  depotName: undefined,
  depotType: undefined,
  wareId: undefined,
  wareName: undefined,
  wareSn: undefined,
  wareType: undefined,
  orderSn: undefined,
  orderStatus: undefined,
  payAmount: undefined,
  payNo: undefined,
  payTime: undefined,
  payType: undefined,
  platformDiscount: undefined,
  postage: undefined,
  preSaleTime: undefined,
  promiseDeliveryTime: undefined,
  province: undefined,
  provinceId: undefined,
  receiveTime: undefined,
  receiverAddress: undefined,
  receiverAddressMask: undefined,
  receiverName: undefined,
  receiverNameMask: undefined,
  receiverPhone: undefined,
  receiverPhoneMask: undefined,
  refundStatus: undefined,
  remark: undefined,
  remarkTag: undefined,
  remarkTagName: undefined,
  returnFreightPayer: undefined,
  riskControlStatus: undefined,
  selfContained: undefined,
  sellerDiscount: undefined,
  stockOutHandleStatus: undefined,
  supportNationwideWarranty: undefined,
  town: undefined,
  townId: undefined,
  trackingNumber: undefined,
  tradeInNationalSubsidyAmount: undefined,
  tradeType: undefined,
  updatedAt: undefined,
  urgeShippingTime: undefined,
  yypsDate: undefined,
  yypsTime: undefined,
  openAddressId2: undefined,
  orderExceptionTypeList:undefined,
  orderItemList:undefined,
  artNo:undefined,
  orderSourceType:undefined
}
const data = reactive<PageData<DepotOrderForm, DepotOrderQuery>>({
  form: {...initFormData},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    shopOrderId: undefined,
    userId: undefined,
    itemList: undefined,
    status: undefined,
    createBy: undefined,
    createTime: undefined,
    shopId: undefined,
    shopName: undefined,
    address: undefined,
    addressMask: undefined,
    afterSalesStatus: undefined,
    buyerMemo: undefined,
    confirmStatus: undefined,
    confirmTime: undefined,
    createdTime: undefined,
    deliveryOneDay: undefined,
    discountAmount: undefined,
    duoDuoPayReduction: undefined,
    duoduoWholesale: undefined,
    goodsAmount: undefined,
    depotCode: undefined,
    depotId: undefined,
    depotName: undefined,
    depotType: undefined,
    wareId: undefined,
    wareName: undefined,
    wareSn: undefined,
    wareType: undefined,
    orderSn: undefined,
    orderStatus: undefined,
    payAmount: undefined,
    payNo: undefined,
    payTime: undefined,
    payType: undefined,
    platformDiscount: undefined,
    postage: undefined,
    preSaleTime: undefined,
    promiseDeliveryTime: undefined,
    province: undefined,
    provinceId: undefined,
    receiveTime: undefined,
    receiverAddress: undefined,
    receiverAddressMask: undefined,
    receiverName: undefined,
    receiverNameMask: undefined,
    receiverPhone: undefined,
    receiverPhoneMask: undefined,
    refundStatus: undefined,
    remarkTag: undefined,
    remarkTagName: undefined,
    returnFreightPayer: undefined,
    riskControlStatus: undefined,
    selfContained: undefined,
    sellerDiscount: undefined,
    stockOutHandleStatus: undefined,
    supportNationwideWarranty: undefined,
    town: undefined,
    townId: undefined,
    trackingNumber: undefined,
    tradeInNationalSubsidyAmount: undefined,
    tradeType: undefined,
    updatedAt: undefined,
    urgeShippingTime: undefined,
    yypsDate: undefined,
    yypsTime: undefined,
    openAddressId2: undefined,
    orderExceptionTypeList:undefined,
    orderSourceType:undefined,
    confirmTimeList: undefined,
    createdTimeStart: undefined,
    createdTimeEnd: undefined,
    params: {}
  },
  rules: {
    id: [
      { required: true, message: "主键不能为空", trigger: "blur" }
    ],
  }
});

const { queryParams, form, rules } = toRefs(data);

// LODOP打印对象类型定义
interface LODOPObject {
  PRINT: () => void;
  PREVIEW: () => void;
  [key: string]: any;
}

/** 查询仓库订单信息列表 */
const getList = async () => {
  loading.value = true;
  const res = await listDepotOrder(queryParams.value as DepotOrderQuery);
  depotOrderList.value = res.rows;
  console.log(depotOrderList,"======================")
  total.value = res.total;
  loading.value = false;
}

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
}

/** 表单重置 */
const reset = () => {
  form.value = {...initFormData};
  depotOrderFormRef.value?.resetFields();
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
}

/** 多选框选中数据 */
const handleSelectionChange = (selection: DepotOrderVO[]) => {
  ids.value = selection.map(item => item.id);
  selectedRows.value = selection;
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = "添加仓库订单信息";
}

/** 修改按钮操作 */
const handleUpdate = async (row?: DepotOrderVO) => {
  reset();
  const _id = row?.id || ids.value[0]
  const res = await getDepotOrder(_id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = "修改仓库订单信息";
}

/** 提交按钮 */
const submitForm = () => {
  depotOrderFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateDepotOrder(form.value).finally(() =>  buttonLoading.value = false);
      } else {
        await addDepotOrder(form.value).finally(() =>  buttonLoading.value = false);
      }
      proxy?.$modal.msgSuccess("操作成功");
      dialog.visible = false;
      await getList();
    }
  });
}

/** 删除按钮操作 */
const handleDelete = async (row?: DepotOrderVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除仓库订单信息编号为"' + _ids + '"的数据项？').finally(() => loading.value = false);
  await delDepotOrder(_ids);
  proxy?.$modal.msgSuccess("删除成功");
  await getList();
}

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download('zhishu/depotOrder/export', {
    ...queryParams.value
  }, `depotOrder_${new Date().getTime()}.xlsx`)
}

const colorOptions = [
  { value: '1', label: 'red' },
  { value: '2', label: 'yellow' },
  { value: '3', label: 'green' },
  { value: '4', label: 'blue' },
  { value: '5', label: 'purple' }
];
const getTagColor = (tag) => {
  return colorOptions.find(item  => item.value  === String(tag))?.label || 'gray';
};

/**
 * 发货
 */
const handleShipments = async (row) => {
  try {
    let base64Pdf;
    let expressName = '韵达';

    if (expressType.value === '1') {
      // 韵达快递
      base64Pdf = await getMailNo(row.id);
      expressName = '韵达';
    } else if (expressType.value === '2') {
      // 圆通快递
      base64Pdf = await getMailNoByYuanTong(row.id);
      expressName = '圆通';
    } else if (expressType.value === '3') {
      // 申通快递
      base64Pdf = await getMailNoByShenTong(row.id);
      expressName = '申通';
    }else if (expressType.value === '4'){
      // 中通快递
      base64Pdf = await getMailNoByZhongTong(row.id);
      expressName = '中通';
    }

    const preview = true;
    const pdfMark = base64Pdf.msg;

    try {
      const LODOP = await createPrintTask(
        expressType.value === '1' ? 'yunda' :
        expressType.value === '2' ? 'yuantong' :
        expressType.value === '3' ? 'shentong' : 'zhongtong',
        pdfMark
      ) as LODOPObject;

      if (preview) {
        LODOP.PREVIEW();
      } else {
        LODOP.PRINT();
      }
      proxy?.$modal.msgSuccess(`${expressName}快递单号生成成功，已发货`);
      await getList(); // 刷新列表
    } catch (error: any) {
      proxy?.$modal.msgError(`${expressName}快递单打印失败: ${error?.message || '未知错误'}`);
    }
  } catch (error: any) {
    let expressTypeName = '韵达';
    if (expressType.value === '2') expressTypeName = '圆通';
    if (expressType.value === '3') expressTypeName = '申通';

    proxy?.$modal.msgError(`获取${expressTypeName}快递单号失败: ${error?.message || '未知错误'}`);
  }
}

/**
 * 打印
 */
const handleShipmentsPdf = async (row) => {
  try {
    let base64Pdf;
    let expressName = '韵达';

    // 保存当前订单的商品信息到localStorage
    try {
      const itemData = JSON.parse(row.itemList);
      const customData = {
        itemName: itemData.itemName || '',
        isbn: itemData.isbn || '',
        quality: itemData.quality || '标准',
        number: itemData.number || '1'
      };
      window.localStorage.setItem('currentOrderCustomData', JSON.stringify(customData));
    } catch (e) {
      console.error('保存商品信息失败', e);
    }

    if (expressType.value === '1') {
      // 韵达快递
      base64Pdf = await getPdf(row.trackingNumber);
      expressName = '韵达';
    } else if (expressType.value === '2') {
      // 圆通快递 - 为row.waybillNo赋值固定的测试单号
      row.waybillNo = 'YT2819013465880';
      base64Pdf = await getPdfByYuanTong(row.waybillNo);
      expressName = '圆通';
    } else if (expressType.value === '3') {
      // 申通快递 - 为row.waybillNo赋值固定的测试单号
      row.waybillNo = 'ST2819013465880';
      base64Pdf = await getPdfByShenTong(row.waybillNo);
      expressName = '申通';
    } else if (expressType.value === '4'){
      row.waybillNo = '73100130019486';
      base64Pdf = await getPdfByZhongTong(row.waybillNo);
    }

    const preview = true;
    const pdfMark = base64Pdf.msg;

    try {
      const LODOP = await createPrintTask(expressType.value === '1' ? 'yunda' : 'yuantong', pdfMark) as LODOPObject;

      if (preview) {
        LODOP.PREVIEW();
      } else {
        LODOP.PRINT();
      }
      proxy?.$modal.msgSuccess(`${expressName}快递单打印成功`);
    } catch (error: any) {
      proxy?.$modal.msgError(`${expressName}快递单打印失败: ${error?.message || '未知错误'}`);
    }
  } catch (error: any) {
    proxy?.$modal.msgError(`获取${expressType.value === '1' ? '韵达' : '圆通'}快递单PDF失败: ${error?.message || '未知错误'}`);
  }
}

function base64ToBlob(base64, mimeType) {
  const sliceSize = 1024;
  const byteCharacters = atob(base64.split(',')[1]);
  const bytesLength = byteCharacters.length;
  const slicesCount = Math.ceil(bytesLength / sliceSize);
  const byteArrays = new Array(slicesCount);

  for (let sliceIndex = 0; sliceIndex < slicesCount; ++sliceIndex) {
    const begin = sliceIndex * sliceSize;
    const end = Math.min(begin + sliceSize, bytesLength);

    const bytes = new Array(end - begin);
    for (let offset = begin, i = 0; offset < end; ++i, ++offset) {
      bytes[i] = byteCharacters[offset].charCodeAt(0);
    }
    byteArrays[sliceIndex] = new Uint8Array(bytes);
  }

  return new Blob(byteArrays, { type: mimeType });
}

// 显示快递公司选择对话框
const showExpressDialog = (row, type) => {
  currentRow.value = row;
  actionType.value = type;
  expressDialog.visible = true;
};

// 显示批量打单快递公司选择对话框
const showBatchExpressDialog = () => {
  if (selectedRows.value.length === 0) {
    proxy?.$modal.msgError("请至少选择一条记录");
    return;
  }
  batchExpressDialog.visible = true;
};

// 确认快递公司选择
const confirmExpressSelection = () => {
  expressDialog.visible = false;
  if (actionType.value === 'ship') {
    handleShipments(currentRow.value);
  } else if (actionType.value === 'print') {
    handleShipmentsPdf(currentRow.value);
  }
};

// 确认批量打单
const confirmBatchPrint = async () => {
  batchExpressDialog.visible = false;

  if (selectedRows.value.length === 0) {
    proxy?.$modal.msgError("请至少选择一条记录");
    return;
  }

  const preview = batchPrintMode.value === 1;
  const expressName = expressType.value === '1' ? '韵达' : '圆通';
  const templateName = expressType.value === '1' ? 'yunda' : 'yuantong';

  try {
    loading.value = true;
    proxy?.$modal.msgWarning(`正在处理${selectedRows.value.length}条记录的批量打单请求，请稍候...`);

    // 处理每一条选中的记录
    for (let i = 0; i < selectedRows.value.length; i++) {
      const row = selectedRows.value[i];

      // 只处理已发货的订单（状态为2）
      if (row.orderStatus !== 2) {
        continue;
      }

      try {
        // 保存当前订单的商品信息到localStorage
        try {
          const itemData = JSON.parse(row.itemList);
          const customData = {
            itemName: itemData.itemName || '',
            isbn: itemData.isbn || '',
            quality: itemData.quality || '标准',
            number: itemData.number || '1'
          };
          window.localStorage.setItem('currentOrderCustomData', JSON.stringify(customData));
        } catch (e) {
          console.error('保存商品信息失败', e);
        }

        let base64Pdf;

        if (expressType.value === '1') {
          // 韵达快递
          base64Pdf = await getPdf(row.trackingNumber);
        } else if (expressType.value === '2') {
          // 圆通快递 - 使用trackingNumber作为waybillNo
          const waybillNo = row.trackingNumber || 'YT2819013465880'; // 使用实际单号或测试单号
          base64Pdf = await getPdfByYuanTong(waybillNo);
        } else if (expressType.value === '3') {
          // 申通快递 - 使用trackingNumber作为waybillNo
          const waybillNo = row.trackingNumber || 'ST2819013465880'; // 使用实际单号或测试单号
          base64Pdf = await getPdfByShenTong(waybillNo);
        }

        const pdfMark = base64Pdf.msg;
        const LODOP = await createPrintTask(templateName, pdfMark) as LODOPObject;

        if (preview) {
          LODOP.PREVIEW();
          // 批量预览模式下，只预览第一个，避免打开太多窗口
          if (i === 0) {
            proxy?.$modal.msgSuccess(`正在预览第一个快递单，共选择了${selectedRows.value.length}个订单`);
          }
          break;
        } else {
          LODOP.PRINT();
        }
      } catch (error: any) {
        proxy?.$modal.msgError(`处理订单 ${row.id} 时出错: ${error?.message || '未知错误'}`);
      }
    }

    loading.value = false;
    if (!preview) {
      proxy?.$modal.msgSuccess(`${expressName}快递单批量打印完成`);
    }
  } catch (error: any) {
    loading.value = false;
    proxy?.$modal.msgError(`批量打单失败: ${error?.message || '未知错误'}`);
  }
};

onMounted(() => {
  getList();
});


</script>
