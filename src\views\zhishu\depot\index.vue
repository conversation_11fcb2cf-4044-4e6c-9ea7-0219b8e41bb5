<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter"
      :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="一级货区编号" prop="code" label-width="100px">
              <el-input v-model="queryParams.code" placeholder="请输入一级货区编号" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="名称" prop="name">
              <el-input v-model="queryParams.name" placeholder="请输入名称" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="一级货区地址" prop="address" label-width="100px">
              <el-input v-model="queryParams.address" placeholder="请输入一级货区地址" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="管理员" prop="manager" label-width="90">
              <el-input v-model="queryParams.manager" placeholder="请输入一级货区管理员" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd"
              v-hasPermi="['depot:depot:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()"
              v-hasPermi="['depot:depot:edit']">修改</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()"
              v-hasPermi="['depot:depot:remove']">删除</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport"
              v-hasPermi="['depot:depot:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="depotList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="ID" align="center" prop="id" width="120" :show-overflow-tooltip="true">
            <template #default="{ row }">
              <div class="truncate-cell"> <!-- 文本截断容器 -->
                {{ row.id }}
              </div>
            </template>
        </el-table-column>

        <el-table-column label="用户" align="center">
          <template #default="{ row }">
            <!-- 修正1：使用动态参数拼接 -->
            <el-link type="primary" :href="`/user?id=${row.userId}`" target="_blank">
              {{ row.userName }}
            </el-link>
          </template>
        </el-table-column>
        <el-table-column label="一级货区编号" align="center" prop="code" />


        <el-table-column label="名称" align="center" prop="name" />
        <el-table-column label="二级货区数量" align="center" prop="sheQuantityMax" />
        <el-table-column label="二级货区已用数量" align="center" prop="sheNumber" />
        <el-table-column label="一级货区地址" align="center" prop="address" />
        <el-table-column label="管理员" align="center" prop="manager" />
        <el-table-column label="货区状态" align="center">
          <template #default="{ row }">
            <el-tag :type="row.status === '0' ? 'success' : 'danger'" :effect="row.status === '0' ? 'dark' : 'light'"
              size="large">
              {{ row.status === '0' ? '正常' : '停止' }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="备注" align="center" prop="remark" />

        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)"
                v-hasPermi="['depot:depot:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)"
                v-hasPermi="['depot:depot:remove']"></el-button>
            </el-tooltip>
            <el-tooltip content="运费模板" placement="top">
              <el-button link type="primary" icon="Document" @click="handleFreightTemplate(scope.row)"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改仓库信息设置对话框 -->


    <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body>
      <el-form ref="depotFormRef" :model="form" :rules="rulesFrom" label-width="80px">
        <el-form-item label="名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入名称">
            <template #append>
                <el-select v-model="selected" placeholder="" style="width: 60px" @change="handleLocationTypeChange">
                    <el-option v-for="item in locationTypes" :key="item.value" :label="item.label" :value="item.value"/>
                </el-select>
            </template>
          </el-input>
        </el-form-item>

        <el-form-item label="货区数量" prop="sheQuantityMax" :rules="SheRules">
          <el-input v-model="form.sheQuantityMax" placeholder="请输入最大货区数量(纯数字,不得以0开头,不得超过3位)"/>
        </el-form-item>
        <el-form-item label="货区编码" prop="code">
          <el-input v-model="form.code" placeholder="请输入货区编码,格式示例: AA/A1/1A/,不能以0开头" />
        </el-form-item>
        <el-form-item label="货区状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio :label="0" border size="large" value="0">正常</el-radio>
            <el-radio :label="1" border size="large" value="1">停止</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="货区地址" prop="address">
          <el-input v-model="form.address" placeholder="请输入货区地址，例如 沈阳市和平区XXX街XX号" />
        </el-form-item>
        <el-form-item label="管理员" prop="manager">
          <el-input v-model="form.manager" placeholder="请输入管理员,不得超过8位" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" placeholder="请输入备注" />
        </el-form-item>

      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

<!--    运费模版选择-->
    <el-dialog title="运费模板选择" v-model="freightDialog.visible" width="500px" append-to-body>
      <el-form-item label="模版选择" prop="id" >
        <el-select v-model="templateId"  value-key="id" placeholder="请选择模版" :reserve-keyword="false" clearable filterable style="width: 100%" :loading="loading"  @update:model-value="handleDepotChange">
          <el-option v-for="item in templateList" :key="item.id" :label="item.templateName" :value="item"  />
        </el-select>
      </el-form-item>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submit1">确 定</el-button>
          <el-button @click="cancelFreightTemplate">取 消</el-button>
        </div>
      </template>
    </el-dialog>

  </div>





</template>

<script setup name="Depot" lang="ts">
import { listDepot, getDepot, delDepot, addDepot, updateDepot } from '@/api/zhishu/depot';
import { DepotVO, DepotQuery, DepotForm } from '@/api/zhishu/depot/types';
import { TDistrictVo } from '@/api/zhishu/district/types';
import { ref } from 'vue';
import { templateNameList } from '@/api/zhishu/logistics';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const depotList = ref<DepotVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
let curruntRow = null;

const queryFormRef = ref<ElFormInstance>();
const depotFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: '',
  isAdd: true // 默认是新增操作
});

interface DepotOption {
  id: number
  templateName: string
}

const templateId = ref<DepotOption | null>(null) // 严格匹配选项类型
const templateList = ref<DepotVO[]>([])

//获取运费模版名称和id
const loadData = async () => {
  loading.value  = true
  try {
    const res = await   templateNameList()
    templateList.value=res.rows
    // return res.rows  || []
  } catch (error) {
  } finally {
    loading.value  = false
  }
}
const handleDepotChange = (val: DepotOption | null) => {
  form.value.id  = val?.id || null
  form.value.depotName  = val?.templateName || ''
}


// 运费模板对话框状态
const freightDialog = reactive({
  visible: false,
  title: '运费模板设置'
});

// 运费模板表单数据
const freightForm = reactive({
  templateName: '',
  deliveryArea: [],
  pricingMethod: 'weight',
  deliveryMethod: 'express',
  deliveryNote: ''
});

// 类型选项配置
const locationTypes = [
  { value: '库', label: '库' },
  { value: '架', label: '架' },
  { value: '层', label: '层' },
  { value: '位', label: '位' }
];


// 格式化节点数据
const formatNode = (level: number, isLeaf = false) => (item: TDistrictVo) => ({
  value: item.id,
  label: item.name,
  leaf: isLeaf,
  level: level
});

/** 取消运费模板 */
const cancelFreightTemplate = () => {
  freightDialog.visible = false;
};

let selected=ref<string>("");

// 表单验证规则
const rulesFrom = reactive({
  name: [
    { required: true, message: '货区名称不能为空', trigger: 'blur' },
    { max: 10, message: '货区名称长度不能超过10位', trigger: 'blur' }

  ],
  manager: [
    { required: true, message: '管理员不能为空', trigger: 'blur' },
    { max: 8, message: '管理员姓名长度不能超过8位', trigger: 'blur' }
  ],
  code:[
    { required: true, message: '获取编码不能为空', trigger: 'blur' },
    {
      pattern: /^(?:[A-Z]{2}|[a-z]{2}|[A-Z][0-9]|[a-z][0-9]|[1-9][a-zA-Z])$/i,
      message: '格式示例: AA/aa/A1/a1/1A/1a,不能以0开头',
      trigger: 'blur'
    }
  ]
});


const SheRules = computed(() => {
  const value = form.value.sheQuantityMax;

  return [
    {
      required: true,
      message: '请输入最大货区数量',
      trigger: ['blur', 'change'] // 同时监听输入变化
    },
    {
      validator: (_, val, callback) => {
        const num = Number(val);
        if (isNaN(num)) {
          callback(new Error('必须输入数字'));
        } else if (num > 255) {
          callback(new Error('不得超过255'));
        } else if (/^0\d+/.test(val)) {
          callback(new Error('不能以0开头'));
        } else {
          callback();
        }
      },
      trigger: 'blur'
    }
  ];
});



const initFormData: DepotForm = {
  id: undefined,
  code: undefined,
  name: undefined,
  unit:"库",
  address: undefined,
  manager: undefined,
  status: undefined,
  remark: undefined,
  sheQuantityMax: undefined,
  sheNumber:undefined,
  userName: undefined,
  userId: undefined
}
const data = reactive<PageData<DepotForm, DepotQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    code: undefined,
    name: undefined,
    unit:undefined,
    address: undefined,
    sheNumber:undefined,
    manager: undefined,
    status: undefined,
    userName: undefined,
    params: {
    }
  }
});
const { queryParams, form } = toRefs(data);



// 运费模板表单数据
const tForm = reactive({
  id: undefined,
  code: undefined,
  name: undefined,
  unit:"库",
  address: undefined,
  manager: undefined,
  status: undefined,
  remark: undefined,
  sheQuantityMax: undefined,
  sheNumber:undefined,
  userName: undefined,
  userId: undefined,
  templateId:undefined,
});


const handleFreightTemplate = async (row?: DepotVO) => {
  freightDialog.visible = true;
  reset();
  const _id = row?.id || ids.value[0]
  const res = await getDepot(_id);
  Object.assign(tForm, res.data);
  freightDialog.visible = true;
  freightDialog.isAdd = false; // 设置为修改操作
}

/** 提交按钮 */
const submit1 = () => {

  reset();

  tForm.templateId = templateId.value?.id  || null  // 解构基础类型值
  updateDepot(tForm).finally(() => buttonLoading.value = false);

  proxy?.$modal.msgSuccess("操作成功");
  freightDialog.visible = false;
}

const handleLocationTypeChange=async(val)=>{
  form.value.unit=val
}

/** 查询仓库信息设置列表 */
const getList = async () => {
  loading.value = true;
  const res = await listDepot(queryParams.value);
  depotList.value = res.rows;
  total.value = res.total;
  loading.value = false;
}

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
}

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  depotFormRef.value?.resetFields();
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
}

/** 多选框选中数据 */
const handleSelectionChange = (selection: DepotVO[]) => {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = "添加一级货区信息设置";
  dialog.isAdd = true; // 设置为新增操作
}

/** 修改按钮操作 */
const handleUpdate = async (row?: DepotVO) => {
  reset();
  const _id = row?.id || ids.value[0]
  const res = await getDepot(_id);
 selected.value = res.data.unit;
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = "修改一级货区信息设置";
  dialog.isAdd = false; // 设置为修改操作
}

/** 提交按钮 */
const submitForm = () => {
  depotFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateDepot(form.value).finally(() => buttonLoading.value = false);
      } else {
        await addDepot(form.value).finally(() => buttonLoading.value = false);
      }
      proxy?.$modal.msgSuccess("操作成功");
      dialog.visible = false;
      await getList();
    }
  });
}

/** 删除按钮操作 */
const handleDelete = async (row?: DepotVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除仓库信息设置编号为"' + _ids + '"的数据项？').finally(() => loading.value = false);
  await delDepot(_ids);
  proxy?.$modal.msgSuccess("删除成功");
  await getList();
}

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download('depot/depot/export', {
    ...queryParams.value
  }, `depot_${new Date().getTime()}.xlsx`)
}

onMounted(() => {
  getList();
  loadData();
});
</script>
<style>
/* 文本溢出处理 */
.truncate-cell {
  width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 增强悬浮提示样式 */
.el-tooltip__popper {
  max-width: 400px;
  word-break: break-all;
}

/* 运费模板样式 */
.freight-template-container {
  padding: 10px;
}

.overseas-section {
  margin-top: 20px;
  border-top: 1px solid #ebeef5;
  padding-top: 15px;
}
</style>
