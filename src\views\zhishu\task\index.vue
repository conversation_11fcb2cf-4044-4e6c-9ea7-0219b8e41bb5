<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="任务类型" prop="taskType">
              <el-select v-model="queryParams.taskType" placeholder="请选择任务类型" clearable>
                <el-option v-for="dict in t_task_type" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="店铺名称" prop="shopIds">
              <el-input v-model="queryParams.shopIds" placeholder="请输入店铺" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="文件名称" prop="fileName">
              <el-input v-model="queryParams.fileName" placeholder="请输入文件名称" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="任务状态" prop="taskStatus">
              <el-select v-model="queryParams.taskStatus" placeholder="请选择任务状态" clearable>
                <el-option v-for="dict in t_task_status" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['zhishu:task:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['zhishu:task:remove']"
              >删除</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['zhishu:task:export']">导出</el-button>
          </el-col>
          <!-- <el-col :span="1.5">
            <el-button type="success" plain icon="Upload" :disabled="multiple"  @click="dialogVisible = true" v-hasPermi="['zhishu:task:tbstock']">同步仓库</el-button>
          </el-col> -->
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="taskList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="任务编码" align="center" prop="id" v-if="true" />
        <el-table-column label="任务类型" align="center" prop="taskType">
          <template #default="scope">
            <dict-tag :options="t_task_type" :value="scope.row.taskType" />
          </template>
        </el-table-column>
        <el-table-column label="店铺名称" align="center" prop="shopNames" />
        <el-table-column label="文件名称" align="center" prop="fileName" width="250"/>
        <el-table-column label="待执行/已完成" align="center" prop="dataNum" >
          <template #default="scope">
            <span>{{   scope.row.waitCount }} /  {{ scope.row.successCount}}</span>
          </template>
        </el-table-column>
        <el-table-column label="任务状态" align="center">
          <template #default="scope">
            <span v-if="scope.row.waitCount === 0 && scope.row.successCount === 0">未执行</span>
            <span v-else-if="scope.row.waitCount === 0">完成</span>
            <span v-else>执行中</span>
          </template>
        </el-table-column>
         <el-table-column label="创建时间" align="center" prop="createTime" width="200">
          <template #default="scope">
            <span>{{ proxy.parseTime(scope.row.createTime) }}</span>
          </template>
        </el-table-column>

        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="导入" placement="top"  >
              <el-button link type="primary" icon="Download"  v-if=" scope.row.taskType === 'SYNC_GOODS_KFZ'||scope.row.taskType==='SYNC_GOODS_WLN'"
                         @click="artConRules(scope.row)"  :disabled="!scope.row.hasSyncFile" ></el-button>
            </el-tooltip>
            <el-tooltip content="日志" placement="top" >
              <el-button link type="primary" icon="Memo" @click="shopSetUp(scope.row)"></el-button>
            </el-tooltip>
            <!-- <el-tooltip content="停止" placement="top">
              <el-button :class="{isHide : scope.row.waitCount === 0 || scope.row.taskType === 'SYNC_GOODS_KFZ' || scope.row.taskType === 'UPDATE_GOODS_PRICE'}" link type="primary" icon="VideoPause" @click="editThread(scope.row,2)"></el-button>
            </el-tooltip> -->
            <el-tooltip content="暂停" placement="top">
              <el-button :class="{isHide : scope.row.waitCount === 0 || scope.row.pauseCount != 0 || scope.row.taskType === 'SYNC_GOODS_KFZ' || scope.row.taskType === 'UPDATE_GOODS_PRICE' }" link type="primary" icon="VideoPause" @click="editThread(scope.row,0)"></el-button>
            </el-tooltip>
            <el-tooltip content="恢复" placement="top">
              <el-button :class="{isHide : scope.row.waitCount === 0 || scope.row.pauseCount === 0 || scope.row.taskType === 'SYNC_GOODS_KFZ' || scope.row.taskType === 'UPDATE_GOODS_PRICE' }" link type="primary" icon="VideoPlay" @click="editThread(scope.row,1)"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['zhishu:task:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改任务列表对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="900px" append-to-body>
      <el-form ref="taskFormRef" :model="formRelease" :rules="rulesRelease" label-width="80px" style="height:600px">
        <el-form-item label="任务类型" prop="taskType">
          <el-select v-model="formRelease.taskType" placeholder="请选择任务类型">
            <el-option v-for="dict in t_task_type" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="更新方式" prop="way" label-width="100px" style="width: 100%">
          <el-radio-group v-model="formRelease.way">
            <el-radio v-for="dict in t_update_method" :key="dict.value" :value="dict.value">{{ dict.label }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <div style="margin: -18px 0px 14px 101px; color: #999">对已上传数据发现isbn重复之后的处理方式</div>

        <el-form-item label="上架状态" prop="listStatus" label-width="100px" style="width: 100%">
          <el-radio-group v-model="formRelease.listStatus">
            <el-radio v-for="dict in t_list_status" :key="dict.value" :value="dict.value">{{ dict.label }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <div style="margin: -18px 0px 14px 101px; color: #999">发布或更新商品的时候，设置商品的上下架状态，新品一定不能选下架（闲鱼除外）</div>

        <el-form-item label="图书类目" prop="bookCategory" label-width="100px" style="width: 100%">
          <el-radio-group v-model="formRelease.bookCategory">
            <el-radio v-for="dict in t_book_category" :key="dict.value" :value="dict.value">{{ dict.label }}</el-radio>
          </el-radio-group>
        </el-form-item>

        <div style="margin: -18px 0px 14px 101px; color: #999">如果选择虚拟类目，则采用核销完成订单</div>
        <el-form-item :class="{ isHide: formRelease.bookCategory == 0 }" label="指定类目" prop="bookCategoryAppoint" label-width="100px" style="width: 100%">
          <el-cascader :options="catsList" v-model="formRelease.bookCategoryAppoint" :show-all-levels="false" />
        </el-form-item>

<!--        <el-form-item label="店铺选择" prop="shopIds" label-width="100px" style="width: 100%">-->
          <el-form-item label="孔夫子店铺:" prop="shopIds" label-width="100px" style="width: 100%">
            <!-- 孔夫子（整行） -->
            <el-checkbox-group v-model="formRelease.shopIds" style="flex: 1;">
              <el-checkbox
                v-for="item in kfzShopList"
                :key="item.value"
                :label="item.value"
                style="margin-right: 15px;"
              >
                {{ item.label }}
              </el-checkbox>
            </el-checkbox-group>
          </el-form-item>
          <el-form-item label="拼多多店铺:" prop="shopIds" label-width="100px" style="width: 100%">
            <!-- 拼多多（整行） -->
            <el-checkbox-group v-model="formRelease.shopIds" style="flex: 1;">
              <el-checkbox
                v-for="item in pddShopList"
                :key="item.value"
                :label="item.value"
                style="margin-right: 15px;"
              >
                {{ item.label }}
              </el-checkbox>
            </el-checkbox-group>
          </el-form-item>
<!--        </el-form-item>-->

        <el-form-item label="文件选择" prop="fileUrl" label-width="100px" style="width: 100%">
          <el-row>
            <el-col :span="14">
              <el-upload
                ref="upload"
                class="upload-demo"
                :action="uploadImgUrl"
                :limit="1"
                :headers="headers"
                :on-success="handleAvatarSuccess"
                :on-exceed="handleExceed"
                :list-type="picture"
                accept=".xlsx"
              >
                <template #trigger>
                  <el-button type="primary">上传文件</el-button>
                </template>
              </el-upload>
            </el-col>
            <el-col :span="6">
              <el-button @click="importTemplate">下载模板</el-button>
            </el-col>
          </el-row>

        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>



    <!-- 店铺日志总信息 -->
    <el-dialog :title="dialog.title" v-model="dialog.shopSetup" width="800px" append-to-body>
      <div style="width:732px;height:162px;background:#f2f2f2;white-space: pre-wrap;padding:13px;margin-bottom:15px; "  >
        {{logsMessage}}
      </div>

      <el-tooltip content="刷新" >
        <el-button icon="Refresh"   size="small" style="float: right;" @click="refreshTheLogs" ></el-button>
      </el-tooltip>

      <el-table v-loading="loading" :data="taskLogsList" height="400px">
        <el-table-column label="店铺名称" align="center" prop="shopName" />

        <el-table-column label="待执行/已完成" align="center" prop="createTime" width="300">
          <template #default="scope">
            <span>{{ scope.row.waitCount }} / {{scope.row.successCount}}</span>
          </template>
        </el-table-column>

        <!-- <el-table-column label="进度" align="center" >
          <template #default="scope">
            {{scope.row.progress }}%
          </template>
        </el-table-column>-->

        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="查看" placement="top">
              <el-button link type="primary" icon="Memo" @click="getLogsDetailList(scope.row)">查看</el-button>
            </el-tooltip>
            <!-- <el-tooltip content="下载" placement="top">
              <el-button link type="primary" icon="Download" @click="downloadLogs(scope.row.taskId + scope.row.shopId)">下载</el-button>
            </el-tooltip> -->
          </template>
        </el-table-column>
      </el-table>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancelTaskLog">关 闭</el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 店铺任务详细信息 -->
    <el-dialog :title="dialog.logDetailTitle" v-model="dialog.taskLogsDetail" width="700px" append-to-body>

      <el-table v-loading="loading" :data="taskLogsDetailList" height="700px">
        <el-table-column label="日志名称" align="center" prop="msg"/>
        <el-table-column label="数量" align="center" prop="allNum" width="100px"/>
        <el-table-column label="占比" align="center" width="100px">
          <template #default="scope">
            {{ (scope.row.allNum / allNum * 100).toFixed(2) }}%
          </template>
        </el-table-column>

        <!-- <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="100px">
          <template #default="scope">
            <el-tooltip content="下载日志" placement="top">
              <el-button link type="primary" icon="Download" @click="downloadLogs(scope.row.mark)">下载日志</el-button>
            </el-tooltip>
          </template>
        </el-table-column> -->
      </el-table>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="detailCancel">关 闭</el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 同步仓库 -->
    <!--    导入弹窗事件-->

    <el-dialog title="选择仓库" v-model="dialogVisible" width="600px" append-to-body>
      <el-form :model="formDepot" :rules="rulesRelease" label-width="80px" style="height:100px">
        <el-form-item label="选择仓库:" prop="depotId" label-width="100px" style="width: 100%">
          <el-select v-model="formDepot.depotId" placeholder="请选择仓库" clearable>
            <el-option v-for="item in depotList" :key="item.id" :label="item.name+''+item.unit" :value="item.id" >
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="tbstockSubmit">确 定</el-button>
          <el-button @click="cancelStock">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!--    货号装换弹框-->
    <el-dialog :title="dialog.title" v-model="artDialog.visible" width="800px" append-to-body>
      <el-form ref="shelvesFormRef" :model="form" :rules="rules">
        <!--        <el-form-item label="货区名称" prop="depotId" >-->
        <!--          <el-select v-model="selectedId"  value-key="id" placeholder="请选择一级货区" :reserve-keyword="false"  clearable filterable style="width: 100%" :loading="loading"  @update:model-value="handleDepotChange">-->
        <!--            <el-option v-for="item in depotList" :key="item.id" :label="item.name+''+item.unit" :value="item" />-->
        <!--          </el-select>-->
        <!--        </el-form-item>-->

        <el-form-item label="货区名称" prop="deliveryArea">
          <el-cascader :props="props" v-model="selectedValues"  @change="handleCascaderChange" />
        </el-form-item>
        <el-form-item label="货号规则" >
          <el-radio-group v-model="radio1">
            <el-radio value="1" size="large" border style="width: 600px">一级 + 二级 + 三级 + 品相 + isbn + 丛书 + 毫秒时间戳</el-radio>
          </el-radio-group>
          <el-radio-group v-model="radio1">
            <el-radio value="2" size="large" border style="width: 600px"  disabled>开发中</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitArtRuleForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Task" lang="ts">
import { listTask, getTask, delTask, addTask, updateTask ,
    logsTask,logsDetailTask,downloadLog,pauseThread,continueThread,
    logsMsg,checkFile,delFile,tbStock,stopTask,editRunningTaskStatus} from '@/api/zhishu/task';
import { TaskVO, TaskQuery, TaskForm, ReleaseTaskVO } from '@/api/zhishu/task/types';
import { artNoRuleSave, getFre, getListShop, getShe } from '@/api/zhishu/shop';
import { CascaderProps, genFileId } from 'element-plus';
import type { UploadInstance, UploadProps, UploadRawFile } from 'element-plus'
import { globalHeaders } from '@/utils/request';
import { getCats,getSpecs} from '@/api/zhishu/pdd';
import { DepotVO } from '@/api/zhishu/depot/types';
import { depotNameList } from '@/api/zhishu/shelves';
import { ShopVO } from '@/api/zhishu/shop/types';
import { ref } from 'vue';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { t_task_type, t_task_status,t_update_method,t_list_status,t_book_category }
= toRefs<any>(proxy?.useDict('t_task_type', 't_task_status','t_update_method','t_list_status','t_book_category'));


const taskList = ref<TaskVO[]>([]);
const taskLogsList = ref([]);
const taskLogsDetailList = ref([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const statues = ref<Array<string | number>>([]);
const fileNames = ref<Array<string>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const depotList = ref<DepotVO[]>([]);
const queryFormRef = ref<ElFormInstance>();
const taskFormRef = ref<ElFormInstance>();
const dialogVisible = ref(false);

const baseUrl = import.meta.env.VITE_APP_BASE_API;
const uploadImgUrl = ref(baseUrl + '/zhishu/task/upload'); // 上传的图片服务器地址
const headers = ref(globalHeaders());
const logsMessage = ref('');

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const shopList = [];

const initFormData: TaskForm = {
  id: undefined,
  taskType: undefined,
  shopIds: undefined,
  shopNames:undefined,
  fileName: undefined,
  dataNum: undefined,
  taskStatus: undefined,
  status: undefined,
  threadId :undefined,
  hasSyncFile:undefined,
};



const data = reactive<PageData<TaskForm, TaskQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    taskType: undefined,
    shopIds: undefined,
    shopNames:undefined,
    fileName: undefined,
    dataNum: undefined,
    taskStatus: undefined,
    status: undefined,
    params: {}
  },
  rules: {
    id: [{ required: true, message: '不能为空', trigger: 'blur' }]
  }
});

const { queryParams, form, rules } = toRefs(data);

const formRelease = ref({
  taskType: undefined,
  way: '0',
  listStatus: '0',
  bookCategory: '0',
  bookCategoryAppoint: undefined,
  appointCategory: undefined,
  shopIds: undefined,
  fileUrl: undefined,
  data: undefined
});

const rulesRelease = {
  id: [{ required: true, message: '不能为空', trigger: 'blur' }]
}
const radio1 = ref('')
const  artDialog=reactive<DialogOption>({
  visible: false,
  title: '',
  isAdd: true
});

const selectedValues = ref([])
const freList = ref([]) // 目标存储数组


let id = 0
const props: CascaderProps = {
  multiple: true,
  checkStrictly: true,
  lazy: true,
  async lazyLoad(node, resolve) {
    const { level, value } = node;
    // 根据层级调用不同的API方法
    const fetchData = async () => {
      try {
        let nodes = [];

        if (level === 0) {
          // 第一级数据（不需要父ID）
          const res = await depotNameList();
          nodes = (res.data  || res.rows  || []).map(item => ({
            value: item.id,
            label: `${item.name}${item.unit  || ''}`,
            leaf: false,
            disabled:true
          }));
        } else if (level === 1) {
          // 第二级数据（需要第一级的ID）
          const res = await getShe(value);
          // 你的第二个API方法，传入父ID
          nodes = (res.data  || res.rows  || []).map(item => ({
            value: item.id,
            label: `${item.name}${item.unit  || ''}`,
            leaf: false,
            disabled:true
          }));
        } else if (level === 2) {
          // 第三级数据（需要第二级的ID）
          const res = await getFre(value); // 你的第三个API方法，传入父ID
          nodes = (res.data  || res.rows  || []).map(item => ({
            value: item.id,
            label: `${item.name}${item.unit  || ''}`,
            leaf: true
          }));
        }
        resolve(nodes);
      } catch (error) {
        console.error('加载级联数据失败:', error);
        resolve([]); // 出错时返回空数组
      }
    };
    // 模拟异步加载
    setTimeout(fetchData, 50);

  },
}

const handleCascaderChange = (values) => {
  // 提取所有第三级ID（多选模式下每个选项的最后一个元素）
  freList.value  = values
    .map(path => path[2]) // 取每条的第三级ID
    .filter(Boolean) // 过滤undefined
  console.log(' 当前货区ID列表:', freList.value)
}


// 货号装换规则弹窗
const artConRules=async (row?: ShopVO)=>{
  artDialog.visible=true
  dialog.title="确认货号转换规则"
  form.shopIds=row.shopIds;
  form.id=row.id;

}

// 货号装换规则提交(确认)
const  submitArtRuleForm=async ()=>{
  selectedValues.value=[];
  const saveArtNo={
    saveArtNoRule:1,
    shopId:form.shopIds,
    freightIdList:freList.value,
    taskId:form.id,
  }

  console.log(saveArtNo)
  artNoRuleSave(saveArtNo);
  artDialog.visible=false;

  ElMessage({
    message: '正在导入中，请稍等.....',
    grouping: true,
    type: 'success',
  })
  setTimeout(() => {
    getList()
  }, 7000)
}


//事件总线
const eventBus = useEventBus<string>('sys-notification');


// 事件监听（自动销毁）
const unsubscribe = eventBus.on((event) => {
  if (event === 'refresh-task') {
    getList();
  }
});


// 记录任务Id
const taskId=ref(0);


// 刷新日志按钮
const refreshTheLogs= async () => {

   const data = await logsTask(taskId.value);

  const logsShopList = data.allData;

  allNum.value = parseInt(data.successCount) + parseInt(data.waitCount);

  logsMessage.value = '程序开始执行\n总执行条数：'+allNum.value+'\n已执行条数：'+data.successCount+'\n待执行条数：'+data.waitCount

  if(allNum.value == data.successCount){
    logsMessage.value += '\n已加载任务全部完成'
  }

  taskLogsList.value = logsShopList;
  reset();
}






/** 查询任务列表列表 */
const getList = async () => {
  loading.value = true;
  const res = await listTask(queryParams.value);
  taskList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

// const getShopList = async () => {
//   const res = await getListShop();
//   if (res.length == 0) {
//     shopList.length = 0;
//   } else {
//     for (var i = 0; i < res.length; i++) {
//       shopList.push({
//         value: res[i].id,
//         label: res[i].shopName
//       });
//     }
//   }
// }
const pddShopList=[];
const kfzShopList=[];
const getShopList = async () => {
  const res = await getListShop();
  console.log(res)
  if (res.length == 0) {
    shopList.length = 0;
  } else {
    for (var i = 0; i < res.length; i++) {
      // shopList.push({
      //   value: res[i].id,
      //   label: res[i].shopName,
      //    type:res[i].shopType
      // });
      if(res[i].shopType==="1"){
        pddShopList.push(
          {
            value: res[i].id,
            label: res[i].shopName,
          }
        )
      }else if(res[i].shopType==="2"){
        kfzShopList.push(
          {
            value: res[i].id,
            label: res[i].shopName,
          }
        )
      }
    }
  }
}

const editThread = async (row,type) =>{
  if(type == 0){
    //暂停
    await editRunningTaskStatus(row.id,'5');
  }else if(type == 1){
    //恢复
    await editRunningTaskStatus(row.id,'0');
  }else if(type == 2){
    //停止
    await editRunningTaskStatus(row.id,'6');
  }
  getList();
}



/** 取消按钮 */
const cancel = () => {
  reset();
  upload.value!.clearFiles();
  dialog.visible = false;

};

const cancelTaskLog = () =>{
  dialog.shopSetup = false;
}

const cancelStock = () => {
  dialogVisible.value = false;
}

const detailCancel = () => {
  reset();
  dialog.taskLogsDetail = false;
}

const allNum = ref();
const shopSetUp = async (row) => {


  const data = await logsTask(row.id);


  const logsShopList = data.allData;

  // logsMessage.value = await logsMsg(row.id);

  allNum.value = parseInt(data.successCount) + parseInt(data.waitCount);

  logsMessage.value = '程序开始执行\n总执行条数：'+allNum.value+'\n已执行条数：'+data.successCount+'\n待执行条数：'+data.waitCount

  if(allNum.value == data.successCount){
    logsMessage.value += '\n已加载任务全部完成'
  }

  taskLogsList.value = logsShopList;
  taskId.value=row.id;
  reset();
  dialog.shopSetup = true;
  dialog.title = '任务日志';
};

const getLogsDetailList = async (row) => {
  reset();
  const logsDetailList = await logsDetailTask(row);
  taskLogsDetailList.value = logsDetailList;
  dialog.taskLogsDetail = true;
  dialog.logDetailTitle = '详细日志信息:'+taskLogsDetailList.value[0].shopName;
}

const downloadLogs = async (fileName) => {
    fileName = fileName + ".xlsx";
    // const url = 'https://test.api.buzhiyushu.cn/zhishu/task/downloadLogs/'+fileName;
    // const url = 'https://api.buzhiyushu.cn/zhishu/task/downloadLogs/'+fileName;
  const url = 'http://localhost:8080/zhishu/task/downloadLogs/'+fileName;
    const link = document.createElement('a');
    link.href = url;
    link.download = fileName; // 设置下载的文件名
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);


  // const file = await downloadLog(fileName);
  // console.log(file);
}

const returnNumber = (num,total) => {
  return (Number(num) / total * 100).toFixed(2);
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  taskFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: TaskVO[]) => {
  ids.value = selection.map((item) => item.id);
  statues.value = selection.map((item) => item.status);
  fileNames.value = selection.map((item) => item.fileName);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = async () => {
  reset();
  await delFile();
  dialog.visible = true;
  dialog.title = '添加任务列表';
};

/** 修改按钮操作 */
const handleUpdate = async (row?: TaskVO) => {
  reset();
  const _id = row?.id || ids.value[0];
  const res = await getTask(_id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = '修改任务列表';
};

/** 提交按钮 */
const submitForm = async () => {
  if(formRelease.value.taskType == undefined){
    proxy?.$modal.msgError('请选择任务类型');
    return false;
  }
  if(formRelease.value.bookCategory != '0' && formRelease.value.bookCategoryAppoint == undefined){
    proxy?.$modal.msgError('请指定类目');
    return false;
  }

  if(formRelease.value.shopIds == undefined){
    proxy?.$modal.msgError('请选择店铺');
    return false;
  }
  const fileName = await checkFile();
  if(formRelease.value.data == undefined){
    proxy?.$modal.msgError('请上传文件或等待文件加载完毕');
    return false;
  }

  if(formRelease.value.bookCategory == '0'){
    formRelease.value.bookCategoryAppoint = 0;
  }

  upload.value!.clearFiles();

  buttonLoading.value = true;
  formRelease.value.shopIds = formRelease.value.shopIds.join(',')
  formRelease.value.bookCategoryAppoint = formRelease.value.bookCategoryAppoint[2];

  await addTask(formRelease.value).finally(() => (buttonLoading.value = false));
  proxy?.$modal.msgSuccess('操作成功');
  dialog.visible = false;
  await getList();
};
/**
 * 同步仓库
 */
const tbstock = async () => {
  formDepot.value = { ...initFormDepot };
  dialogVisible.value = true;
}

const tbstockSubmit = async () => {
  if(formDepot.value.depotId == undefined){
    proxy?.$modal.msgError('请选择仓库');
    return;
  }
  for(var i=0;i<statues.length;i++){
    if(statues[i] != '3'){
       proxy?.$modal.msgError('请选择已完成的任务同步仓库！！');
      return;
    }
  }
  for(var i=0;i<fileNames.value.length;i++){
    if(fileNames.value[i] == '系统发布商品'){
      proxy?.$modal.msgError('请选择文件上传的任务同步仓库！！');
      return;
    }
  }
  let data = {
      "ids":ids.value.join(','),
      "depotId" : formDepot.value.depotId
  }
  const res = await tbStock(data);

  if(res.code == 200){
    proxy?.$modal.msgSuccess(res.msg);
    formDepot.value.depotId == undefined;
    dialogVisible.value = false;
  }else{
    proxy?.$modal.msgError(res.msg);
  }
}

/** 删除按钮操作 */
const handleDelete = async (row?: TaskVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除任务列表编号为"' + _ids + '"的数据项？').finally(() => (loading.value = false));
  await delTask(_ids);
  proxy?.$modal.msgSuccess('删除成功');
  await getList();
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    'zhishu/task/export',
    {
      ...queryParams.value
    },
    `task_${new Date().getTime()}.xlsx`
  );
};

/** 下载模板操作 */
const importTemplate = () => {
  proxy?.download('zhishu/task/importTemplate', {}, `task_template_${new Date().getTime()}.xlsx`);
};

/**
 * 文件上传
 */
const upload = ref<UploadInstance>()

const handleAvatarSuccess: UploadProps['onSuccess'] = async (response, uploadFile) => {
  if(response.code == 500){
    upload.value!.clearFiles()
    proxy?.$modal.msgError(response.msg);
    return false;
  }
  formRelease.value.data = response.data
  // formRelease.value.fileUrl = response.data.url;
};
const handleExceed: UploadProps['onExceed'] = (files) => {
  upload.value!.clearFiles()
  const file = files[0] as UploadRawFile
  file.uid = genFileId()
  upload.value!.handleStart(file)
}

const submitUpload = () => {
  upload.value!.submit()
}

let catsList = [];

const getCatss = async () =>{
  catsList = await getCats();
}

//仓库数据加载
const initFormDepot = ref({
  depotId: undefined
});

const formDepot = ref({
  depotId: undefined
});

const loadData = async () => {
  loading.value  = true
  try {
    const res = await depotNameList()
    depotList.value=res.rows
  } catch (error) {
  } finally {
    loading.value  = false
  }
}

onMounted(() => {
  getList();
  getShopList();
  getCatss();
  loadData();
});
</script>

<style>
.isHide{
  display: none;
}
</style>
