export interface FilterSetVO {
  /**
   * 主键
   */
  id: string | number;

  /**
   * 过滤类型
   */
  filterType: string;

  /**
   * 限制类型
   */
  limitationType: string;

  /**
   * 添加方式
   */
  addWay: string;

  /**
   * 内容文件
   */
  addTxt: string;

  /**
   * 状态（0正常 1停用）
   */
  status: string;

}

export interface FilterSetForm extends BaseEntity {
  /**
   * 主键
   */
  id?: string | number;

  /**
   * 过滤类型
   */
  filterType?: string;

  /**
   * 限制类型
   */
  limitationType?: string;

  /**
   * 添加方式
   */
  addWay?: string;

  /**
   * 内容文件
   */
  addTxt?: string;

  /**
   * 状态（0正常 1停用）
   */
  status?: string;

}

export interface FilterSetQuery extends PageQuery {

  /**
   * 过滤类型
   */
  filterType?: string;

  /**
   * 限制类型
   */
  limitationType?: string;

  /**
   * 添加方式
   */
  addWay?: string;

  /**
   * 内容文件
   */
  addTxt?: string;

  /**
   * 状态（0正常 1停用）
   */
  status?: string;

    /**
     * 日期范围参数
     */
    params?: any;
}



