import { string } from "vue-types";

export interface ExcelTaskVO extends BaseEntity {
  /**
   *
   */
  id: string | number;

  /**
   * 任务类型 1 发布商品
   */
  taskType: string;

  /**
   * 仓库id 字符串
   */
  depotIds: string | number;

  /**
   * 文件名称
   */
  fileName: string;

  /**
   * 执行数据条数
   */
  dataNum: number;

  /**
   * 任务状态
   */
  taskStatus: string;

  /**
   * 状态（0正常 1停用）
   */
  status: string;

  /**
   * 线程id
   */
  threadId: string | number;
}

export interface ExcelTaskForm extends BaseEntity {
  /**
   *
   */
  id?: string | number;

  /**
   * 任务类型 1 发布商品
   */
  taskType?: string;
  /**
   * 仓库id 字符串
   */
  depotIds?: string | number;

  /**
   * 文件名称
   */
  fileName?: string;

  /**
   * 执行数据条数
   */
  dataNum?: number;

  /**
   * 任务状态
   */
  taskStatus?: string;

  /**
   * 状态（0正常 1停用）
   */
  status?: string;

  /**
   * 线程id
   */
  threadId: string | number;
}

export interface ExcelTaskQuery extends PageQuery {

  /**
   * 任务类型 1 发布商品
   */
  taskType?: string;
  /**
   * 仓库id 字符串
   */
  depotIds?: string | number;

  /**
   * 文件名称
   */
  fileName?: string;

  /**
   * 执行数据条数
   */
  dataNum?: number;

  /**
   * 任务状态
   */
  taskStatus?: string;

  /**
   * 状态（0正常 1停用）
   */
  status?: string;

  /**
   * 日期范围参数
   */
  params?: any;

    /**
   * 线程id
   */
  threadId: string | number;
}



/**
 * 发布任务Vo
 */
export interface ReleaseExccelTaskVO{

  id?: number;
  /**
   * 任务类型
   */
  taskType?: string;
  /**
   * 更新方式
   */
  way?: string;

  /**
   * 上架状态
   */
  listStatus?: string;

  /**
   * 图书类目
   */
  bookCategory?: string;

  /**
   * 指定类目
   */
  appointCategory?: string;
  /**
   * 仓库id 选择
   */
  depotIds?: string;
}

