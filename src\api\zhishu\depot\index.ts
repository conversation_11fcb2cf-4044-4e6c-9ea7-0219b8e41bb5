import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { DepotVO, DepotForm, DepotQuery } from '@/api/zhishu/depot/types';
import { templateData } from '@/api/zhishu/district/types';
import { ShelvesVO } from '@/api/zhishu/shelves/types';
import { FreightVO } from '@/api/zhishu/freight/types';

/**
 * 查询仓库信息列表
 * 查询仓库信息设置列表
 * @param query
 * @returns {*}
 */

export const listDepot = (query?: DepotQuery): AxiosPromise<DepotVO[]> => {
  return request({
    url: '/depot/depot/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询仓库信息详细
export const listDepots = (query?: DepotQuery): AxiosPromise<DepotVO[]> => {
  return request({
    url: '/depot/depot/list1',
    method: 'get',
    params: query
  });
};



/**
 * 查询仓库信息设置详细
 * @param id
 */
export const getDepot = (id: string | number): AxiosPromise<DepotVO> => {
  return request({
    url: '/depot/depot/' + id,
    method: 'get'
  });
};

/**
 * 新增仓库信息设置
 * @param data
 */
export const addDepot = (data: DepotForm) => {
  return request({
    url: '/depot/depot',
    method: 'post',
    data: data
  });
};

/**
 * 修改仓库信息
 * @param data
 */
export const updateDepot = (data: DepotForm) => {
  return request({
    url: '/depot/depot',
    method: 'put',
    data: data
  });
};

/**
 * 删除仓库信息
 * 删除仓库信息设置
 * @param id
 */
export const delDepot = (id: string | number | Array<string | number>) => {
  return request({
    url: '/depot/depot/' + id,
    method: 'delete'
  });
};


// export const shelveList = (id: number|string): AxiosPromise<ShelvesVO[]> => {
//   return request({
//     url: ,
//     method: 'get'
//   });
// };

/**
 * 获取三级货区信息
 * @param id
 */
export const shelveInfoList = (id: string | number): AxiosPromise<ShelvesVO> => {
  return request({
    url: '/depot/depot/shelves/' + id,
    method: 'get'
  });
};

/**
 * 获取二级货区信息
 * @param id
 */
export const freList = (id: string | number): AxiosPromise<FreightVO> => {
  return request({
    url: '/depot/depot/freight/' + id,
    method: 'get'
  });
};


/**
 * 查询仓库信息列表
 * 查询仓库信息设置列表
 * @param query
 * @returns {*}
 */

export const depotNameListAll = (query?: DepotQuery): AxiosPromise<DepotVO[]> => {
  return request({
    url: '/depot/depot/getDepotAll',
    method: 'get',
    params: query
  });
};
