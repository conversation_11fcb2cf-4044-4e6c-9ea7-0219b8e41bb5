export interface ShopGoodsPublishedVO {
  /**
   * 主键
   */
  id: string | number;

  /**
   * 图书主键
   */
  shopGoodsId: string | number;

  goodsName : string;

  shopName : string;

  /**
   * 发布的店铺ids
   */
  shopId: string | number;

  /**
   * 平台商品id
   */
  platformId: string | number;

  /**
   * 状态（0正常 1停用）
   */
  status: string;

  /**
   * 商品编码
   */
  itemNumber:string;
  /**
   * 商品图片
   */
  bookPic:string;
  /**
   * isbn
   */
  isbn:string;
  /**
   * 品相
   */
  conditionCode:string;
  /**
   * 商品售价
   */
  price:string;
  /**
   * 发布时间
   */
  createTime:string;
  /**
   * 线上商品数量
   */
  inventory:number;
  /**
   * 线上商品
   */
  onlineProducts:string;
  /**
   * 供应商
   */
  supplier:string;

  /**
   * 创建人
   */
  createBy:String;
  /**
   * 店铺类型
   */
  shopType:number;
  /**
   * 三方店铺Id
   */
  mallId:number;

  // goodsName:string;

  // createTime: string;

}

export interface ShopGoodsPublishedForm extends BaseEntity {
  /**
   * 主键
   */
  id?: string | number;

  /**
   * 图书主键
   */
  shopGoodsId?: string | number;

  /**
   * 发布的店铺ids
   */
  shopId?: string | number;

  goodsName : string;

  shopName : string;

  /**
   * 平台商品id
   */
  platformId?: string | number;

  /**
   * 状态（0正常 1停用）
   */
  status?: string;

  /**
   * 商品编码
   */
  itemNumber?:string;

  /**
   * 书图片
   */
  bookPic?:string;

  /**
   * isbn
   */
  isbn?:string;

  /**
   * 品相
   */
  conditionCode?:string;

  /**
   * 标准售价
   */
  price?:string;

  /**
   * 发布时间
   */
  createTime?:string;

  /**
   * 货区Id
   */
  depotId?:string;
}

export interface ShopGoodsPublishedQuery extends PageQuery {

  /**
   * 图书主键
   */
  shopGoodsId?: string | number;

  /**
   * 发布的店铺id
   */
  shopId?: string | number;

  goodsName : string;

  shopName : string;

  /**
   * 平台商品id
   */
  platformId?: string | number;

  /**
   * 状态（0正常 1停用）
   */
  status?: string;

    /**
     * 日期范围参数
     */
    params?: any;

    createTime?: string;
}



