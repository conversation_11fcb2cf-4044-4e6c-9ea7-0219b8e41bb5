import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { ShopGoodsPublishedVO, ShopGoodsPublishedForm, ShopGoodsPublishedQuery } from '@/api/zhishu/shopGoodsPublished/types';

/**
 * 查询记录发布数据列表
 * @param query
 * @returns {*}
 */

export const listShopGoodsPublished = (query?: ShopGoodsPublishedQuery): AxiosPromise<ShopGoodsPublishedVO[]> => {
  return request({
    url: '/zhishu/shopGoodsPublished/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询记录发布数据详细
 * @param id
 */
export const getShopGoodsPublished = (id: string | number): AxiosPromise<ShopGoodsPublishedVO> => {
  return request({
    url: '/zhishu/shopGoodsPublished/' + id,
    method: 'get'
  });
};

/**
 * 新增记录发布数据
 * @param data
 */
export const addShopGoodsPublished = (data: ShopGoodsPublishedForm) => {
  return request({
    url: '/zhishu/shopGoodsPublished',
    method: 'post',
    data: data
  });
};

/**
 * 修改记录发布数据
 * @param data
 */
export const updateShopGoodsPublished = (data: ShopGoodsPublishedForm) => {
  return request({
    url: '/zhishu/shopGoodsPublished',
    method: 'put',
    data: data
  });
};

/**
 * 删除记录发布数据
 * @param id
 */
export const delShopGoodsPublished = (id: string | number | Array<string | number>) => {
  return request({
    url: '/zhishu/shopGoodsPublished/' + id,
    method: 'delete'
  });
};
