import request from '@/utils/request';
import { AxiosPromise } from 'axios';

let LODOP = null

function getBlackTxt(txt,width,height,txtLeft,txtTop){
  // 1. 创建 Canvas 画布
  var canvas = document.createElement("canvas");
  canvas.width = width;
  canvas.height = height;
  var ctx = canvas.getContext("2d");

  // 2. 绘制黑色背景 + 文字
  ctx.fillStyle = "#000000";
  ctx.fillRect(0, 0, canvas.width, canvas.height);
  ctx.fillStyle = "#FFFFFF";
  ctx.font = "bold 16px Microsoft YaHei";
  ctx.textAlign = "center";
  ctx.fillText(txt, txtLeft, txtTop);

  // 3. 转换为 Base64 图片
  return canvas.toDataURL("image/png");
}

function getCLodopImg(trackingNumber){
  // 1. 创建隐藏的 Canvas 并生成横向条码
  const canvas = document.createElement("canvas");
  JsBarcode(canvas, trackingNumber, {
    format: "CODE39",
    fontSize:"12",
    margin:0,
    displayValue: true,   // 显示条码文字
    lineColor: "#000000", // 条码颜色
    width: 1,            // 线条粗细
    height: 40           // 条码高度
  });

  // 2. 创建新 Canvas 并旋转 90°
  const rotatedCanvas = document.createElement("canvas");
  rotatedCanvas.width = canvas.height;  // 旋转后宽度 = 原高度
  rotatedCanvas.height = canvas.width;  // 旋转后高度 = 原宽度
  const ctx = rotatedCanvas.getContext("2d");

  // 关键旋转操作！
  ctx.translate(rotatedCanvas.width / 2, rotatedCanvas.height / 2); // 移动旋转中心
  ctx.rotate(Math.PI / 2);                                         // 旋转90°
  ctx.drawImage(canvas, -canvas.width / 2, -canvas.height / 2);    // 绘制图像

  // 3. 获取图片数据并打印
  return rotatedCanvas.toDataURL("image/png");
}

// 模板注册中心（扩展新模板只需在此注册）
const templateModules = {
  // 销售小票模板
  receipt: (LODOP, params) => {
    LODOP.PRINT_INIT("销售小票")
    LODOP.SET_PRINT_PAGESIZE(1, '76mm', '110mm', '')

    LODOP.ADD_PRINT_TEXT("10mm", "5mm", "60mm", "5mm", "=== 销售小票 ===")
    LODOP.SET_PRINT_STYLEA(0, "FontSize", 12)

    params.items.forEach((item, index) => {
      LODOP.ADD_PRINT_TEXT(
        `${25 + index * 8}mm`,
        "5mm",
        "60mm",
        "5mm",
        `${item.name} x${item.qty} ￥${item.price}`
      )
    })
  },

  // 韵达快递模板
  yunda: (LODOP, params) => {
    // LODOP.PRINT_DESIGN();

    LODOP.PRINT_INIT("韵达快递单")
    LODOP.SET_PRINT_PAGESIZE(3, "77mm", "120mm", "")
    LODOP.ADD_PRINT_PDF("-4mm","-1mm","10mm","10mm",params);
    LODOP.ADD_PRINT_RECT("10.6mm", "0", "76mm", "118mm", 0, 1) // 外围边框
  },

  // 圆通快递模板
  yuantong: (LODOP, params) => {
    LODOP.PRINT_INIT("圆通快递单")
    LODOP.SET_PRINT_PAGESIZE(3, "77mm", "120mm", "")
    LODOP.ADD_PRINT_PDF("-4mm","-1mm","10mm","10mm",params);
    LODOP.ADD_PRINT_RECT("10.6mm", "0", "76mm", "118mm", 0, 1) // 外围边框

    try {
      // 尝试解析PDF内容中的商品信息
      const pdfData = params;

      // 在红色框区域添加商品信息
      LODOP.SET_PRINT_STYLE("FontName", "Microsoft YaHei");
      LODOP.SET_PRINT_STYLE("FontSize", 9);

      // 添加标题 - 调整位置到红框区域中间位置
      LODOP.ADD_PRINT_TEXT("88mm", "5mm", "70mm", "5mm", "物品清单:");
      LODOP.SET_PRINT_STYLEA(0, "FontSize", 10);
      LODOP.SET_PRINT_STYLEA(0, "Bold", 1);

      // 尝试从后端获取的自定义数据中提取商品信息
      const customData = window.localStorage.getItem('currentOrderCustomData');
      if (customData) {
        try {
          const orderInfo = JSON.parse(customData);
          // 添加商品信息 - 调整位置避免与上方内容重叠
          LODOP.ADD_PRINT_TEXT("93mm", "5mm", "70mm", "5mm", `商品名称: ${orderInfo.itemName || '未知商品'}`);
          LODOP.ADD_PRINT_TEXT("98mm", "5mm", "70mm", "5mm", `ISBN: ${orderInfo.isbn || '无'}`);
          LODOP.ADD_PRINT_TEXT("103mm", "5mm", "70mm", "5mm", `品质: ${orderInfo.quality || '标准'}`);
          LODOP.ADD_PRINT_TEXT("108mm", "5mm", "70mm", "5mm", `数量: ${orderInfo.number || '1'}件`);
        } catch (e) {
          console.error('解析商品信息失败', e);
          // 添加默认商品信息
          LODOP.ADD_PRINT_TEXT("93mm", "5mm", "70mm", "20mm", "商品信息解析失败，请检查数据格式");
        }
      } else {
        // 添加默认商品信息
        LODOP.ADD_PRINT_TEXT("93mm", "5mm", "70mm", "20mm", "图书/文具/音像制品");
      }
    } catch (error) {
      console.error('添加商品信息失败', error);
    }
  },

  // 申通快递模板
  shentong: (LODOP, params) => {
    LODOP.PRINT_INIT("申通快递单")
    LODOP.SET_PRINT_PAGESIZE(3, "77mm", "120mm", "")
    LODOP.ADD_PRINT_PDF("-4mm","-1mm","10mm","10mm",params);
    LODOP.ADD_PRINT_RECT("10.6mm", "0", "76mm", "118mm", 0, 1) // 外围边框

    try {
      // 在红色框区域添加商品信息
      LODOP.SET_PRINT_STYLE("FontName", "Microsoft YaHei");
      LODOP.SET_PRINT_STYLE("FontSize", 9);

      // 添加标题
      LODOP.ADD_PRINT_TEXT("88mm", "5mm", "70mm", "5mm", "物品清单:");
      LODOP.SET_PRINT_STYLEA(0, "FontSize", 10);
      LODOP.SET_PRINT_STYLEA(0, "Bold", 1);

      // 尝试从localStorage获取商品信息
      const customData = window.localStorage.getItem('currentOrderCustomData');
      if (customData) {
        try {
          const orderInfo = JSON.parse(customData);
          // 添加商品信息
          LODOP.ADD_PRINT_TEXT("93mm", "5mm", "70mm", "5mm", `商品名称: ${orderInfo.itemName || '未知商品'}`);
          LODOP.ADD_PRINT_TEXT("98mm", "5mm", "70mm", "5mm", `ISBN: ${orderInfo.isbn || '无'}`);
          LODOP.ADD_PRINT_TEXT("103mm", "5mm", "70mm", "5mm", `品质: ${orderInfo.quality || '标准'}`);
          LODOP.ADD_PRINT_TEXT("108mm", "5mm", "70mm", "5mm", `数量: ${orderInfo.number || '1'}件`);
        } catch (e) {
          console.error('解析商品信息失败', e);
          LODOP.ADD_PRINT_TEXT("93mm", "5mm", "70mm", "20mm", "商品信息解析失败，请检查数据格式");
        }
      } else {
        LODOP.ADD_PRINT_TEXT("93mm", "5mm", "70mm", "20mm", "图书/文具/音像制品");
      }
    } catch (error) {
      console.error('添加商品信息失败', error);
    }
  },

  // 中通快递模板
  zhongtong: (LODOP, params) => {
    LODOP.PRINT_INIT("中通快递单")
    LODOP.SET_PRINT_PAGESIZE(3, "77mm", "120mm", "")
    LODOP.ADD_PRINT_PDF("-4mm","-1mm","10mm","10mm",params);
    LODOP.ADD_PRINT_RECT("10.6mm", "0", "76mm", "118mm", 0, 1) // 外围边框

    try {
      // 在红色框区域添加商品信息
      LODOP.SET_PRINT_STYLE("FontName", "Microsoft YaHei");
      LODOP.SET_PRINT_STYLE("FontSize", 9);

      // 添加标题
      LODOP.ADD_PRINT_TEXT("88mm", "5mm", "70mm", "5mm", "物品清单:");
      LODOP.SET_PRINT_STYLEA(0, "FontSize", 10);
      LODOP.SET_PRINT_STYLEA(0, "Bold", 1);

      // 尝试从localStorage获取商品信息
      const customData = window.localStorage.getItem('currentOrderCustomData');
      if (customData) {
        try {
          const orderInfo = JSON.parse(customData);
          // 添加商品信息
          LODOP.ADD_PRINT_TEXT("93mm", "5mm", "70mm", "5mm", `商品名称: ${orderInfo.itemName || '未知商品'}`);
          LODOP.ADD_PRINT_TEXT("98mm", "5mm", "70mm", "5mm", `ISBN: ${orderInfo.isbn || '无'}`);
          LODOP.ADD_PRINT_TEXT("103mm", "5mm", "70mm", "5mm", `品质: ${orderInfo.quality || '标准'}`);
          LODOP.ADD_PRINT_TEXT("108mm", "5mm", "70mm", "5mm", `数量: ${orderInfo.number || '1'}件`);
        } catch (e) {
          console.error('解析商品信息失败', e);
          LODOP.ADD_PRINT_TEXT("93mm", "5mm", "70mm", "20mm", "商品信息解析失败，请检查数据格式");
        }
      } else {
        LODOP.ADD_PRINT_TEXT("93mm", "5mm", "70mm", "20mm", "图书/文具/音像制品");
      }
    } catch (error) {
      console.error('添加商品信息失败', error);
    }
  },

  // 可继续添加其他模板...
}


export const initLodop = () => {
  return new Promise((resolve, reject) => {
    if (LODOP) return resolve(LODOP)

    const script = document.createElement('script')
    script.src = 'http://localhost:8000/CLodopfuncs.js'
    script.onload = () => {
      LODOP = getCLodop()
      if (!LODOP) {
        reject(new Error('C-Lodop 未正确安装'))
      }
      resolve(LODOP)
    }
    script.onerror = () => reject(new Error('加载 C-Lodop 失败'))
    document.head.appendChild(script)
  })
}

export const createPrintTask = async (templateName, content) => {
  try {

    const LODOP = await initLodop()
    LODOP.SET_LICENSES("","630D8906DC4186920589CAE3FFA925A278D","","");
    // 检查模板是否存在
    if (!templateModules[templateName]) {
      console.log(templateName)
      throw new Error(`未找到模板: ${templateName}`)
    }

    // 执行模板渲染
    templateModules[templateName](LODOP, content)

    return LODOP
  } catch (error) {
    throw error
  }
}


