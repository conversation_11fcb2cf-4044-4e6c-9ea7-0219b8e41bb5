import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { ShopImgVO, ShopImgForm, ShopImgQuery } from '@/api/zhishu/shopImg/types';

/**
 * 查询店铺图片列表
 * @param query
 * @returns {*}
 */

export const listShopImg = (query?: ShopImgQuery): AxiosPromise<ShopImgVO[]> => {
  return request({
    url: '/zhishu/shopImg/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询店铺图片详细
 * @param id
 */
export const getShopImg = (id: string | number): AxiosPromise<ShopImgVO> => {
  return request({
    url: '/zhishu/shopImg/' + id,
    method: 'get'
  });
};

/**
 * 新增店铺图片
 * @param data
 */
export const addShopImg = (data: ShopImgForm) => {
  return request({
    url: '/zhishu/shopImg',
    method: 'post',
    data: data
  });
};

/**
 * 修改店铺图片
 * @param data
 */
export const updateShopImg = (data: ShopImgForm) => {
  return request({
    url: '/zhishu/shopImg',
    method: 'put',
    data: data
  });
};

/**
 * 删除店铺图片
 * @param id
 */
export const delShopImg = (id: string | number | Array<string | number>) => {
  return request({
    url: '/zhishu/shopImg/' + id,
    method: 'delete'
  });
};


/**
 * 根据店铺id获取店铺图片
 */
export const listShopImgByPid = (pid: string | number) =>{
  return request({
    url: '/zhishu/shopImg/listByPid/' + pid,
    method: 'get'
  });
}
