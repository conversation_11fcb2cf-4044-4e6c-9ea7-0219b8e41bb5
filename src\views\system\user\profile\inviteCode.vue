<template>
  <div class="app-container">
    <el-card shadow="hover">
      <template #header>
        <div class="card-header">
          <span>我的邀请码</span>
        </div>
      </template>
      <el-table v-loading="loading" :data="inviteCodeList" stripe style="width: 100%">
        <el-table-column prop="code" label="邀请码" width="120" />
        <el-table-column prop="inviteUrl" label="邀请链接">
          <template #default="scope">
            <div class="invite-url-container">
              <el-input v-model="scope.row.inviteUrl" disabled />
              <el-button type="primary" plain @click="copyInviteUrl(scope.row.inviteUrl)">复制</el-button>
            </div>
          </template>
        </el-table-column>
        <!-- <el-table-column prop="expireTime" label="过期时间" width="180">
          <template #default="scope">
            {{ parseTime(scope.row.expireTime) }}
          </template>
        </el-table-column> -->
        <!-- <el-table-column prop="used" label="使用状态" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.used ? 'success' : 'info'">
              {{ scope.row.used ? '已使用' : '未使用' }}
            </el-tag>
          </template>
        </el-table-column> -->
        <el-table-column prop="createdAt" label="创建时间" width="180">
          <template #default="scope">
            {{ parseTime(scope.row.createdAt) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="100">
          <template #default="scope">
            <el-button 
              type="success" 
              link 
              :icon="Share" 
              @click="handleShare(scope.row)">分享</el-button>
          </template>
        </el-table-column>
      </el-table>

      <el-pagination
        v-if="total > 0"
        v-model:current-page="queryParams.pageNum"
        v-model:page-size="queryParams.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </el-card>

    <!-- 分享对话框 -->
    <el-dialog v-model="shareDialogVisible" title="分享邀请码" width="400px">
      <div class="share-container">
        <p>请使用微信扫描下方二维码进行分享</p>
        <div class="qrcode-container">
          <canvas ref="qrcodeRef"></canvas>
        </div>
        <div class="share-info">
          <p>邀请码: {{ currentShareCode?.code }}</p>
          <p>邀请链接: {{ currentShareCode?.inviteUrl }}</p>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="shareDialogVisible = false">关闭</el-button>
          <el-button type="primary" @click="copyInviteUrl(currentShareCode?.inviteUrl)">复制链接</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, reactive, nextTick } from 'vue';
import { ElMessage } from 'element-plus';
import { parseTime } from '@/utils/ruoyi';
import request from '@/utils/request';
import { Share } from '@element-plus/icons-vue';
import QRCode from 'qrcode';

// 邀请码列表
const inviteCodeList = ref([]);
// 加载状态
const loading = ref(false);
// 总数
const total = ref(0);
// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10
});
// 分享对话框显示状态
const shareDialogVisible = ref(false);
// 当前分享的邀请码
const currentShareCode = ref(null);
// 二维码容器引用
const qrcodeRef = ref(null);

// 获取邀请码列表
const getInviteCodeList = async () => {
  loading.value = true;
  try {
    const res = await request({
      url: '/zhishu/invite/page',
      method: 'get',
      params: queryParams
    });
    console.log(res);
    if (res.code === 200 && res.rows) {
      inviteCodeList.value = res.rows;
      total.value = res.total || 0;
      
      // 如果没有邀请码，自动生成
      if (inviteCodeList.value.length === 0) {
        await autoGenerateInviteCode();
      }
    } else {
      inviteCodeList.value = [];
      total.value = 0;
    }
  } catch (error) {
    console.error('获取邀请码列表失败:', error);
    inviteCodeList.value = [];
    total.value = 0;
  } finally {
    loading.value = false;
  }
};

// 自动生成邀请码
const autoGenerateInviteCode = async () => {
  try {
    const res = await request({
      url: '/zhishu/invite/generate',
      method: 'post',
      data: {}
    });
    if (res.code === 200) {
      ElMessage.success('邀请码生成成功');
      getInviteCodeList();
    } else {
      ElMessage.error(res.msg || '邀请码生成失败');
    }
  } catch (error) {
    console.error('生成邀请码失败:', error);
  }
};

// 复制邀请链接
const copyInviteUrl = (url) => {
  navigator.clipboard.writeText(url)
    .then(() => {
      ElMessage.success('邀请链接已复制到剪贴板');
    })
    .catch(() => {
      ElMessage.error('复制失败，请手动复制');
    });
};

// 处理每页显示数量变化
const handleSizeChange = (val: number) => {
  queryParams.pageSize = val;
  getInviteCodeList();
};

// 处理页码变化
const handleCurrentChange = (val: number) => {
  queryParams.pageNum = val;
  getInviteCodeList();
};

// 处理分享
const handleShare = async (row) => {
  currentShareCode.value = row;
  shareDialogVisible.value = true;
  
  // 等待DOM更新后生成二维码
  await nextTick();
  if (qrcodeRef.value) {
    try {
      // 生成新的二维码
      await QRCode.toCanvas(qrcodeRef.value, row.inviteUrl, {
        width: 200,
        margin: 1,
        color: {
          dark: '#000000',
          light: '#ffffff'
        }
      });
    } catch (err) {
      console.error('生成二维码失败:', err);
      // 修复类型错误，使用安全的错误信息提取
      const errorMessage = err instanceof Error ? err.message : '未知错误';
      ElMessage.error(`生成二维码失败: ${errorMessage}`);
    }
  }
};

// 页面加载时获取数据
onMounted(() => {
  getInviteCodeList();
});
</script>

<style scoped>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.invite-url-container {
  display: flex;
  align-items: center;
}

.invite-url-container .el-input {
  margin-right: 10px;
}

.share-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.qrcode-container {
  margin: 20px 0;
  width: 200px;
  height: 200px;
  display: flex;
  justify-content: center;
}

.share-info {
  width: 100%;
  text-align: center;
}
</style> 