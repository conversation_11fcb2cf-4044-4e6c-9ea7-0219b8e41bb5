<template>
  <div class="p-2">

    <transition :enter-active-class="proxy?.animate.searchAnimate.enter"
                :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="企业名称" prop="companyName" label-width="100px">
              <el-input v-model="queryParams.companyName" placeholder="请输入企业名称" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="联系方式" prop="contactPhone">
              <el-input v-model="queryParams.contactPhone" placeholder="请输入联系方式" clearable @keyup.enter="handleQuery" />
            </el-form-item>
<!--            <el-form-item label="审核状态" prop="status">-->
<!--                <el-select v-model="queryParams.status" value-key="id" placeholder="请选择可见内容" :reserve-keyword="false"  clearable filterable :loading="loading">-->
<!--                  <el-option v-for="item in statusList" :key="item.value" :label="item.label"  :value="item.value"  />-->
<!--                </el-select>-->
<!--            </el-form-item>-->
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>
<!--statusList-->

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="auditList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="ID" align="center" prop="id" :show-overflow-tooltip="true" >
          <template #default="{ row }">
            <div class="truncate-cell">  <!-- 文本截断容器 -->
              {{ row.id  }}
            </div>
          </template>
        </el-table-column>
<!--        <el-table-column label="用户ID" align="center" prop="userId" />-->
<!--        <el-table-column label="用户昵称" align="center" prop="userName" />-->

        <el-table-column label="用户昵称" align="center" prop="userName" width="140" :show-overflow-tooltip="true">

          <template #default="scope">
            <div class="truncate-cell" @click="dialogVisible = true">  <!-- 文本截断容器 -->
              <el-button link @click="handleQueryInfo(scope.row)" class="apply-btn" title="点击显示用户详细信息">{{ scope.row.userName }}</el-button>
            </div>

<!--@click="handleQueryInfo(scope.row)-->
          </template>
        </el-table-column>

<!--        <el-table-column label="管理员ID" align="center" prop="adminId" />-->
        <el-table-column label="企业名称" align="center" prop="companyName" />
        <el-table-column label="企业类型" align="center" prop="companyType">
          <template #default="{ row }">
            {{ row.companyType  === '1' ? '有限责任公司' :
            row.companyType  === '2' ? '股份有限公司' :
              row.companyType  === '3' ? '个人独资企业' :
                '合伙企业' }}
          </template>
        </el-table-column>
        <el-table-column label="联系人名" align="center" prop="contactPerson" />
        <el-table-column label="联系方式" align="center" prop="contactPhone" />
        <el-table-column label="邮箱" align="center" prop="email" />
<!--        <el-table-column label="营业执照" align="center" prop="license" />-->
<!--        <el-table-column label="备注" align="center" prop="remark" />-->
        <el-table-column label="备注" align="center" prop="remark" width="120" :show-overflow-tooltip="true" :render-header="renderHeader">
          <template #default="{ row }">
            <div class="truncate-cell" >  <!-- 文本截断容器 -->
              {{ row.remark  }}
            </div>
          </template>
        </el-table-column>
        <!--        <el-table-column label="审核状态" align="center" prop="status" />-->
        <el-table-column label="审核状态" align="center">
          <template #default="{ row }">
            <el-tag
              :type="row.status  === '0' ? 'success' : row.status === '1' ? 'danger' : 'info'"
              :effect="row.status  === '0' ? 'dark' : 'light'"
              size="large"
            >
              {{ row.status  === '0' ? '通过' : row.status  === '1' ? '未通过' : '待审核' }}
            </el-tag>
          </template>
        </el-table-column>




        <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="120px">
          <template #default="scope">
            <el-tooltip content="通过" placement="top">
              <el-button link type="success" icon="Check" @click="handleUpdateStatus(scope.row.id,'0')" ></el-button>
            </el-tooltip>

            <el-tooltip content="未通过" placement="top" >
              <el-button link type="danger" circle  @click="handleOpenDialog(scope.row.id)" >X</el-button>
            </el-tooltip>

          </template>
        </el-table-column>
      </el-table>
      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>



    <!-- 添加或修改审核对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body>
      <el-form ref="auditFormRef" :model="form" :rules="rules" label-width="80px">
<!--        <el-form-item label="用户ID" prop="userId">-->
<!--          <el-input v-model="form.userId" placeholder="请输入用户ID" />-->
<!--        </el-form-item>-->
        <el-form-item label="管理员ID" prop="adminId">
          <el-input v-model="form.adminId" placeholder="请输入管理员ID" />
        </el-form-item>

        <el-form-item label="企业名称" prop="userId">
          <el-input v-model="form.companyName" placeholder="请输入企业名称" />
        </el-form-item>
        <el-form-item label="企业类型" prop="companyType">
          <el-input v-model="form.companyType" placeholder="请输入企业类型" />

        </el-form-item>
        <el-form-item label="联系人名" prop="contactPerson">
          <el-input v-model="form.contactPerson" placeholder="请输入联系人名" />
        </el-form-item>
        <el-form-item label="联系方式" prop="contactPhone">
          <el-input v-model="form.contactPhone" placeholder="请输入联系方式" />
        </el-form-item>
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="form.email" placeholder="请输入邮箱" />
        </el-form-item>
        <el-form-item label="营业执照" prop="license">
          <el-input v-model="form.license" placeholder="请输入营业执照" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" placeholder="请输入备注" />
        </el-form-item>
        <el-form-item label="审核状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio :label="0" border size="large" value="0" >通过</el-radio>
            <el-radio :label="1" border size="large" value="1">未通过</el-radio>
            <el-radio :label="2" border size="large" value="2">待审核</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>




<!--    <el-dialog title="填写未通过信息"  center v-model="dialog1.visible" width="50%">-->
<!--      <el-form ref="" :model="form"label-width="120px" >-->
<!--        <el-form-item center prop="remark" >-->
<!--          <el-input-->
<!--            type="textarea"-->
<!--            :rows="3"-->
<!--            v-model="form.remark"-->
<!--            placeholder="请输入未通过信息"-->
<!--          ></el-input>-->
<!--        </el-form-item>-->
<!--      </el-form>-->
<!--      <el-button type="primary" @click="hitbackButton" >打回</el-button>-->
<!--    </el-dialog>-->




    <el-dialog
      title="填写未通过信息"
      center
      v-model="dialog1.visible"
      width="50%"
      :close-on-click-modal="false"
    >
      <el-form
        ref="formRef"
        :model="form"
        label-width="120px"
        :rules="rules"
      >
        <el-form-item
          label="驳回理由"
          prop="remark"
          :rules="[
          { required: true, message: '必须填写驳回理由', trigger: 'blur' },
          { min: 10, message: '至少输入10个字符', trigger: 'blur' }
        ]"
        >
          <el-input
            type="textarea"
            :rows="3"
            v-model="form.remark"
            placeholder="请输入具体驳回原因，例如：1.材料不完整；2.信息有误..."
            show-word-limit
            :maxlength="500"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="dialog1.visible  = false">取消</el-button>
        <el-button
          type="primary"
          @click="hitbackButton(form.remark)"
        >
          确认打回
        </el-button>
      </template>
    </el-dialog>


    <!-- 申请入驻对话框 -->
    <el-dialog title="申请入驻" v-model="dialogVisible" width="50%" :before-close="handleClose">
      <el-form ref="settleFormRef" :model="form" :rules="rules" label-width="120px" disabled>
        <el-form-item label="身份证正反面" prop="cardIdentity">
          <el-image
            v-if="cardFronUrl" :src="cardFronUrl" class="avatar"
            :preview-src-list=[cardFronUrl]
            style="width: 200px;height: 200px;margin-right: 30px"
          >
          </el-image>
          <el-image
            v-if="cartSideUrl" :src="cartSideUrl" class="avatar"
            :preview-src-list=[cartSideUrl]
            style="width: 200px;height: 200px"
          >
          </el-image>
        </el-form-item>

        <el-form-item label="联系人" prop="contactPerson">
          <el-input v-model="form.contactPerson" disabled="true" placeholder="联系人姓名"></el-input>
        </el-form-item>
        <el-form-item label="联系电话" prop="contactPhone">
          <el-input v-model="form.contactPhone" placeholder="请输入联系电话"></el-input>
        </el-form-item>

        <el-form-item label="电子邮箱" prop="email">
          <el-input v-model="form.email" placeholder="请输入电子邮箱"></el-input>
        </el-form-item>

        <el-form-item label="营业执照" prop="license">
          <el-image
            v-if="imageUrl" :src="imageUrl" class="avatar"
            :preview-src-list=[imageUrl]
            style="width: 200px;height: 200px"
          >
            <template #tip>
              <div class="upload-tip">请上传经营许可证</div>
            </template>
          </el-image>
        </el-form-item>

        <el-form-item style="display: none" label="营业执照名称:" prop="licenseName">
          <el-input v-model="form.licenseName"  placeholder="请输入营业执照名称"></el-input>
        </el-form-item>
        <el-form-item label="注册号:" prop="licenseNumber">
          <el-input v-model="form.licenseNumber" disabled="true" placeholder="营业执照注册号"></el-input>
        </el-form-item>
        <el-form-item label="企业名称" prop="companyName">
          <el-input v-model="form.companyName" disabled="true" placeholder="企业全称"></el-input>
        </el-form-item>
        <el-form-item label="店铺地址" prop="adress">
          <el-input v-model="form.adress" disabled="true" placeholder="店铺地址"></el-input>
        </el-form-item>
        <el-form-item label="企业类型" prop="companyType">
          <el-select v-model="form.companyType" placeholder="请选择企业类型">
            <el-option label="有限责任公司" value="1"></el-option>
            <el-option label="股份有限公司" value="2"></el-option>
            <el-option label="个人独资企业" value="3"></el-option>
            <el-option label="合伙企业" value="4"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="营业执照过期时间" prop="licenseTime">
          <el-date-picker
            v-model="form.licenseTime"
            date-format="YYYY/MM/DD"
            value-format="YYYY/MM/DD"
            type="date"
            placeholder="date"

          />
        </el-form-item>

        <el-form-item label="经营许可证" prop="businessLicense">
          <el-image
            v-if="Imagelicense" :src="Imagelicense" class="avatar"
            :preview-src-list=[Imagelicense]
            style="width: 200px;height: 200px"
          >
            <template #tip>
              <div class="upload-tip">请上传经营许可证</div>
            </template>
          </el-image>
        </el-form-item>

        <el-form-item label="经营许可证过期时间" prop="businessLicenseTime">
          <el-date-picker
            v-model="form.businessLicenseTime"
            date-format="YYYY/MM/DD"
            value-format="YYYY/MM/DD"
            type="date"
            placeholder="date"
          />
        </el-form-item>
        <el-form-item label="出版物许可证证照核实方式" prop="remark">
          <el-input
            type="textarea"
            :rows="3"
            v-model="form.remark"
            placeholder="请输入其他需要说明的信息"
          ></el-input>
        </el-form-item>
      </el-form>
    </el-dialog>


  </div>
</template>

<script setup name="Audit" lang="ts">
import { listAudit, getAudit, delAudit, addAudit, updateAudit, UpdateStatus, SendFailedInFo } from '@/api/zhishu/audit';
import { AuditVO, AuditQuery, AuditForm, AuditParams, SendFailed } from '@/api/zhishu/audit/types';
import { FormInstance, rowContextKey, UploadProps } from 'element-plus';
import { getImage } from '@/api/zhishu/image';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const auditList = ref<AuditVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const auditFormRef = ref<ElFormInstance>();
// 表单引用（用于校验）
const formRef = ref<ElFormInstance>()

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const dialogVisible = ref(false);

const dialog1 = reactive<DialogOption>({
  visible: false,
  title: ''
});

// 身份证正面
const cardFronUrl=ref('')
// 身份证反面
const cartSideUrl=ref('')


const cartUrl=ref([])
// 营业执照图片
const imageUrl = ref('')

const  cardIdentity=ref('')
const Imagelicense=ref('')

const initFormData: AuditForm = {
  userId: undefined,
  userName: undefined,
  adminId: undefined,
  status: undefined,
  companyName: undefined,
  companyType: undefined,
  contactPerson: undefined,
  contactPhone: undefined,
  email: undefined,
  license: undefined,
  remark: undefined,

}


const data = reactive<PageData<AuditForm, AuditQuery>>({
  form: {...initFormData},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    userId: undefined,
    userName: undefined,
    adminId: undefined,
    companyName : undefined,
    companyType : undefined,
    contactPerson : undefined,
    contactPhone : undefined,
    email : undefined,
    license : undefined,
    remark : undefined,
    status: undefined,
    params: {
    }
  },
  rules: {
  }
});

const statusList = [
  { value: '0', label: '通过 ' },
  { value: '1', label: '未通过 ' },
  { value: '2', label: '待审核' },
];


// 组合式API存储当前操作上下文
const operationContext = reactive<{
  currentId: string | number;
  currentData: Record<string, any>;
}>({
  currentId: '',
  currentData: {}
})


const { queryParams, form, rules} = toRefs(data);


// 打开弹窗方法
const handleOpenDialog = (id : number | string) => {
  operationContext.currentId  = id   // 存储关键ID
  console.log(operationContext.currentId)
  // operationContext.currentData  = { ...rowData } // 深拷贝避免引用污染
  dialog1.visible  = true
}







// 文件上传成功处理
const handleUploadSuccess: UploadProps['onSuccess'] = (response, file) => {
  form.value.license = response.data.url; // 根据实际API返回调整
  ElMessage.success('上传成功');
};

// 文件上传前验证
const beforeUpload: UploadProps['beforeUpload'] = (file) => {
  const isJPG = file.type === 'image/jpeg' || file.type === 'image/png';
  const isLt2M = file.size / 1024 / 1024 < 2;

  if (!isJPG) {
    ElMessage.error('上传图片只能是 JPG/PNG 格式!');
  }
  if (!isLt2M) {
    ElMessage.error('上传图片大小不能超过 2MB!');
  }
  return isJPG && isLt2M;
};


/** 查询审核列表 */
const getList = async () => {
  loading.value = true;
  const res = await listAudit(queryParams.value);
  auditList.value = res.rows;
  total.value = res.total;
  loading.value = false;
}

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
}

/** 表单重置 */
const reset = () => {
  form.value = {...initFormData};
  auditFormRef.value?.resetFields();
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
}

/** 多选框选中数据 */
const handleSelectionChange = (selection: AuditVO[]) => {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = "添加审核";
}

const  handleQueryInfo=async (row?:AuditVO)=>{
  reset();
  const _id = row?.id || ids.value[0]
  const res = await getAudit(_id);
  imageUrl.value=await getImage(res.data.license);
  Imagelicense.value=res.data.businessLicense
  console.log(Imagelicense.value)
  // Imagelicense.value=await getImage(res.data.businessLicense);
  try {
    const urlArray = JSON.parse(res.data.cardIdentity);
    cardFronUrl.value = await getImage(urlArray[0]); // 第一个URL
    cartSideUrl.value = await getImage(urlArray[1]); // 第二个URL
  } catch (error) {

  }
  Object.assign(form.value, res.data);
}

/** 修改按钮操作 */
const handleUpdate = async (row?: AuditVO) => {
  reset();
  const _id = row?.id || ids.value[0]
  const res = await getAudit(_id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = "修改审核";
}

/** 审核未通过按钮操作*/
const handleUpdateStatus1=async (id: number | string,status: '0' | '1')=> {
  // await proxy?.$modal.confirm('是否审核编号为"' + id + '"的数据项？').finally(() => loading.value = false);
  dialog1.visible = true;
  const params: AuditParams = {
    id: id,
    status: status
  }


  // 发送PUT请求（根据后端接口设计选择POST/PUT）
  const response = UpdateStatus(params)
  proxy?.$modal.msgSuccess("审核未通过");
  await getList();
}






/** 审核通过按钮操作*/
const handleUpdateStatus=async (id: number | string,status: '0' | '1')=> {
  await proxy?.$modal.confirm('是否审核编号为"' + id + '"的数据项？').finally(() => loading.value = false);
  // dialog1.visible = true;
  const params: AuditParams = {
    id: id,
    status: status
  }

  // 发送PUT请求（根据后端接口设计选择POST/PUT）
  const response = UpdateStatus(params)
  proxy?.$modal.msgSuccess("审核通过");
  await getList();
  await getList();
}








/**
 * 打回按钮

 */
const hitbackButton=async (remark:string) => {
// console.log(remark)
  const payload:SendFailed = {
    id: operationContext.currentId,  // 获取缓存的ID
    status:"1",
    remark
    // originalData: operationContext.currentData
  }
  console.log(payload)
  SendFailedInFo(payload);
  dialog1.visible = false;

}






/** 提交按钮 */
const submitForm = () => {
  auditFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateAudit(form.value).finally(() =>  buttonLoading.value = false);
      } else {
        await addAudit(form.value).finally(() =>  buttonLoading.value = false);
      }
      proxy?.$modal.msgSuccess("操作成功");
      dialog.visible = false;
      await getList();
    }
  });
}

/** 删除按钮操作 */
const handleDelete = async (row?: AuditVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除审核编号为"' + _ids + '"的数据项？').finally(() => loading.value = false);
  await delAudit(_ids);
  proxy?.$modal.msgSuccess("删除成功");
  await getList();
}

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download('zhishu/audit/export', {
    ...queryParams.value
  }, `audit_${new Date().getTime()}.xlsx`)
}

onMounted(() => {
  getList();
});
</script>
<style>
/* 文本溢出处理 */
.truncate-cell {
  width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.avatar-uploader{
  border: 1px dashed var(--el-border-color);
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: var(--el-transition-duration-fast);
}

.avatar-uploader:hover {
  border-color: var(--el-color-primary);
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 140px;
  height: 120px;
  text-align: center;
}
.upload-tip {
  margin-top: 8px;
  width: 100%; /* 与上传区域同宽 */
  text-align: center; /* 文字居中 */
}
</style>
<style scoped>
.avatar-uploader .avatar {
  width: 178px;
  height: 178px;
  display: block;
}
</style>
