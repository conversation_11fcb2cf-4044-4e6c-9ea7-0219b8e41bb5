<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="店铺类型" prop="shopType">
              <el-select v-model="queryParams.shopType" placeholder="请选择店铺类型" clearable>
                <el-option v-for="dict in t_shop_type" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="分组" prop="shopGroup">
              <el-input v-model="queryParams.shopGroup" placeholder="请输入分组" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="店铺名称" prop="shopName">
              <el-input v-model="queryParams.shopName" placeholder="请输入店铺名称" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="三方名称" prop="shopAliasName">
              <el-input v-model="queryParams.shopAliasName" placeholder="请输入三方名称" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="是否授权 " prop="shopAuthorize">
              <el-select v-model="queryParams.shopAuthorize" placeholder="请选择是否授权" clearable>
                <el-option v-for="dict in t_shop_authorize" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>

            <el-form-item label="店铺状态" prop="status">
              <el-select v-model="queryParams.status" placeholder="请选择店铺状态" clearable>
                <el-option v-for="dict in sys_normal_disable" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['zhishu:shop:add']">新增 </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['zhishu:shop:edit']">修改 </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['zhishu:shop:remove']"
              >删除
            </el-button>
          </el-col>
          <!-- <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['zhishu:shop:export']">导出</el-button>
          </el-col> -->
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="shopList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="店铺编码" align="center" prop="id" v-if="true" width="200" />
        <el-table-column label="店铺类型" align="center" prop="shopType">
          <template #default="scope">
            <dict-tag :options="t_shop_type" :value="scope.row.shopType" />
          </template>
        </el-table-column>
        <el-table-column label="分组" align="center" prop="shopGroup" />
        <el-table-column label="店铺名称" align="center" prop="shopName" />
        <el-table-column label="三方名称" align="center" prop="shopAliasName" />
        <el-table-column label="上传限制" align="center" prop="skuSpec">
          <template #default="scope">
            <span v-if="scope.row.shopType == 2">110000</span>
            <span v-else-if="!scope.row.skuSpec || scope.row.skuSpec.includes('7天')">500</span>
            <span v-else-if="scope.row.skuSpec.includes('专营店') && !scope.row.skuSpec.includes('7天')">110000</span>
            <span v-else>{{ scope.row.skuSpec }}</span>
          </template>
        </el-table-column>

        <el-table-column label="是否授权" align="center" prop="shopAuthorize" width="170">
          <template #default="scope">
            <span v-if="scope.row.shopAuthorize==='0'" style="color: red">未授权,请点击</span>
            <span v-else-if="scope.row.shopAuthorize==='1'" style="color: #0d84ff">已授权</span>
            <span v-else-if="scope.row.shopAuthorize==='2'" style="color: #ef9c00">已过期，请再次点击</span>
            <el-button link :type="typeMapping[scope.row.shopAuthorize]"   icon="Pointer" v-loading="loadingAoth" @click="toCode(scope.row)" v-hasPermi="['zhishu:shop:authorize']"></el-button>
          </template>
        </el-table-column>
        <el-table-column label="到期时间" align="center" prop="expirationTime" width="160">
          <template #default="scope">
            <span>{{ parseTime(scope.row.expirationTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="添加时间" align="center" prop="addTime" width="160">
          <template #default="scope">
            <span>{{ parseTime(scope.row.addTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="店铺状态" align="center" prop="status">
          <template #default="scope">
            <dict-tag :options="sys_normal_disable" :value="scope.row.status" />
          </template>
        </el-table-column>
        <el-table-column label="自动发布" align="center" prop="autoAdd">
          <template #default="scope">
           <span v-if="scope.row.autoAdd==='0'">未开启</span>
            <span v-else-if="scope.row.autoAdd==='1'">已开启</span>
            <span v-else>未设置</span>
          </template>
        </el-table-column>

        <el-table-column label="订单状态" align="center" prop="isSynOrder">
          <template #default="scope">
            <el-switch
              :model-value="scope.row.isSynOrder === 1"
              inline-prompt
              active-text="开启"
              :active-value="true"
              inactive-text="关闭"
              :inactive-value="false"
              size="large"
              style="--el-switch-on-color: rgb(64, 158, 255)"
              @change="(val) => handleOrderStatusChange(scope.row, val)"
            />
          </template>
        </el-table-column>

        <el-table-column label="设置" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="设置" placement="top">
              <el-button link type="primary" icon="Setting" @click="shopSetUp(scope.row)" v-hasPermi="['zhishu:shop:setUp']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>


        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">

            <el-tooltip content="拉取" placement="top" v-if="scope.row.shopType==='2'||scope.row.shopType==='3'">
              <el-button link type="primary" icon="BottomRight" @click="importRule(scope.row)" :disabled="scope.row.hasSyncFile" v-hasPermi="['zhishu:shop:pull']"></el-button>
            </el-tooltip>

            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['zhishu:shop:edit']"></el-button>
            </el-tooltip>

            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['zhishu:shop:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改店铺信息对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body>
      <el-form ref="shopFormRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="店铺类型" prop="shopType">
          <el-select v-model="form.shopType" placeholder="请选择店铺类型">
            <el-option v-for="dict in t_shop_type" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item :class="{ isHide: (form.shopType == 1 || form.shopType == undefined) && addOrEditMark == true }" label="店铺名称" prop="shopName">
          <el-input v-model="form.shopName" placeholder="请输入店铺名称" />
        </el-form-item>
        <el-form-item :class="{ isHide: form.shopType != 2 }" label="登录账号" prop="account">
          <el-input v-model="form.account" placeholder="请输入登录账号" />
        </el-form-item>

        <el-form-item :class="{ isHide: form.shopType != 3 }" label="AppKey" prop="account">
          <el-input v-model="form.account" placeholder="请输入AppKey" />
        </el-form-item>
        <el-form-item :class="{ isHide: form.shopType != 3 }" label="AppSecret" prop="password">
          <el-input v-model="form.password" placeholder="请输入AppSecret" />
        </el-form-item>

        <el-form-item :class="{ isHide: form.shopType != 2 }" label="账号密码" prop="password">
          <el-input v-model="form.password" placeholder="请输入账号密码" />
        </el-form-item>
        <el-form-item :class="{ isHide: (form.shopType == 1 || form.shopType == undefined) && addOrEditMark == true }" label="分组" prop="shopGroup">
          <el-input v-model="form.shopGroup" placeholder="请输入分组" />
        </el-form-item>
        <el-form-item :class="{ isHide: (form.shopType == 1 || form.shopType == undefined) && addOrEditMark == true }" label="店铺状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio v-for="dict in sys_normal_disable" :key="dict.value" :value="dict.value">{{ dict.label }} </el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 设置店铺详细信息 -->
    <el-dialog :title="dialog.title" v-model="dialog.shopSetup" width="800px" append-to-body>
      <!-- <template> -->
      <el-tabs v-model="activeName" class="demo-tabs" @tab-click="handleClick">
        <el-tab-pane label="基本设置" name="first">
          <el-form ref="shopDetailFormRef" :model="formDetail" :rules="rulesDetail" label-width="80px" style="height: 630px; width: 100%">
            <el-row>
              <el-col :span="16">
                <el-form-item label="物流模板" prop="templateId" label-width="100px">
                  <el-select v-model="formDetail.templateId" placeholder="请选择" size="large" style="width: 100%">
                    <el-option v-for="item in logisticsTemplateList" :key="item.value" :label="item.label" :value="item.value" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="6" style="margin-left: 20px">
                <el-button style="background-color: #409eff; color: #fff" size="large" @click="chooseTemplate(formDetail.shopType)"> 更新店铺模板 </el-button>
              </el-col>
            </el-row>
            <el-row :class="{ isHide: formDetail.shopType != '1' }">
              <el-col :span="7">
                <el-form-item label="默认库存" prop="stockDeff" label-width="100px" style="width: 100%">
                  <el-input-number
                    v-model="formDetail.stockDeff"
                    :min="0"
                    controls-position="right"
                    size="large"
                    placeholder="请输入"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="7" >
                <el-form-item  label="两件折扣" prop="twoDiscount" label-width="100px" style="width: 100%">
                  <el-input-number
                    v-model="formDetail.twoDiscount"
                    :min="50"
                    :max="99"
                    controls-position="right"
                    size="large"
                    placeholder="请输入"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="9">
                <div style="margin-top: 10px; margin-left: 10px; color: #999">填写50-99的数，例如：9.5折请填写 95</div>
              </el-col>
            </el-row>

              <!-- <el-form-item label="价格区间" prop="highPrice" label-width="100px" style="width: 100%">
                <el-row>
                  <el-col :span="11">
                    <el-input-number v-model="formDetail.lowPrice" :min="0" :max="100" controls-position="right" size="large" style="width: 100%" />
                  </el-col>
                  <el-col :span="1"></el-col>
                  <el-col :span="11">
                    <el-input-number
                      v-model="formDetail.highPrice"
                      :min="0"
                      controls-position="right"
                      size="large"
                      placeholder="请输入"
                      style="width: 100%"
                    />
                  </el-col>
                </el-row>
              </el-form-item>
              <div style="margin: -18px 0px 14px 101px; color: #999">比如填写5和10，那么低于5和高于10直接过滤掉</div> -->
            <el-row>
              <el-col :span="16">
                <el-form-item :label="formDetail.shopType != '1' ? '销售价格' : '拼单价格'" prop="saleTemplateId" label-width="100px">
                  <el-select v-model="formDetail.saleTemplateId" placeholder="请选择" size="large" style="width: 100%">
                    <el-option v-for="item in priceTemplateList" :key="item.value" :label="item.label" :value="item.value" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="6" style="margin-left: 20px">
                <el-button style="background-color: #409eff; color: #fff" size="large" @click="initPriceTemplate()"> 更新价格模板 </el-button>
              </el-col>
            </el-row>


            <el-form-item :class="{ isHide: formDetail.shopType != '1' }" label="是否预售" prop="presale" label-width="100px" style="width: 100%">
              <el-radio-group v-model="formDetail.presale">
                <el-radio v-for="dict in t_presale" :key="dict.value" :value="dict.value">{{ dict.label }}</el-radio>
              </el-radio-group>
            </el-form-item>

            <el-row :class="{ isHide: formDetail.shopType != '1' }">
              <el-col :span="6">
                <el-form-item label="假一赔十" prop="fake" label-width="100px">
                  <el-switch
                    v-model="formDetail.fake"
                    inline-prompt
                    active-text="开启"
                    active-value="1"
                    inactive-text="关闭"
                    inactive-value="0"
                    size="large"
                    style="--el-switch-on-color: rgb(64, 158, 255)"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="七天无理由" prop="sevenDays" label-width="100px">
                  <el-switch
                    v-model="formDetail.sevenDays"
                    inline-prompt
                    active-text="开启"
                    active-value="1"
                    inactive-text="关闭"
                    inactive-value="0"
                    size="large"
                    style="--el-switch-on-color: rgb(64, 158, 255)"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="6" style="margin-left: 20px">
                <el-form-item label="是否二手" prop="isSecondHand" label-width="100px">
                  <el-switch
                    v-model="formDetail.isSecondHand"
                    inline-prompt
                    active-text="是"
                    active-value="1"
                    inactive-text="否"
                    inactive-value="0"
                    size="large"
                    style="--el-switch-on-color: rgb(64, 158, 255)"
                  />
                </el-form-item>
              </el-col>
            </el-row>

            <el-form-item label="发货时间" prop="deliveryTime" label-width="100px" style="width: 100%">
              <el-radio-group v-model="formDetail.deliveryTime">
                <el-radio v-for="dict in t_delivery_time" :key="dict.value" :value="dict.value">{{ dict.label }} </el-radio>
              </el-radio-group>
            </el-form-item>


            <!-- 孔夫子店铺参数 -->
            <el-row  :class="{ isHide: formDetail.shopType != '2' }">
              <el-col :span="20">
                <el-form-item  label="默认品相" prop="conditionDef" label-width="100px">
                  <el-input-number v-model="formDetail.conditionDef" :min="0" :max="100" controls-position="right" size="large" style="width: 30%" />
                </el-form-item>
                <div  style="margin: -18px 0px 14px 101px; color: #999">取值范围：10,20,30,40,50,60,65,70,75,80,85,90,95,100;</div>
              </el-col>
            </el-row>

            <el-form-item :class="{ isHide: formDetail.shopType != '2' }" label="品相描述" prop="conditionDes" label-width="100px">
              <el-input v-model="formDetail.conditionDes" placeholder="请输入品相描述" style="width:390px;height:40px"/>
            </el-form-item>

            <el-form-item :class="{ isHide: formDetail.shopType != '2' }" label="推荐语" prop="recommend" label-width="100px">
              <el-input v-model="formDetail.recommend" placeholder="请输入推荐语" style="width:390px;height:40px"/>
            </el-form-item>

            <el-row >
              <el-col :span="6" :class="{ isHide: formDetail.shopType != '2' }">
                <el-form-item label="是否包邮" prop="isParcel" label-width="100px">
                  <el-switch
                    v-model="formDetail.isParcel"
                    inline-prompt
                    active-text="开启"
                    active-value="1"
                    inactive-text="关闭"
                    inactive-value="0"
                    size="large"
                    style="--el-switch-on-color: rgb(64, 158, 255)"
                  />
                </el-form-item>
              </el-col >
              <el-col :span="6">
                <el-form-item label="自动发布" prop="autoAdd" label-width="100px">
                  <el-switch
                    v-model="formDetail.autoAdd"
                    inline-prompt
                    active-text="开启"
                    active-value="1"
                    inactive-text="关闭"
                    inactive-value="0"
                    size="large"
                    style="--el-switch-on-color: rgb(64, 158, 255)"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12" :class="{ isHide: formDetail.shopType != '2' }">
                <el-form-item :class="{ isHide: formDetail.shopType != '2' }" label="图书模板" prop="bookTemplate" label-width="100px" style="width: 100%">
                  <el-radio-group v-model="formDetail.bookTemplate">
                    <el-radio v-for="dict in t_book_template" :key="dict.value" :value="dict.value">{{ dict.label }}</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row  :class="{ isHide: formDetail.shopType != '2' }">
              <el-col :span="20">
                <el-form-item  label="商品重量" prop="bookWeight" label-width="100px">
                  <el-input-number v-model="formDetail.bookWeight" :min="0" :max="9999.99" controls-position="right" size="large" style="width: 30%" />
                </el-form-item>
                <div  style="margin: -18px 0px 14px 101px; color: #999">当选择的是按重量的运费模板时必填。取值范围：0.01～9999.99，单位：千克</div>
              </el-col>
            </el-row>

             <el-row  :class="{ isHide: formDetail.shopType != '2' }">
              <el-col :span="20">
                <el-form-item  label="标准本数" prop="standardNumber" label-width="100px">
                  <el-input-number v-model="formDetail.standardNumber" :min="0" :max="9999.99" controls-position="right" size="large" style="width: 30%" />
                </el-form-item>
                <div  style="margin: -18px 0px 14px 101px; color: #999">当选择的是按标准本的运费模板时必填。取值范围：0.01～9999.99</div>
              </el-col>
            </el-row>


          </el-form>
        </el-tab-pane>
        <el-tab-pane label="图片/视频设置" name="second">
          <el-form
            ref="shopDetailFormRef"
            :model="formDetail"
            :rules="rulesDetail"
            label-width="80px"
            style="height: 500px; width: 100%; overflow: auto"
          >
            <el-row>
              <el-col :span="11">
                <el-form-item label="封面水印" label-width="100px" style="width: 100%">
                  <el-upload
                    v-model:file-list="fileList1"
                    multiple
                    :action="uploadImgUrl"
                    list-type="picture-card"
                    :headers="headers"
                    :on-success="handleAvatarSuccess"
                    :on-preview="handlePictureCardPreview"
                    :on-remove="handleRemove"
                    limit="1"
                    accept=".png"
                    @click="changeType(1)"
                  >
                    <el-icon>
                      <Plus />
                    </el-icon>
                  </el-upload>

                  <el-dialog v-model="dialogVisible">
                    <img w-full :src="dialogImageUrl" alt="Preview Image" />
                  </el-dialog>
                </el-form-item>
              </el-col>
              <el-col :span="11" :class="{ isHide: formDetail.shopType != '1' }">
                <el-form-item  label="商详水印" prop="businessDetailsWatermark" label-width="100px" style="width: 100%">
                  <el-upload
                    v-model:file-list="fileList2"
                    multiple
                    :action="uploadImgUrl"
                    list-type="picture-card"
                    :headers="headers"
                    :on-success="handleAvatarSuccess"
                    :on-preview="handlePictureCardPreview"
                    :on-remove="handleRemove"
                    limit="1"
                    accept=".png"
                    @click="changeType(2)"
                  >
                    <el-icon>
                      <Plus />
                    </el-icon>
                  </el-upload>

                  <el-dialog v-model="dialogVisible">
                    <img w-full :src="dialogImageUrl" alt="Preview Image" />
                  </el-dialog>
                </el-form-item>
              </el-col>
            </el-row>
            <el-form-item :class="{ isHide: formDetail.shopType != '1' }" label="商详头图" label-width="100px" style="width: 100%">
              <el-upload
                v-model:file-list="fileList3"
                multiple
                :before-upload="beforeAvatarUpload"
                :action="uploadImgUrl"
                list-type="picture-card"
                :headers="headers"
                :on-success="handleAvatarSuccess"
                :on-preview="handlePictureCardPreview"
                :on-remove="handleRemove"
                limit="3"
                @click="changeType(3)"
              >
                <el-icon>
                  <Plus />
                </el-icon>
              </el-upload>

              <el-dialog v-model="dialogVisible">
                <img w-full :src="dialogImageUrl" alt="Preview Image" />
              </el-dialog>
            </el-form-item>

            <el-form-item :class="{ isHide: formDetail.shopType != '1' }" label="商详尾图" label-width="100px" style="width: 100%">
              <el-upload
                v-model:file-list="fileList4"
                multiple
                :before-upload="beforeAvatarUpload"
                :action="uploadImgUrl"
                list-type="picture-card"
                :headers="headers"
                :on-success="handleAvatarSuccess"
                :on-preview="handlePictureCardPreview"
                :on-remove="handleRemove"
                limit="3"
                @click="changeType(4)"
              >
                <el-icon>
                  <Plus />
                </el-icon>
              </el-upload>

              <el-dialog v-model="dialogVisible">
                <img w-full :src="dialogImageUrl" alt="Preview Image" />
              </el-dialog>
            </el-form-item>

            <el-form-item :class="{ isHide: formDetail.shopType != '1' }" label="轮播尾图" label-width="100px" style="width: 100%">
              <el-upload
                v-model:file-list="fileList5"
                multiple
                :before-upload="beforeAvatarUpload"
                :action="uploadImgUrl"
                list-type="picture-card"
                :headers="headers"
                :on-success="handleAvatarSuccess"
                :on-preview="handlePictureCardPreview"
                :on-remove="handleRemove"
                limit="3"
                @click="changeType(5)"
              >
                <el-icon>
                  <Plus />
                </el-icon>
              </el-upload>

              <el-dialog v-model="dialogVisible">
                <img w-full :src="dialogImageUrl" alt="Preview Image" />
              </el-dialog>
            </el-form-item>

            <el-form-item label="轮播数量" prop="carouselNum" label-width="100px" style="width: 100%">
              <el-radio-group v-model="formDetail.carouselNum">
                <el-radio v-for="dict in t_open_or_close" :key="dict.value" :value="dict.value">{{ dict.label }} </el-radio>
              </el-radio-group>
            </el-form-item>
            <div style="margin: -18px 0px 14px 101px; color: #999">开启后会使用详情图、主图、轮播尾图等数据，让轮播图至少8张</div>

            <el-form-item label="白底图" prop="whitePicture" label-width="100px" style="width: 100%">
              <el-radio-group v-model="formDetail.whitePicture">
                <el-radio v-for="dict in t_open_or_close" :key="dict.value" :value="dict.value">{{ dict.label }} </el-radio>
              </el-radio-group>
            </el-form-item>
            <div style="margin: -18px 0px 14px 101px; color: #999">白底图用于商品参加活动，无此需求的可不用添加白底图</div>

            <el-form-item label="水印位置" prop="watermarkPosition" label-width="100px" style="width: 100%">
              <el-radio-group v-model="formDetail.watermarkPosition">
                <el-radio v-for="dict in t_watermark_position" :key="dict.value" :value="dict.value">{{ dict.label }} </el-radio>
              </el-radio-group>
            </el-form-item>
            <div style="margin: -18px 0px 14px 101px; color: #999">开启轮播数量之后这里选择只加一个水印还是全部都加上水印</div>

            <el-form-item label="开启详情图" prop="isDetailsImg" label-width="100px" style="width: 100%">
              <el-radio-group v-model="formDetail.isDetailsImg">
                <el-radio v-for="dict in t_open_or_close" :key="dict.value" :value="dict.value">{{ dict.label }} </el-radio>
              </el-radio-group>
            </el-form-item>
            <div style="margin: -18px 0px 14px 101px; color: #999">开启之后会添加作者简介、图书简介、图书目录等图片到详情中</div>

            <el-form-item :class="{ isHide: formDetail.shopType != '1' }" label="目录详情" prop="isCatalogueDetails" label-width="100px" style="width: 100%">
              <el-radio-group v-model="formDetail.isCatalogueDetails">
                <el-radio v-for="dict in t_open_or_close" :key="dict.value" :value="dict.value">{{ dict.label }} </el-radio>
              </el-radio-group>
            </el-form-item>
            <div :class="{ isHide: formDetail.shopType != '1' }" style="margin: -18px 0px 14px 101px; color: #999">暂时仅适用于淘宝，关闭详情，作者，目录等数据，关闭后可降低违规概率</div>

            <el-form-item :class="{ isHide: formDetail.shopType != '1' }" label="信息图片" prop="isInformationImg" label-width="100px" style="width: 100%">
              <el-radio-group v-model="formDetail.isInformationImg">
                <el-radio v-for="dict in t_open_or_close" :key="dict.value" :value="dict.value">{{ dict.label }} </el-radio>
              </el-radio-group>
            </el-form-item>
            <div :class="{ isHide: formDetail.shopType != '1' }" style="margin: -18px 0px 14px 101px; color: #999">多多适用，关闭后将会用主图替代第一张详情图，就是有图书开本信息显示那张图片</div>
          </el-form>
        </el-tab-pane>
        <el-tab-pane label="标题设置" name="third">
          <el-form ref="shopDetailFormRef" :model="formDetail" :rules="rulesDetail" label-width="80px" style="height: 500px; width: 100%">
            <div :class="{ isHide: formDetail.shopType != '1' }" style="margin: 0px 0px 14px 20px; color: red">
              拼多多限制商品标题总长度不可以大于60字符(每个中文占2字符，其它占1字符)，请谨慎操作
            </div>
            <div :class="{ isHide: formDetail.shopType != '2' }" style="margin: 0px 0px 14px 20px; color: red">
              孔网要求商品标题总长度不可以大于200字符，请谨慎操作
            </div>
            <el-row>
              <el-col :span="16">
                <el-form-item label="标题前缀" prop="titlePrefix" label-width="100px">
                  <el-input v-model="formDetail.titlePrefix" placeholder="请输入标题前缀" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="16">
                <el-form-item label="标题后缀" prop="titleSuffix" label-width="100px">
                  <el-input v-model="formDetail.titleSuffix" placeholder="请输入标题后缀" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="16">
                <el-form-item label="标题过滤" prop="titleFilter" label-width="100px">
                  <el-input v-model="formDetail.titleFilter" placeholder="请输入标题过滤" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="16">
                <el-form-item label="货号前缀" prop="itemNumberPrefix" label-width="100px">
                  <el-input v-model="formDetail.itemNumberPrefix" placeholder="请输入货号前缀" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-form-item label="标题组成" prop="titleConsistOf" label-width="100px">
              <!-- <el-checkbox-group v-model="formDetail.titleConsistOf">
                <el-checkbox v-for="dict in t_title_consist_of" :key="dict.value" :label="dict.value">
                  {{ dict.label }}
                </el-checkbox>
              </el-checkbox-group> -->

                 <draggable v-model="titleConsistOfArray" @end="onEnd" item-key="id">
                  <template #item="{ element }">
                    <el-checkbox v-model="element.checked">
                      {{ element.name }}
                    </el-checkbox>
                  </template>
                </draggable>
            </el-form-item>

            <el-form-item label="间隔字符" prop="spaceCharacter" label-width="100px" style="width: 100%">
              <el-radio-group v-model="formDetail.spaceCharacter">
                <el-radio v-for="dict in t_space_character" :key="dict.value" :value="dict.value">{{ dict.label }} </el-radio>
              </el-radio-group>
            </el-form-item>

            <el-form-item  label="自动截断" prop="autoTruncation" label-width="100px" style="width: 100%">
              <el-radio-group v-model="formDetail.autoTruncation">
                <el-radio v-for="dict in t_open_or_close" :key="dict.value" :value="dict.value">{{ dict.label }} </el-radio>
              </el-radio-group>
            </el-form-item>
            <div :class="{ isHide: formDetail.shopType != '1' }" style="margin: -18px 0px 14px 101px; color: #999">当标题长度大于60的时候，自动截断标题到长度为刚好60</div>
            <div :class="{ isHide: formDetail.shopType != '2' }" style="margin: -18px 0px 14px 101px; color: #999">当标题长度大于200的时候，自动截断标题到长度为刚好200</div>
          </el-form>
        </el-tab-pane>
        <el-tab-pane label="过滤设置" name="fourth">
          <el-form ref="shopDetailFormRef" :model="formDetail" :rules="rulesDetail" label-width="80px" style="height: 500px; width: 100%">
            <el-form-item label="我的设置" prop="mySetUp" label-width="100px">
              <el-checkbox-group v-model="formDetail.mySetUp">
                <el-checkbox v-for="dict in t_shop_set_up" :key="dict.value" :label="dict.value">
                  {{ dict.label }}
                </el-checkbox>
              </el-checkbox-group>
            </el-form-item>
            <el-form-item label="系统设置" prop="systemSetUp" label-width="100px">
              <el-checkbox-group v-model="formDetail.systemSetUp">
                <el-checkbox v-for="dict in t_shop_set_up" :key="dict.value" :label="dict.value">
                  {{ dict.label }}
                </el-checkbox>
              </el-checkbox-group>
            </el-form-item>
            <el-form-item label="过滤套装" prop="filterSuit" label-width="100px" style="width: 100%">
              <el-radio-group v-model="formDetail.filterSuit">
                <el-radio v-for="dict in t_yes_no" :key="dict.value" :value="dict.value">{{ dict.label }}</el-radio>
              </el-radio-group>
            </el-form-item>
            <div style="margin: -18px 0px 14px 101px; color: #999">套装图片或图书将不会被上架</div>

            <el-form-item label="无图过滤" prop="filtering" label-width="100px" style="width: 100%">
              <el-radio-group v-model="formDetail.filtering">
                <el-radio v-for="dict in t_yes_no" :key="dict.value" :value="dict.value">{{ dict.label }}</el-radio>
              </el-radio-group>
            </el-form-item>
            <div style="margin: -18px 0px 14px 101px; color: #999">
              开启后会过滤无图数据，关闭后会使用自动生成的主图进行上传， <span style="color: red">无图数据我们会尽快更新</span>
            </div>
          </el-form>
        </el-tab-pane>
        <el-tab-pane label="其他设置" name="five">
          <el-form ref="shopDetailFormRef" :model="formDetail" :rules="rulesDetail" label-width="80px" style="height: 500px; width: 100%">
            <el-form-item label="删除保护" prop="isDelProtect" label-width="100px" style="width: 100%">
              <el-radio-group v-model="formDetail.isDelProtect">
                <el-radio v-for="dict in t_open_or_close" :key="dict.value" :value="dict.value">{{ dict.label }} </el-radio>
              </el-radio-group>
            </el-form-item>
            <div style="margin: -18px 0px 14px 101px; color: #999">开启之后会对有销量的商品会进行保护，不会删除</div>
            <el-form-item label="下架保护" prop="isRemoveProtect" label-width="100px" style="width: 100%">
              <el-radio-group v-model="formDetail.isRemoveProtect">
                <el-radio v-for="dict in t_open_or_close" :key="dict.value" :value="dict.value">{{ dict.label }} </el-radio>
              </el-radio-group>
            </el-form-item>
            <div style="margin: -18px 0px 14px 101px; color: #999">开启之后会对有销量的商品会进行保护，不会下架</div>
            <el-form-item label="图书定价" prop="bootPrice" label-width="100px" style="width: 100%">
              <el-radio-group v-model="formDetail.bootPrice">
                <el-radio v-for="dict in t_open_or_close" :key="dict.value" :value="dict.value">{{ dict.label }} </el-radio>
              </el-radio-group>
            </el-form-item>
            <div style="margin: -18px 0px 14px 101px; color: #999">
              当上传文件中的价格缺失或错误的时候自动使用图书定价(以图书定价为基础再使用价格模板进行计算)
            </div>
            <el-form-item label="自有图片" prop="ownPictures" label-width="100px" style="width: 100%">
              <el-radio-group v-model="formDetail.ownPictures">
                <el-radio v-for="dict in t_open_or_close" :key="dict.value" :value="dict.value">{{ dict.label }} </el-radio>
              </el-radio-group>
            </el-form-item>
            <div style="margin: -18px 0px 14px 101px; color: #999">开启之后，上传图书将会优先使用自有图片库或详细同步图书中的图片数据</div>

            <el-form-item  label="常驻地区" prop="districtId" label-width="100px" style="width: 100%">
              <el-cascader :options="districtList" v-model="formDetail.districtId" :show-all-levels="false" />
            </el-form-item>


            <el-form-item :class="{ isHide: formDetail.shopType != '1' }" label="自定义规格" prop="ownPictures" label-width="100px" style="width: 100%">
              <el-row>
                <el-col :span="15">
                  <el-input v-model="zdyGg" disabled />
                </el-col>
                <el-col :span="5" style="margin-left: 10px">
                  <el-button type="primary" @click="setZdySpac">设置规格</el-button>
                </el-col>
              </el-row>
            </el-form-item>
          </el-form>
        </el-tab-pane>
      </el-tabs>
      <!-- </template> -->
      <!-- 底部按钮 -->
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitFormDetail">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog v-model="showGuigePopup" title="自定义规格设置" width="600">
      <el-form ref="shopDetailFormRef" :model="formGg" :rules="rulesDetail" label-width="80px" style="height: 600px; width: 100%">
        <el-form-item label="规格类型" prop="ownPictures" label-width="100px" style="width: 100%">
          <div class="ggTypeBox">
            <div
              class="typeItem"
              v-for="spec in specList"
              :class="{ activeClass: spec.parent_spec_id === chooseIndex }"
              :key="spec.parent_spec_id"
              @click="chooseItem(spec.parent_spec_id, spec.parent_spec_name)"
            >
              {{ spec.parent_spec_name }}
            </div>
          </div>
        </el-form-item>

        <el-form-item label="规格组成" prop="specCompose" label-width="100px" style="width: 100%">
          <el-radio-group v-model="formGg.specCompose">
            <el-radio v-for="dict in t_ggzc" :key="dict.value" :value="dict.value">{{ dict.label }}</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item :class="{ isHide: formGg.specCompose != 0 }" label="规格名称" prop="specName" label-width="100px">
          <el-input v-model="formGg.specName" placeholder="请输入规格名称" />
        </el-form-item>

        <el-form-item :class="{ isHide: formGg.specCompose == 0 }" label="规格前缀" prop="specPrefix" label-width="100px">
          <el-input v-model="formGg.specPrefix" placeholder="请输入规格前缀" />
        </el-form-item>

        <el-form-item :class="{ isHide: formGg.specCompose == 0 }" label="规格后缀" prop="specSuffix" label-width="100px">
          <el-input v-model="formGg.specSuffix" placeholder="请输入规格后缀" />
        </el-form-item>

        <el-form-item label="规格编码组成" prop="specCodeCompose" label-width="100px" style="width: 100%">
          <el-radio-group v-model="formGg.specCodeCompose">
            <el-radio v-for="dict in t_ggzc" :key="dict.value" :value="dict.value">{{ dict.label }}</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item :class="{ isHide: formGg.specCodeCompose == 0 }" label="规格编码前缀" prop="specCodePrefix" label-width="100px">
          <el-input v-model="formGg.specCodePrefix" placeholder="请输入规格编码前缀" />
        </el-form-item>

        <el-form-item :class="{ isHide: formGg.specCodeCompose == 0 }" label="规格编码后缀" prop="specCodeSuffix" label-width="100px">
          <el-input v-model="formGg.specCodeSuffix" placeholder="请输入规格编码后缀" />
        </el-form-item>

        <el-form-item label="SKU水印图片" label-width="100px" style="width: 100%">
          <el-upload
            class="avatar-uploader"
            multiple
            :action="SkuUploadImgUrl"
            list-type="picture-card"
            :show-file-list="false"
            :headers="headers"
            :on-success="skuHandleAvatarSuccess"
            accept=".jpg,.png"
            @click="changeType(7)"
          >
            <img v-if="skuImageUrl" :src="skuImageUrl" class="avatar" />
            <el-icon v-else class="avatar-uploader-icon">
              <Plus />
            </el-icon>
          </el-upload>
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="ggSetSubmit">确 定</el-button>
          <el-button @click="ggCancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>



<!--"-->

    <el-dialog :title="dialogWln.title" @close="handleDialogClose" v-model="dialogWln.visible" width="700px" style="height: 650px" append-to-body>
      <div v-show="showSearch" style="width: 600px">
        <el-form :inline="true">
          <el-form-item label="店铺名称" prop="shopGroup">
            <el-input v-model="shopName" placeholder="" clearable @keyup.enter="handleQuery" />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="Search" @click="wlnHandleQuery">搜索</el-button>
          </el-form-item>
        </el-form>
      </div>
      <el-table
        :data="wlnShopList"
        style="width: 605px;"
        height="450px"
      >
        <el-table-column prop="shopName" label="店铺名称"/>
        <el-table-column prop="shopNick" label="系统ID" />
        <el-table-column label="授权" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="授权" placement="top">
              <el-button link type="primary" icon="Pointer" @click="wlnAuthorize(scope.row)"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <div class="dialog-footer" style="display: flex; justify-content: space-between; align-items: center; margin-top: 15px">
        <div class="pagination-buttons">
          <el-button :disabled="page <= 1" @click="prevPage">上一页</el-button>
          <el-button @click="nextPage" :disabled="!wlnShopList?.length || wlnShopList.length < limit">下一页</el-button>
        </div>
<!--         -->
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>


<script setup name="Shop" lang="ts">
import { globalHeaders } from '@/utils/request';
import { CascaderProps, UploadProps, UploadUserFile } from 'element-plus';
import {
  listShop,
  getShop,
  delShop,
  addShop,
  updateShop,
  toPddGetCode,
  getLogisticsTemplate,
  toKongfzGetCode,
  synchronizationGoods, artNoRuleSave, getShe, getFre, getWlnShopList, authorizationWln
} from '@/api/zhishu/shop';
import { updateIsSynOrder } from '@/api/zhishu/shopOrder';
import { listShopDetail, getShopDetail, delShopDetail, addShopDetail, updateShopDetail } from '@/api/zhishu/shopDetail';
import { listPriceTemplate, getPriceTemplate, delPriceTemplate, addPriceTemplate, updatePriceTemplate } from '@/api/zhishu/priceTemplate';
import { listSpec, getSpec, getSpecByShopId, delSpec, addSpec, updateSpec } from '@/api/zhishu/spec';
import { addShopImg, updateShopImg, listShopImgByPid, delShopImg } from '@/api/zhishu/shopImg';

import { ShopVO, ShopQuery, ShopForm, saveArtNo, WlnShopList } from '@/api/zhishu/shop/types';
import { ShopDetailVO, ShopDetailQuery, ShopDetailForm } from '@/api/zhishu/shopDetail/types';
import { ShopImgVO } from '@/api/zhishu/shopImg/types';
import { PriceTemplateVO, PriceTemplateQuery, PriceTemplateForm } from '@/api/zhishu/priceTemplate/types';
import { SpecVO, SpecQuery, SpecForm } from '@/api/zhishu/spec/types';
import { getSpecs} from '@/api/zhishu/pdd';

import { getTemplateSimpleList } from '@/api/zhishu/kongfz';
import { getCitiesByProvinceId, getDistrictsByCityId, getDistrictTree, getProvinces } from '@/api/zhishu/district';

import draggable from 'vuedraggable';
import { ref } from 'vue';
import { DepotVO } from '@/api/zhishu/depot/types';
import { depotNameList } from '@/api/zhishu/shelves';
import { TDistrictVo } from '@/api/zhishu/district/types';
import { getImage } from '@/api/zhishu/image';


const  artDialog=reactive<DialogOption>({
  visible: false,
  title: '',
  isAdd: true
});

// 定义方法
const onEnd = (event) => {
  console.log('拖拽结束:', event);
};


const activeName = ref('first');
const radio1 = ref('')

const handleClick = (event) => {};

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const {
  t_shop_type,
  t_shop_authorize,
  sys_normal_disable,
  t_yes_no,
  t_space_character,
  t_open_or_close,
  t_title_consist_of,
  t_shop_set_up,
  t_presale,
  t_delivery_time,
  t_watermark_position,
  t_ggzc,
  t_book_template
} = toRefs<any>(
  proxy?.useDict(
    't_shop_type',
    't_shop_authorize',
    'sys_normal_disable',
    't_yes_no',
    't_space_character',
    't_open_or_close',
    't_title_consist_of',
    't_shop_set_up',
    't_presale',
    't_delivery_time',
    't_watermark_position',
    't_ggzc',
    't_book_template'
  )
);

const shopList = ref<ShopVO[]>([]);
const shopDetailList = ref<ShopDetailVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const loadingAoth = ref(false);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const shopFormRef = ref<ElFormInstance>();
const shopDetailFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

/**
 * 多图片上传
 */
const fileList1 = ref<UploadUserFile[]>([]);
const fileList2 = ref<UploadUserFile[]>([]);
const fileList3 = ref<UploadUserFile[]>([]);
const fileList4 = ref<UploadUserFile[]>([]);
const fileList5 = ref<UploadUserFile[]>([]);

const logisticsTemplateList = [
  {
    value: '',
    label: '无数据',
    feeManner:''
  }
];

const priceTemplateList = [
  {
    value: '',
    label: '无数据'
  }
];

const initFormData: ShopForm = {
  id: undefined,
  shopType: undefined,
  shopGroup: undefined,
  shopName: undefined,
  shopAuthorize: undefined,
  expirationTime: undefined,
  shopKey: undefined,
  token: undefined,
  status: '0',
  account : undefined,
  password : undefined
};

const formGg = ref({
  id: undefined,
  shopId: undefined,
  specTypeId: undefined,
  specTypeName: undefined,
  specCompose: '0',
  specName: undefined,
  specPrefix: undefined,
  specSuffix: undefined,
  specCodeCompose: '0',
  specCodePrefix: undefined,
  specCodeSuffix: undefined,
  specSyUrl: undefined,
  file: undefined
});

const initSpecData = {
  id: undefined,
  shopId: undefined,
  specTypeId: undefined,
  specTypeName: undefined,
  specCompose: '0',
  specName: undefined,
  specPrefix: undefined,
  specSuffix: undefined,
  specCodeCompose: '0',
  specCodePrefix: undefined,
  specCodeSuffix: undefined,
  specSyUrl: undefined,
  file:undefined
};

const initFormDataDetail: ShopDetailForm = {
  id: undefined,
  shopId: undefined,
  templateId: undefined,
  highPrice: 0,
  lowPrice: 0,
  saleTemplateId: undefined,
  sevenDays: '0',
  isSecondHand: '0',
  carouselNum: '1',
  isDetailsImg: '0',
  isCatalogueDetails: '0',
  isInformationImg: '0',
  titlePrefix: undefined,
  titleSuffix: undefined,
  titleFilter: undefined,
  itemNumberPrefix: undefined,
  titleConsistOf: undefined,
  spaceCharacter: '0',
  autoTruncation: '1',
  mySetUp: undefined,
  systemSetUp: undefined,
  filterSuit: '0',
  isDelProtect: '0',
  isRemoveProtect: '0',
  bootPrice: '0',
  ownPictures: '0',
  stockDeff: 0,
  twoDiscount: undefined,
  presale: '0',
  fake: '0',
  deliveryTime: '0',
  whitePicture: '0',
  watermarkPosition: '1',
  filtering: '0',
  lowerPrice: '0',
  lowerPriceDiscount: undefined,
  spellTemplateId: undefined,
  districtId:undefined,
  conditionDef:undefined,
  conditionDes:undefined,
  recommend:undefined,
  isParcel:"1",
  bookTemplate:'17',
  bookWeight:0,
  standardNumber:0,
  shopType:undefined,
  autoAdd:'0',
};

const data = reactive<PageData<ShopForm, ShopQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    shopType: undefined,
    shopGroup: undefined,
    shopName: undefined,
    shopAuthorize: undefined,
    expirationTime: undefined,
    shopKey: undefined,
    token: undefined,
    status: undefined,
    account : undefined,
    password : undefined,
    skuSpec : undefined,
    params: {}
  },
  rules: {
    id: [{ required: true, message: '不能为空', trigger: 'blur' }],
    shopType: [{ required: true, message: '店铺类型不能为空', trigger: 'blur' }],
    shopName: [{ required: true, message: '店铺名称不能为空', trigger: 'blur' }],
    status: [{ required: true, message: '状态不能为空', trigger: 'blur' }]
  }
});

const dataDetail = reactive<PageData<ShopDetailForm, ShopDetailQuery>>({
  formDetail: { ...initFormDataDetail },
  queryParamsDetail: {
    pageNum: 1,
    pageSize: 10,
    shopId: undefined,
    templateId: undefined,
    highPrice: undefined,
    lowPrice: undefined,
    saleTemplateId: undefined,
    sevenDays: undefined,
    isSecondHand: undefined,
    carouselNum: undefined,
    isDetailsImg: undefined,
    isCatalogueDetails: undefined,
    isInformationImg: undefined,
    titlePrefix: undefined,
    titleSuffix: undefined,
    titleFilter: undefined,
    itemNumberPrefix: undefined,
    titleConsistOf: undefined,
    spaceCharacter: undefined,
    autoTruncation: undefined,
    mySetUp: undefined,
    systemSetUp: undefined,
    filterSuit: undefined,
    isDelProtect: undefined,
    isRemoveProtect: undefined,
    bootPrice: undefined,
    ownPictures: undefined,
    stockDeff: undefined,
    twoDiscount: undefined,
    presale: undefined,
    fake: undefined,
    deliveryTime: undefined,
    whitePicture: undefined,
    watermarkPosition: undefined,
    filtering: undefined,
    lowerPrice: undefined,
    lowerPriceDiscount: undefined,
    spellTemplateId: undefined,
    districtId:undefined,
    hasSyncFile:undefined,
    conditionDef:undefined,
    conditionDes:undefined,
    recommend:undefined,
    isParcel:'1',
    bookTemplate:undefined,
    bookWeight:undefined,
    standardNumber:undefined,
    shopType:undefined,
    autoAdd:undefined,
    params: {}
  },
  rulesDetail: {
    id: [{ required: true, message: '主键不能为空', trigger: 'blur' }],
    stockDeff: [{ required: true, message: '默认库存不能为空', trigger: 'blur' }],
    twoDiscount: [{ required: true, message: '两件折扣不能为空', trigger: 'blur' }],
    templateId: [{ required: true, message: '物流模板不能为空', trigger: 'blur' }],
    // highPrice: [{ required: true, message: '价格区间不能为空', trigger: 'blur' }],
    saleTemplateId: [{ required: true, message: '销售价格不能为空', trigger: 'blur' }],
    sevenDays: [{ required: true, message: '请选择是否开启7天无理由', trigger: 'blur' }],
    isSecondHand: [{ required: true, message: '请选择是否二手', trigger: 'blur' }],
    presale: [{ required: true, message: '请选择是否预售', trigger: 'blur' }],
    fake: [{ required: true, message: '请选择是否假一赔十', trigger: 'blur' }],
    deliveryTime: [{ required: true, message: '请选择发货时间', trigger: 'blur' }]
  }
});

const { queryParams, form, rules } = toRefs(data);
const { queryParamsDetail, formDetail, rulesDetail } = toRefs(dataDetail);





const importRule=async (row?: ShopVO)=>{
  ElMessage({
    message: '正在拉取中.....',
    grouping: true,
    type: 'success',
  })
  const shop=row.id;
  const shopType=row.shopType;
  synchronizationGoods(shop,Number(shopType));
  setTimeout(() => {
    getList()
  }, 5000);

}

/** 处理订单状态变更 */
const handleOrderStatusChange = async (row: any, val: boolean) => {
  try {
    await updateIsSynOrder({
      shopId: row.id,
      isSynOrder: val ? 1 : 0
    });
    proxy?.$modal.msgSuccess('订单状态更新成功');
    getList(); // 刷新列表
  } catch (error) {
    proxy?.$modal.msgError('订单状态更新失败');
    getList(); // 刷新列表以恢复原状态
  }
};

const typeMapping = {
  0: 'danger',
  1: 'primary',
  2: 'danger'
};


/** 查询店铺信息列表 */
const getList = async () => {
  loading.value = true;
  const res = await listShop(queryParams.value);
  shopList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

/** 取消按钮 */
const cancel = () => {
  reset();
  resetDetail();
  dialog.visible = false;
  dialog.shopSetup = false;
  artDialog.visible=false;
  dialogWln.visible=false;
};
let showGuigePopup = ref(false);
let chooseIndex = ref('');

const chooseItem = (parentSpecId, parentSpecName) => {
  chooseIndex.value = Number(parentSpecId);
  formGg.value.specTypeId = Number(parentSpecId);
  formGg.value.specTypeName = parentSpecName;
};

const getZdySpac = async (shopId) => {
  const res = await getSpecByShopId(shopId);
  zdyGg.value = res.data.specName;
  skuImageUrl.value = await getImage(res.data.specSyUrl);
  Object.assign(formGg.value, res.data);
  chooseItem(res.data.specTypeId,res.data.specTypeName);
}

const setZdySpac = async () => {
  getZdySpac(formDetail.value.shopId);
  showGuigePopup.value = true;
};

const ggCancel = () => {
  showGuigePopup.value = false;
  chooseIndex.value = '';
  formGg.value = { ...initSpecData };
};

/**
 * 自定义规格提交
 */
const ggSetSubmit = async () => {
  // showGuigePopup.value = false;
  formGg.value.shopId = formDetail.value.shopId;

  if (formGg.value.id) {
    await updateSpec(formGg.value).finally(() => (buttonLoading.value = false));
  } else {
    await addSpec(formGg.value).finally(() => (buttonLoading.value = false));
  }

  showGuigePopup.value = false;

  zdyGg.value = formGg.value.specName;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  shopFormRef.value?.resetFields();
};

const resetDetail = () => {
  formDetail.value = { ...initFormDataDetail };
  shopDetailFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: ShopVO[]) => {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

const addOrEditMark = ref(false);
/** 新增按钮操作 */
const handleAdd = () => {
  addOrEditMark.value = true;
  reset();
  dialog.visible = true;
  dialog.title = '添加店铺信息';
};

/** 修改按钮操作 */
const handleUpdate = async (row?: ShopVO) => {
  addOrEditMark.value = false;
  reset();
  const _id = row?.id || ids.value[0];
  const res = await getShop(_id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = '修改店铺信息';
};

//规格列表
let specList;
//规格名称
const zdyGg = ref('');
/** 店铺设置操作 */
const shopSetUp = async (row?: ShopVO) => {
  console.log(row,"--------------------------")
  if(!row?.token){
    proxy?.$modal.msgError('请先授权店铺');
    return false;
  }
  if(dataBj(row.expirationTime).value){
    proxy?.$modal.msgError('授权已过期,请重新授权');
    return false;
  }
  reset();
  resetDetail();

  const _id = row?.id || ids.value[0];
  const res = await getShopDetail(_id);
  Object.assign(formDetail.value, res.data);

  formDetail.value.shopType = row.shopType;
  if (formDetail.value.titleConsistOf != null && formDetail.value.titleConsistOf != undefined) {
    formDetail.value.titleConsistOf = formDetail.value.titleConsistOf.split(',');
  } else {
    formDetail.value.titleConsistOf = [];
  }
  if (formDetail.value.mySetUp != null && formDetail.value.mySetUp != undefined) {
    formDetail.value.mySetUp = formDetail.value.mySetUp.split(',');
  } else {
    formDetail.value.mySetUp = [];
  }
  if (formDetail.value.systemSetUp != null && formDetail.value.systemSetUp != undefined) {
    formDetail.value.systemSetUp = formDetail.value.systemSetUp.split(',');
  } else {
    formDetail.value.systemSetUp = [];
  }
  // //价格处理
  // formDetail.value.highPrice = formDetail.value.highPrice / 100;
  // formDetail.value.lowPrice = formDetail.value.lowPrice /100;
  //孔夫子 商品重量，本数处理
  if(formDetail.value.bookWeight != null && formDetail.value.bookWeight != undefined){
    formDetail.value.bookWeight = formDetail.value.bookWeight / 100
  }
  if(formDetail.value.standardNumber != null && formDetail.value.standardNumber != undefined){
    formDetail.value.standardNumber = formDetail.value.standardNumber / 100
  }

  dialog.shopSetup = true;
  dialog.title = '店铺设置';
  formDetail.value.shopId = _id;


  imageUrl.value = '';
  imageUrl2.value = '';
  fileList1.value.length = 0;
  fileList2.value.length = 0;
  fileList3.value.length = 0;
  fileList4.value.length = 0;
  fileList5.value.length = 0;
  skuImageUrl.value = '';

  /**
   * 常驻地区
   */
  let pid = '';
  for(var i=0;i<districtList.length;i++){
    if(districtList[i].value == formDetail.value.districtId){
        break;
      }
    const children = districtList[i].children;
    for(var j=0;j<children.length;j++){
      if(children[j].value == formDetail.value.districtId){
        pid = districtList[i].value;
        break;
      }
    }
    if(pid != ''){
      break;
    }
  }
  if(pid != ''){
    formDetail.value.districtId = [pid,Number(formDetail.value.districtId)];
  }else{
    formDetail.value.districtId = [Number(formDetail.value.districtId)];
  }

  /**
   * 获取店铺图片
   */
  const imgList = await listShopImgByPid(_id);
  const data = imgList.data;

  //商详头图临时数组
  for (var i = 0; i < data.length; i++) {
    if (data[i].type == '1') {
      fileList1.value.push({
        id: data[i].id,
        url: data[i].absolutePath
      });
    } else if (data[i].type == '2') {
      fileList2.value.push({
        id: data[i].id,
        url: data[i].absolutePath
      });
    } else if (data[i].type == '3') {
      fileList3.value.push({
        id: data[i].id,
        url: data[i].absolutePath
      });
    } else if (data[i].type == '4') {
      fileList4.value.push({
        id: data[i].id,
        url: data[i].absolutePath
      });
    } else if (data[i].type == '5') {
      fileList5.value.push({
        id: data[i].id,
        url: data[i].absolutePath
      });
    }
  }
  //运费模板
  chooseTemplate(row);
  //生成标题组成
  getTitleConsistOfArray(res.data.titleConsistOf);
  if(row.shopType == '1'){
    //拼多多规格类型列表
    specList = await getSpecs(_id,row.mallId);
    //拼多多查询自定义规格
    getZdySpac(_id);

  }else if(row.shopType == '2'){
    //孔夫子
  }

};

/**
 * 获取标题组成
 */
// 定义响应式数据
const titleConsistOfArray = ref([]);
const getTitleConsistOfArray = (titleConsistOf) => {
    titleConsistOfArray.value.length = 0;
    const array = t_title_consist_of._object.t_title_consist_of;
    const tArr = titleConsistOf.split(",");

    for(var i=0;i<tArr.length;i++){
      const tkeyVal = tArr[i].split(":");
      const name = ref('');
      for(var j=0;j<array.length;j++){
        if(tkeyVal[0] == array[j].value){
          name.value = array[j].label;
          break;
        }
      }
      if(tkeyVal[1] == 'false'){
        tkeyVal[1] = '';
      }
      titleConsistOfArray.value.push({
        id:tkeyVal[0],
        name:name.value,
        checked:Boolean(tkeyVal[1])
      })
    }
}

/** 根据店铺类型获取不同的运费模板 */
const chooseTemplate = (row) => {
  const shopType = row.shopType;
  logisticsTemplateList.length = 0;
  if(shopType == '1'){
    initLogisticsTemplate(row.mallId);
  }else if(shopType == '2'){
    initKongfzTemplate();
  }
}

/** 拼多多初始化运费模板信息 */
const initLogisticsTemplate = async (mallId) => {
  var list = await getLogisticsTemplate(formDetail.value.shopId,mallId);
  if (list.length == 0) {
    logisticsTemplateList.push({
      value: '',
      label: '未获取到模板信息',
      feeManner:''
    });
  } else {
    for (var i = 0; i < list.length; i++) {
      logisticsTemplateList.push({
        value: list[i].template_id,
        label: list[i].template_name,
        feeManner:''
      });
    }
  }
};

/** 孔夫子初始化运费模板信息 */
const initKongfzTemplate = async () => {
  var list = await getTemplateSimpleList(formDetail.value.shopId)
  if (list.length == 0) {
    logisticsTemplateList.push({
      value: '',
      label: '未获取到模板信息',
      feeManner:list[i].feeManner
    });
  } else {
    for (var i = 0; i < list.length; i++) {
      logisticsTemplateList.push({
        value: list[i].mouldId,
        label: list[i].mouldName,
        feeManner:list[i].feeManner
      });
    }
  }
}

/** 初始化价格模板信息 */
const initPriceTemplate = async () => {
  const list = await listPriceTemplate();
  priceTemplateList.length = 0;
  if (list.rows.length == 0) {
    priceTemplateList.push({
      value: '',
      label: '未获取到模板信息'
    });
  } else {
    for (var i = 0; i < list.rows.length; i++) {
      priceTemplateList.push({
        value: list.rows[i].id,
        label: list.rows[i].templateName
      });
    }
  }
};

/** 提交按钮 */
const submitForm = () => {
  if(form.value.shopType == '1' && addOrEditMark.value == true){
    //拼多多登录
    pddAddCode();

  }
  if(form.value.shopType == '2' && form.value.account == undefined){
    proxy?.$modal.msgError('请输入登录账号');
    return;
  }
  if(form.value.shopType == '2' && form.value.password == undefined){
    proxy?.$modal.msgError('请输入账号密码');
    return;
  }
  shopFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateShop(form.value).finally(() => (buttonLoading.value = false));
      } else {
        await addShop(form.value).finally(() => (buttonLoading.value = false));
      }
      proxy?.$modal.msgSuccess('操作成功');
      dialog.visible = false;
      await getList();
    }
  });
};

/** 设置提交按钮 */
const submitFormDetail = () => {
  //获取店铺类型
  var shopType = formDetail.value.shopType;
  //校验物流模板
  if(!formDetail.value.templateId){
    proxy?.$modal.msgError('物流模板不能为空');
    return;
  }

  //拼多多校验
  if(shopType == '1'){
    //校验默认库存
    if(!formDetail.value.stockDeff && formDetail.value.stockDeff != 0){
      proxy?.$modal.msgError('默认库存不能为空');
      return;
    }
    //校验两件折扣
    if(!formDetail.value.twoDiscount){
      proxy?.$modal.msgError('两件折扣不能为空');
      return;
    }
  }else if(shopType == '2'){
    //孔夫子校验

    for(var i=0;i<logisticsTemplateList.length;i++){
      if(formDetail.value.templateId == logisticsTemplateList[i].value){
        if(logisticsTemplateList[i].feeManner == 'weight'
        && (!formDetail.value.bookWeight || parseFloat(formDetail.value.bookWeight) == 0)){
          proxy?.$modal.msgError('根据选择的运费模板要求，请填写"商品重量"');
          return;
        }else if(logisticsTemplateList[i].feeManner == 'weightPiece'
        && (!formDetail.value.standardNumber || parseFloat(formDetail.value.standardNumber) == 0)){
          proxy?.$modal.msgError('根据选择的运费模板要求，请填写商品"标准本数"');
          return;
        }
        break;
      }
    }

    if(!formDetail.value.conditionDef){
      proxy?.$modal.msgError('请填写默认品相');
      return;
    }
    if(!formDetail.value.isParcel){
      formDetail.value.isParcel = '0'
    }
    if(!formDetail.value.bookTemplate){
      formDetail.value.bookTemplate = '17'
    }
  }

  // //校验价格区间
  // if(!formDetail.value.highPrice || !formDetail.value.lowerPrice){
  //   proxy?.$modal.msgError('价格区间不能为空');
  //   return;
  // }
  //校验销售价格模板
  if(!formDetail.value.saleTemplateId){
    proxy?.$modal.msgError('请选择销售价格模板');
    return;
  }

  formDetail.value.districtId = formDetail.value.districtId[formDetail.value.districtId.length-1];
  //校验标题
  const bool = ref(false);
  for(var i=0;i<titleConsistOfArray.value.length;i++){
    if(titleConsistOfArray.value[i].checked == true){
      bool.value = true;
      break;
    }
  }
  if(bool.value == false){
    proxy?.$modal.msgError('请选择标题设置→标题组成');
    return;
  }

  shopDetailFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;

      formDetail.value.mySetUp = formDetail.value.mySetUp.join(',');
      formDetail.value.systemSetUp = formDetail.value.systemSetUp.join(',');
      //标题组成
      let titleConsistOf = '';
      for(var i=0;i<titleConsistOfArray.value.length;i++){
        if(titleConsistOf == ''){
          titleConsistOf = titleConsistOfArray.value[i].id+":"+titleConsistOfArray.value[i].checked;
        }else{
          titleConsistOf = titleConsistOf + ',' + titleConsistOfArray.value[i].id+":"+titleConsistOfArray.value[i].checked;
        }
      }
      formDetail.value.titleConsistOf = titleConsistOf;
      // formDetail.value.highPrice = formDetail.value.highPrice * 100;
      // formDetail.value.lowPrice = formDetail.value.lowPrice * 100;

      formDetail.value.bookWeight = formDetail.value.bookWeight * 100;
      formDetail.value.standardNumber = formDetail.value.standardNumber * 100;

      if (formDetail.value.id) {
        await updateShopDetail(formDetail.value).finally(() => (buttonLoading.value = false));
      } else {
        await addShopDetail(formDetail.value).finally(() => (buttonLoading.value = false));
      }
      proxy?.$modal.msgSuccess('操作成功');
      dialog.shopSetup = false;

      await getList();
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row?: ShopVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除店铺信息编号为"' + _ids + '"的数据项？').finally(() => (loading.value = false));
  await delShop(_ids);
  proxy?.$modal.msgSuccess('删除成功');
  await getList();
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    'zhishu/shop/export',
    {
      ...queryParams.value
    },
    `shop_${new Date().getTime()}.xlsx`
  );
};


const dialogWln = reactive<DialogOption>({
  visible: false,
  title: ''
});

const  shopIdNum=ref();
const  shopName=ref();
const  page=ref(1);
const  limit=ref(10);
// 正确定义为响应式数组
let wlnShopList = ref<WlnShopList[]>([])

//授权
const toCode = async (row?: ShopVO) => {
  console.log(row)

  if(row?.shopType==="3"){
    dialogWln.title="店铺信息展示";
    dialogWln.visible=true;
    shopIdNum.value = row?.id || ids.value[0];
    const res=await getWlnShopList(page.value,limit.value,shopIdNum.value,shopName.value)
    wlnShopList.value=res.data;
  }else{
    loadingAoth.value = true;
    reset();
    resetDetail();
    const _id = row?.id || ids.value[0];
    let url;
    if (row.shopType === '1') {
      url = await toPddGetCode(_id);
      loadingAoth.value = false;
      window.open(url, '_blank');
    } else if (row.shopType === '2') {
      url = await toKongfzGetCode(_id);
      loadingAoth.value = false;
      window.open(url, '_blank');
    }
  }
};

const pddAddCode = async () => {
  let url = await toPddGetCode('');
  loadingAoth.value = false;
  window.open(url, '_blank');
}


//新增功能部分
//上一页
const prevPage=async ()=>{
  page.value-=1;
  const res=await getWlnShopList(page.value,limit.value,shopIdNum.value,shopName.value)
  wlnShopList=res.data;
}
// 下一页
const nextPage=async ()=>{
  page.value+=1;
  const res=await getWlnShopList(page.value,limit.value,shopIdNum.value,shopName.value)
  wlnShopList=res.data;
}
//添加搜索条件
const wlnHandleQuery=async ()=>{
  page.value=1;
  const res=await getWlnShopList(page.value,limit.value,shopIdNum.value,shopName.value)
  wlnShopList=res.data;
}
//万里牛授权
const wlnAuthorize=async (row?: WlnShopList)=>{
  const res=await authorizationWln(shopIdNum.value,row.shopName,row.shopNick);
  if(res.data){
    proxy?.$modal.msgSuccess('操作成功');
  }else{
    proxy?.$modal.msgError('操作失败');
  }
  dialogWln.visible = false;
  await getList();
}
const handleDialogClose=async ()=>{
  shopIdNum.value=null;
  shopName.value=null;
  page.value=1;
  dialogWln.visible = false; // 明确设置为false
}





//图片上传数量提示
// const handleExceedOne = () =>{
//   proxy?.$modal.msgError('只能上传1张图片');
// }
const handleExceedThree = () => {
  proxy?.$modal.msgError('只能上传3张图片');
};

const baseUrl = import.meta.env.VITE_APP_BASE_API;
//店铺图片上传
const uploadImgUrl = ref(baseUrl + '/zhishu/image/shopDetailUpload'); // 上传的图片服务器地址
//规格图片上传
const SkuUploadImgUrl = ref(baseUrl + '/zhishu/image/specUpload'); // 上传的图片服务器地址

const headers = ref(globalHeaders());

const imageUrl = ref('');
const imageUrl2 = ref('');
const imageType = ref('');
const skuImageUrl = ref('');

const handleAvatarSuccess: UploadProps['onSuccess'] = async (response, uploadFile) => {
  var type = imageType.value;
  var relativePath = '';
  if (type == '1') {
    imageUrl.value = URL.createObjectURL(uploadFile.raw!);
    relativePath = imageUrl.value;
  } else if (type == '2') {
    imageUrl2.value = URL.createObjectURL(uploadFile.raw!);
    relativePath = imageUrl2.value;
  }
  const initDataShopImg: ShopImgVO = {
    id: undefined,
    pid: formDetail.value.shopId,
    relativePath: relativePath,
    absolutePath: response.data.url,
    type: imageType.value,
    imgOrder: undefined,
    status: undefined
  };
  await addShopImg(initDataShopImg);
};

const skuHandleAvatarSuccess : UploadProps['onSuccess'] = async (response, uploadFile) => {
  formGg.value.specSyUrl = response.data.url;
  skuImageUrl.value = response.data.url
}

/**
 * 获取上传图片类型
 * 1封面水印  2商详水印 3 商详头图  4 商详尾图 5 轮播尾图
 */
const changeType = (type) => {
  imageType.value = type;
};

const beforeAvatarUpload: UploadProps['beforeUpload'] = (rawFile) => {
  return new Promise((resolve, reject) => {
    // 检查文件类型
    if (rawFile.type !== 'image/jpeg' && rawFile.type !== 'image/png') {
      ElMessage.error('图片必须是 JPG/PNG 格式！');
      return reject(false); // 阻止上传
    }

    // 检查文件大小
    if (rawFile.size / 1024 > 200) {
      ElMessage.error('图片大小不能超过200KB！');
      return reject(false); // 阻止上传
    }

    // 检查图片尺寸（异步）
    const reader = new FileReader();
    reader.onload = (event) => {
      const img = new Image();
      img.src = event.target?.result as string;

      img.onload = () => {
        if (img.width !== 800 || img.height !== 800) {
          ElMessage.error('水印图片必须是800*800');
          reject(false); // 阻止上传
        } else {
          resolve(true); // 允许上传
        }
      };

      img.onerror = () => {
        reject(false); // 图片加载失败，阻止上传
      };
    };

    reader.onerror = () => {
      reject(false); // 文件读取失败，阻止上传
    };

    reader.readAsDataURL(rawFile);
  });
};

//日期比较
const dataBj = (time) => {
    const targetTime = ref(time);
    const currentTime = ref('');
    const isTargetTimeEarlier = ref(false);

    // 获取当前时间并格式化
    const getCurrentTime = () => {
      const now = new Date();
      const year = now.getFullYear();
      const month = String(now.getMonth() + 1).padStart(2, '0');
      const day = String(now.getDate()).padStart(2, '0');
      const hours = String(now.getHours()).padStart(2, '0');
      const minutes = String(now.getMinutes()).padStart(2, '0');
      const seconds = String(now.getSeconds()).padStart(2, '0');
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    };

    // 比较目标时间和当前时间
    const compareTimes = () => {
      const targetDate = new Date(targetTime.value);
      const nowDate = new Date();
      isTargetTimeEarlier.value = targetDate < nowDate; // 如果目标时间早于当前时间，返回 true
    };

    currentTime.value = getCurrentTime();
    compareTimes();

    return isTargetTimeEarlier;
}


const dialogImageUrl = ref('');
const dialogVisible = ref(false);

const handleRemove: UploadProps['onRemove'] = async (uploadFile, uploadFiles) => {
  if(uploadFile.id! != undefined){
      await delShopImg(uploadFile.id!);
  }
};

const handlePictureCardPreview: UploadProps['onPreview'] = (uploadFile) => {
  dialogImageUrl.value = uploadFile.url!;
  dialogVisible.value = true;
};

let districtList = [];
/**
 * 获取省市
 */
const getDistrict = async () =>{
  districtList =  await getDistrictTree();
};

//事件总线
const eventBus = useEventBus<string>('sys-notification');


// 事件监听（自动销毁）
const unsubscribe = eventBus.on((event) => {
  if (event === 'refresh-shop') {
    getList();
  }
});


onMounted(() => {
  getList();
  initPriceTemplate();
   //获取省市
  getDistrict();
});
</script>

<style scoped lang="scss">
.remark {
  padding: 0 0 20px 100px;
}

.demo-tabs > .el-tabs__content {
  padding: 32px;
  color: #6b778c;
  font-size: 32px;
  font-weight: 600;
}

.el-upload-list__item-thumbnail {
  object-fit: fill !important;
}
</style>

<style scoped>
.avatar-uploader .avatar {
  width: 178px;
  height: 178px;
  display: block;
}

.ggTypeBox {
  flex-wrap: wrap;
  justify-content: space-between;
}

.activeClass {
  color: #000 !important;
  background: rgba(64, 158, 255, 0.5) !important;
}

.typeItem {
  min-width: 50px;
  padding: 2px 4px;
  border-radius: 10px;
  border: 1px solid #c9c8c8;
  background: #f0efef;
  color: #2a2a2a;
  font-size: 13px;
  float: left;
  margin-bottom: 10px;
  margin-left: 5px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.isHide {
  display: none;
}

img{
  width: 80px !important;
  height: 80px !important;
}

 .isHide{
   display: none;
 }

</style>
