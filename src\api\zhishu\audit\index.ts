import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { AuditVO, AuditForm, AuditQuery, AuditParams, SendFailed } from '@/api/zhishu/audit/types';

/**
 * 查询审核列表
 * @param query
 * @returns {*}
 */

export const listAudit = (query?: AuditQuery): AxiosPromise<AuditVO[]> => {
  return request({
    url: '/zhishu/audit/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询审核详细
 * @param id
 */
export const getAudit = (id: string | number): AxiosPromise<AuditVO> => {
  return request({
    url: '/zhishu/audit/' + id,
    method: 'get'
  });
};

/**
 * 新增审核
 * @param data
 */
export const addAudit = (data: AuditForm) => {
  return request({
    url: '/zhishu/audit',
    method: 'post',
    data: data
  });
};

/**
 * 修改审核
 * @param data
 */
export const updateAudit = (data: AuditForm) => {
  return request({
    url: '/zhishu/audit',
    method: 'put',
    data: data
  });
};

/**
 * 删除审核
 * @param id
 */
export const delAudit = (id: string | number | Array<string | number>) => {
  return request({
    url: '/zhishu/audit/' + id,
    method: 'delete'
  });
};

export const  UpdateStatus=(data:AuditParams)=>{
  return request({
    url: '/zhishu/audit/updateStatus',
    method: 'put',
    data: data
  });
};

export const SendFailedInFo=(data:SendFailed)=>{
  return request({
    url: '/zhishu/audit/failSend',
    method: 'post',
    data: data
  });
}
