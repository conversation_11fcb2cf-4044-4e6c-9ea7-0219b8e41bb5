import {ref, reactive} from 'vue'

export default function () {

  let table_column: any = reactive([
    {
      label: '书名',
      align: 'center',
      prop: 'bookName',
      width: '200',
    },
    {
      label: '图片',
      align: 'center',
      prop: 'bookPic',
    },    {
      label: 'isbn',
      align: 'center',
      prop: 'isbn',
    },    {
      label: 'selection',
      align: 'center',
      prop: 'bookName',
    },    {
      label: 'selection',
      align: 'center',
      prop: 'bookName',
    },    {
      label: 'selection',
      align: 'center',
      prop: 'bookName',
    },    {
      label: 'selection',
      align: 'center',
      prop: 'bookName',
    },    {
      label: 'selection',
      align: 'center',
      prop: 'bookName',
    },    {
      label: 'selection',
      align: 'center',
      prop: 'bookName',
    },    {
      label: 'selection',
      align: 'center',
      prop: 'bookName',
    },


  ])

}
