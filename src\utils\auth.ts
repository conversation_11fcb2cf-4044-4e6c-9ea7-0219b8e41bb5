const TokenKey = 'Admin-Token';

const tokenStorage = useStorage<null | string>(TokenKey, null);

export function getToken() {
  const token = localStorage.getItem('token');
  console.log('Getting token:', token); // 添加日志
  return token;
}

export function setToken(token: string) {
  console.log('Setting token:', token); // 添加日志
  localStorage.setItem('token', token);
}

export function removeToken() {
  console.log('Removing token'); // 添加日志
  localStorage.removeItem('token');
}

// export function getToken() {
//   return localStorage.getItem('token');
// }
// export const getToken = () => tokenStorage.value;
// export const setToken = (access_token: string) => (tokenStorage.value = access_token);
// export const removeToken = () => (tokenStorage.value = null);
