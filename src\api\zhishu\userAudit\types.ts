export interface UserAuditVO {
  /**
   * ID
   */
  id: string | number;

  /**
   * 用户ID
   */
  userId: string | number;

  /**
   * 管理员ID
   */
  adminId: string | number;

  /**
   * 审核状态（0通过 1未通过 2待审核)
   */
  status: string;

  /**
   * 企业名称
   */
  companyName : string;

  /**
   * 企业类型
   */
  companyType : string;

  /**
   * 联系人名
   */
  contactPerson : string;

  /**
   * 联系方式
   */
  contactPhone : string;

  /**
   * 邮箱
   */
  email : string;
  /**
   * 身份证
   */
  cardIdentity:string,

  /**
   * 营业执照名称
   */

  licenseName:string,


  /**
   * 营业执照注册号
   */
  licenseNumber:string,


  /**
   * 营业执照
   */
  license : string;

  /**
   * 备注
   */
  remark : string;

  /**
   * 用户昵称
   */
  userName : string;
  /**
   * 地址
   */
  adress:string;

  /**
   * 经营许可证
   */
  businessLicense:string;

  /**
   * 营业执照过期时间
   */
  licenseTime:string;


  /**
   * 经营许可证过期时间
   */
  businessLicenseTime:string;

}

export interface UserAuditForm extends BaseEntity {
  /**
   * 用户ID
   */
  userId?: string | number;

  /**
   * 用户昵称
   */
  userName?: string;

  /**
   * 管理员ID
   */
  adminId?: string | number;

  /**
   * 审核状态（0通过 1未通过 2待审核)
   */
  status?: string;

  /**
   * 企业名称
   */
  companyName?: string;

  /**
   * 企业类型
   */
  companyType?: string;

  /**
   * 联系人名
   */
  contactPerson?: string;

  /**
   * 联系方式
   */
  contactPhone?: string;

  /**
   * 邮箱
   */
  email?: string;
  /**
   * 身份证
   */
  cardIdentity?:string,

  /**
   * 营业执照名称
   */

  licenseName?:string,


  /**
   * 营业执照注册号
   */
  licenseNumber?:string,

  /**
   * 地址
   */
  adress?:string,

  /**
   * 经营许可证
   */
  businessLicense?:string,

  /**
   * 营业执照过期时间
   */
  licenseTime?:string,


  /**
   * 经营许可证过期时间
   */
  businessLicenseTime?:string,


  /**
   * 营业执照
   */
  license?: string;

  /**
   * 备注
   */
  remark?: string;

}

export interface UserAuditQuery extends PageQuery {

  /**
   * 用户ID
   */
  userId?: string | number;

  /**
   * 用户昵称
   */
  userName?: string;

  /**
   * 管理员ID
   */
  adminId?: string | number;

  /**
   * 审核状态（0通过 1未通过)
   */
  status?: string;

  /**
   * 企业名称
   */
  companyName?: string;

  /**
   * 企业类型
   */
  companyType?: string;

  /**
   * 联系人名
   */
  contactPerson?: string;

  /**
   * 联系方式
   */
  contactPhone?: string;

  /**
   * 邮箱
   */
  email?: string;

  /**
   * 身份证
   */
  cardIdentity?:string,

  /**
   * 营业执照名称
   */

  licenseName?:string,


  /**
   * 营业执照注册号
   */
  licenseNumber?:string,

  /**
   * 地址
   */
  adress?:string,

  /**
   * 经营许可证
   */
  businessLicense?:string,

  /**
   * 营业执照过期时间
   */
  licenseTime?:string,


  /**
   * 经营许可证过期时间
   */
  businessLicenseTime?:string,



  /**
   * 营业执照
   */
  license?: string;

  /**
   * 备注
   */
  remark?: string;

  /**
   * 日期范围参数
   */
  params?: any;
}


