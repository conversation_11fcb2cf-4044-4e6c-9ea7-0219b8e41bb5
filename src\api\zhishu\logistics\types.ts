export interface LogisticsVO {

  /**
   * 物流模版id
   */
  id: number | string;
  /**
   * 物流模板名称
   */
  templateName: string;

  /**
   * 发货地-省
   */
  deliveryProvince: string;

  /**
   * 发货地-市
   */
  deliveryCity: string;

  /**
   * 发货地-区
   */
  deliveryArea: string;

  /**
   * 详细地址
   */
  deliveryAddress: string;

  /**
   * 计价方式（0按重量 1按标准本数（图书专用） 2按件数 3单独设置运费）
   */
  pricingMethod: string;

  /**
   * 运送方式（0快递 1自提）
   */
  shipping: string;

  /**
   * 首重 首本 首件
   */
  firWbv: number;

  /**
   * 首费 单位：元
   */
  firPrice: number;

  /**
   * 续重 续本 续件
   */
  continueWbv: number;

  /**
   * 续费 单位：元
   */
  continuePrice: number;

  /**
   * 模板状态（0正常 1停用）
   */
  status: string;

  /**
   * 运送范围
   */
  shippingRange: string;

  /**
   * 仓库ID
   */
  warehouseId: string | number;

  /**
   * 仓库名称
   */
  warehouseName:string;

}

export interface LogisticsForm extends BaseEntity {
  /**
   * 物流模版id
   */
  id?: number | string;
  /**
   * 物流模板名称
   */
  templateName?: string;

  /**
   * 发货地-省
   */
  deliveryProvince?: string;

  /**
   * 发货地-市
   */
  deliveryCity?: string;

  /**
   * 发货地-区
   */
  deliveryArea?: string;

  /**
   * 详细地址
   */
  deliveryAddress?: string;

  /**
   * 计价方式（0按重量 1按标准本数（图书专用） 2按件数 3单独设置运费）
   */
  pricingMethod?: string;

  /**
   * 运送方式（0快递 1自提）
   */
  shipping?: string;

  /**
   * 首重 首本 首件
   */
  firWbv?: number;

  /**
   * 首费 单位：元
   */
  firPrice?: number;

  /**
   * 续重 续本 续件
   */
  continueWbv?: number;

  /**
   * 续费 单位：元
   */
  continuePrice?: number;

  /**
   * 模板状态（0正常 1停用）
   */
  status?: string;

  /**
   * 运送范围
   */
  shippingRange?: string;

  /**
   * 仓库ID
   */
  warehouseId?: string | number;

  /**
   * 仓库名称
   */
  warehouseName?:string;

}

export interface LogisticsQuery extends PageQuery {
  /**
   * 物流模版id
   */
  id?: number | string;

  /**
   * 物流模板名称
   */
  templateName?: string;

  /**
   * 发货地-省
   */
  deliveryProvince?: string;

  /**
   * 发货地-市
   */
  deliveryCity?: string;

  /**
   * 发货地-区
   */
  deliveryArea?: string;

  /**
   * 详细地址
   */
  deliveryAddress?: string;

  /**
   * 计价方式（0按重量 1按标准本数（图书专用） 2按件数 3单独设置运费）
   */
  pricingMethod?: string;

  /**
   * 运送方式（0快递 1自提）
   */
  shipping?: string;

  /**
   * 首重 首本 首件
   */
  firWbv?: number;

  /**
   * 首费 单位：元
   */
  firPrice?: number;

  /**
   * 续重 续本 续件
   */
  continueWbv?: number;

  /**
   * 续费 单位：元
   */
  continuePrice?: number;

  /**
   * 模板状态（0正常 1停用）
   */
  status?: string;

  /**
   * 运送范围
   */
  shippingRange?: string;

  /**
   * 仓库ID
   */
  warehouseId?: string | number;

    /**
     * 日期范围参数
     */
    params?: any;

  /**
   * 仓库名称
   */
  warehouseName?:string;
}



