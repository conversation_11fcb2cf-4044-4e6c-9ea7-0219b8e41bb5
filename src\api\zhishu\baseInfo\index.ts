import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import {
  BaseInfoVO,
  BaseInfoForm,
  BaseInfoQuery,
  IllDataUpSet,
  AdvSalesSearch,
  userVoMap
} from '@/api/zhishu/baseInfo/types';
import { UserRechargeForm } from '@/api/zhishu/userRecharge/types';

/**
 * 查询基础信息列表
 * @param query
 * @returns {*}
 */

export const listBaseInfo = (query?: BaseInfoQuery): AxiosPromise<BaseInfoVO[]> => {
  return request({
    url: '/zhishu/baseInfo/list',
    method: 'get',
    params: query,
    withCredentials: true, // 关键配置
  });
};
// https://test.centerbook.buzhiyushu.cn/api/bookBase/getBookBaseInfo
// http://**************:9009/api/bookBase/getBookBaseInfo
//
/**
 * 查询基础信息详细
 * @param id
 */
export const getBaseInfo = (id: string | number): AxiosPromise<BaseInfoVO> => {
  return request({
    url: '/zhishu/baseInfo/' + id,
    method: 'get'
  });
};

/**
 * 新增基础信息
 * @param data
 */
export const addBaseInfo = (data: BaseInfoForm) => {
  return request({
    url: '/zhishu/baseInfo',
    method: 'post',
    data: data
  });
};

/**
 * 修改基础信息
 * @param data
 */
export const updateBaseInfo = (data: BaseInfoForm) => {
  return request({
    url: '/zhishu/baseInfo',
    method: 'put',
    data: data
  });
};
// /zhishu/baseInfo
/**
 * 删除基础信息
 * @param id
 */
export const delBaseInfo = (id: string | number | Array<string | number>) => {
  return request({
    url: '/zhishu/baseInfo/' + id,
    method: 'delete'
  });
};

// 批量修改违规书号
export const updateBaseIllgeData=(data:IllDataUpSet)=>{
  return request({
    url: '/zhishu/baseInfo/batchUpdateIll',
    // url: 'https://**************:9004/api/books/setIllInfo',
    method: 'put',
    data:data
  });
};

/**
 * 核价链接加密
 * @param query
 * @returns {*}
 */

export const linkEncry = (query?: BaseInfoQuery): AxiosPromise<string> => {
  return request({
    url: '/zhishu/baseInfo/pricingLink',
    method: 'get',
    params: query
  });
};


//获取销量信息
// export const saleSearch = (isbn: string): AxiosPromise<AdvSalesSearch> => {
//   return request({
//     url: `https://gather.buzhiyushu.cn/api/price/querySalesFromDB/${isbn}`,
//     method: 'get',
//     headers: {
//       'Content-Type': 'application/json',
//     },
//     withCredentials: false  // 必须关闭！除非后端明确需要
//   });
// };


// 链接到负载均衡B中心书库
export const getUseriSVip = (type:number): AxiosPromise<boolean> => {
  return request({
    // url: 'http://**************:9004/api/user/vipInfo',
    url: '/zhishu/baseInfo/user/vipInfo/'+type,
    method: 'get',
  });
};

// 按钮充值
export const postRecharge = (data: UserRechargeForm) => {
  return request({
    url: '/zhishu/baseInfo/buttonUserRecharge',
    method: 'post',
    data: data
  });
};

//扣除余额
export const setBalance = (): AxiosPromise<userVoMap> => {
  return request({
    url: '/zhishu/baseInfo/setBalance',
    method: 'get',
  });
};


// 页面充值
export const pageRecharge = (data: UserRechargeForm) => {
  return request({
    url: '/zhishu/baseInfo/userRecharge',
    method: 'post',
    data: data
  });
};

//页面图片展示
export const getImage = (isbn:number): AxiosPromise<userVoMap> => {
  return request({
    url: '/zhishu/baseInfo/getImageDeit/'+isbn,
    method: 'get',
  });
};
