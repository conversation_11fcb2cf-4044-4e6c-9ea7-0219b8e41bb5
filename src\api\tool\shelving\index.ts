// product.ts
import request from '@/utils/request';
import { AxiosPromise } from 'axios';

/**
 * 根据库号和商品条码查询商品信息
 * @param warehouseCode 库号（格式：xx-xx-xx-xx-xx-xx）
 * @param barcode 商品条码
 */
export const getProductByBarcode = (
    barcode: string
): AxiosPromise<ProductVO[]> => {
    return request({
        url: '/zhishu/shopGoods/info',
        method: 'get',
        params: {
            barcode
        }
    });
};

/**
 * 根据商品ID查询库存信息
 * @param productId 商品ID
 */
export const getStockInfo = (
    productId: string,
    warehouseCode: string
): AxiosPromise<{ stock: number; price: number }> => {
    return request({
        url: `/zhishu/shopGoods/stock`,
        method: 'get',
        params: {
            productId,
            warehouseCode
        }
    });
};

/**
 * 提交商品信息（包含照片）
 * @param formData 商品表单数据（自动包含ID）
 * @param photos 照片文件列表
 */
export const submitProduct = (
    formData: FormData,
    photos: PhotoItem[]
): AxiosPromise<void> => {

    return request({
        url: '/zhishu/shopGoods/submit',
        method: 'post',
        data: formData,
    })
}


// ==================== 类型定义文件 types.ts ====================
/**
 * 商品查询参数类型
 */
export interface ProductQuery {
    warehouseCode: string;  // 标准库号格式：xx-xx-xx-xx-xx-xx
    barcode: string;
}

/**
 * 商品视图对象
 */
export interface ProductVO {
    id: string;
    name: string;
    barcode: string;
    price: number;
    stock: number;
    condition: number;
    warehouseCode: string;
}

/**
 * 商品提交表单类型
 */
export interface ProductForm extends ProductVO {
    // 继承自ProductVO，包含所有字段
}

/**
 * 照片项类型
 */
export interface PhotoItem {
    file: File;
    preview: string;  // 本地预览URL
}

/**
 * 商品查询响应类型
 */
export interface ProductListResponse {
    list: ProductVO[];
    total: number;
}