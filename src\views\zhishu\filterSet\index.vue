<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="过滤类型" prop="filterType">
              <el-select v-model="queryParams.filterType" placeholder="请选择过滤类型" clearable >
                <el-option v-for="dict in t_filter_type" :key="dict.value" :label="dict.label" :value="dict.value"/>
              </el-select>
            </el-form-item>
            <el-form-item label="限制类型" prop="limitationType">
              <el-select v-model="queryParams.limitationType" placeholder="请选择限制类型" clearable >
                <el-option v-for="dict in t_shop_set_up" :key="dict.value" :label="dict.label" :value="dict.value"/>
              </el-select>
            </el-form-item>
            <el-form-item label="添加方式" prop="addWay">
              <el-select v-model="queryParams.addWay" placeholder="请选择添加方式" clearable >
                <el-option v-for="dict in t_add_way" :key="dict.value" :label="dict.label" :value="dict.value"/>
              </el-select>
            </el-form-item>



            <el-form-item label="内容文件" prop="addTxt">
              <el-input v-model="queryParams.addTxt" placeholder="请输入内容文件" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['zhishu:filterSet:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['zhishu:filterSet:edit']">修改</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['zhishu:filterSet:remove']">删除</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['zhishu:filterSet:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="filterSetList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="主键" align="center" prop="id" v-if="true" />
        <el-table-column label="过滤类型" align="center" prop="filterType">
          <template #default="scope">
            <dict-tag :options="t_filter_type" :value="scope.row.filterType"/>
          </template>
        </el-table-column>
        <el-table-column label="限制类型" align="center" prop="limitationType">
          <template #default="scope">
            <dict-tag :options="t_shop_set_up" :value="scope.row.limitationType"/>
          </template>
        </el-table-column>
        <el-table-column label="添加方式" align="center" prop="addWay">
          <template #default="scope">
            <dict-tag :options="t_add_way" :value="scope.row.addWay"/>
          </template>
        </el-table-column>
        <el-table-column label="内容文件" align="center" prop="addTxt" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['zhishu:filterSet:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['zhishu:filterSet:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改过滤设置对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body>
      <el-form ref="filterSetFormRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="过滤类型" prop="filterType">
          <el-select v-model="form.filterType" placeholder="请选择过滤类型">
            <el-option
                v-for="dict in t_filter_type"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="限制类型" prop="limitationType">
          <el-select v-model="form.limitationType" placeholder="请选择限制类型">
            <el-option
                v-for="dict in t_shop_set_up"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="添加方式" prop="addWay" @change="changeAddWay()">
          <el-radio-group v-model="form.addWay">
            <el-radio
              v-for="dict in t_add_way"
              :key="dict.value"
              :value="dict.value"
            >{{dict.label}}</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item :class="{isHide : form.addWay == 0}" label="文件选择" prop="fileUrl">
          <el-row>
            <el-col :span="14">
              <el-upload
                ref="upload"
                v-model:file-list="fileList"
                class="upload-demo"
                :action="uploadImgUrl"
                :limit="1"
                :headers="headers"
                :on-success="handleAvatarSuccess"
                :before-upload="beforeAvatarUpload"
                :list-type="picture"
                accept=".xlsx"
                :auto-upload="false"
              >
                <template #trigger>
                  <el-button type="primary">上传文件</el-button>
                </template>
              </el-upload>
            </el-col>
            <el-col :span="6">
              <el-button @click="importTemplate">下载模板</el-button>
            </el-col>
          </el-row>

        </el-form-item>

        <el-form-item :class="{isHide : form.addWay == 1}" label="内容文件" prop="addTxt">
            <el-input v-model="form.addTxt" type="textarea" placeholder="请输入限制内容,数据之间回车分割,每行一个数据" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="FilterSet" lang="ts">
import { listFilterSet, getFilterSet, delFilterSet, addFilterSet, updateFilterSet } from '@/api/zhishu/filterSet';
import { FilterSetVO, FilterSetQuery, FilterSetForm } from '@/api/zhishu/filterSet/types';
import type { UploadInstance, UploadProps, UploadRawFile,UploadUserFile  } from 'element-plus'
import { globalHeaders } from '@/utils/request';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { t_add_way, t_shop_set_up, t_filter_type } = toRefs<any>(proxy?.useDict('t_add_way', 't_shop_set_up', 't_filter_type'));

const filterSetList = ref<FilterSetVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const filterSetFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: FilterSetForm = {
  id: undefined,
  filterType: undefined,
  limitationType: undefined,
  addWay: '0',
  addTxt: undefined,
  status: undefined,
}
const data = reactive<PageData<FilterSetForm, FilterSetQuery>>({
  form: {...initFormData},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    filterType: undefined,
    limitationType: undefined,
    addWay: undefined,
    addTxt: undefined,
    status: undefined,
    params: {
    }
  },
  rules: {
    id: [{ required: true, message: "主键不能为空", trigger: "blur" }],
    filterType: [{ required: true, message: "过滤类型不能为空", trigger: "blur" }],
    limitationType: [{ required: true, message: "限制类型不能为空", trigger: "blur" }],
    addWay: [{ required: true, message: "添加方式不能为空", trigger: "blur" }],
    addTxt: [{ required: true, message: "内容文件不能为空", trigger: "blur" }],
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询过滤设置列表 */
const getList = async () => {
  loading.value = true;
  const res = await listFilterSet(queryParams.value);
  filterSetList.value = res.rows;
  total.value = res.total;
  loading.value = false;
}

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
}

/** 表单重置 */
const reset = () => {
  form.value = {...initFormData};
  filterSetFormRef.value?.resetFields();
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
}

/** 多选框选中数据 */
const handleSelectionChange = (selection: FilterSetVO[]) => {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = "添加过滤设置";
}

/** 修改按钮操作 */
const handleUpdate = async (row?: FilterSetVO) => {
  reset();
  const _id = row?.id || ids.value[0]
  const res = await getFilterSet(_id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = "修改过滤设置";
}

/** 提交按钮 */
const submitForm = () => {

  if(form.value.addWay == "1"){
    submitUpload();
  }else{
    submitFileForm();
  }
}

/** 删除按钮操作 */
const handleDelete = async (row?: FilterSetVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除过滤设置编号为"' + _ids + '"的数据项？').finally(() => loading.value = false);
  await delFilterSet(_ids);
  proxy?.$modal.msgSuccess("删除成功");
  await getList();
}

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download('zhishu/filterSet/export', {
    ...queryParams.value
  }, `filterSet_${new Date().getTime()}.xlsx`)
}

/** 添加方式内容改变方法 */
const changeAddWay = () => {
  upload.value!.clearFiles();
  form.value.addTxt = undefined;
}

/** 文件上传 */
const baseUrl = import.meta.env.VITE_APP_BASE_API;
const headers = ref(globalHeaders());
const uploadImgUrl = ref(baseUrl + '/zhishu/filterSet/upload'); // 上传的图片服务器地址
const upload = ref<UploadInstance>()
const fileList = ref<UploadRawFile>();

/** 文件上传事件 */
const submitUpload = () => {
  form.value.addTxt = "1";
  filterSetFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      form.value.addTxt = undefined;

      if(fileList.value.length == 0){
        proxy?.$modal.msgError('请上传文件');
        return false;
      }
      upload.value!.submit()
    }
  })
}

/** 下载模板操作 */
const importTemplate = () => {
  proxy?.download('zhishu/filterSet/importTemplate', {}, `filter_template_${new Date().getTime()}.xlsx`);
};

/** 文件上传后回调 */
const handleAvatarSuccess: UploadProps['onSuccess'] = async (response, uploadFile) => {
  const data = response.data;
  let addTxt = "";
  for(var i=0;i<data.length;i++){
    if(addTxt == ""){
      addTxt = data[i].addTxt;
    }else{
      addTxt = addTxt + "\n" + data[i].addTxt;
    }
  }
  form.value.addTxt = addTxt;
  submitFileForm();
};

const submitFileForm = () => {
  filterSetFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateFilterSet(form.value).finally(() =>  buttonLoading.value = false);
      } else {
        await addFilterSet(form.value).finally(() =>  buttonLoading.value = false);
      }
      proxy?.$modal.msgSuccess("操作成功");
      dialog.visible = false;
      await getList();
    }
  });
}

onMounted(() => {
  getList();
});
</script>

<style>
.isHide{
  display: none;
}
</style>
