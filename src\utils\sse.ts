import { getToken } from '@/utils/auth';
import { ElNotification } from 'element-plus';
import useNoticeStore from '@/store/modules/notice';

// 初始化
export const initSSE = (url: any) => {


  if (import.meta.env.VITE_APP_SSE === 'false') {
    return;
  }

  // 创建事件总线（全局通知）
  const eventBus = useEventBus<string>('sys-notification');

  url = url + '?Authorization=Bearer ' + getToken() + '&clientid=' + import.meta.env.VITE_APP_CLIENT_ID;
  const { data, error } = useEventSource(url, [], {
    autoReconnect: {
      retries: 10,
      delay: 3000,
      onFailed() {
        console.log('Failed to connect after 10 retries');
      }
    }
  });

  watch(error, () => {
    console.log('SSE connection error:', error.value);
    error.value = null;
  });

  watch(data, () => {
    if (!data.value) return;
    useNoticeStore().addNotice({
      message: data.value,
      read: false,
      time: new Date().toLocaleString()
    });

    // 2. 触发全局刷新事件（关键改动点）
    eventBus.emit('refresh-shop');  // 发送刷新指
    eventBus.emit('refresh-task');  // 发送刷新指


    if(data.value == '授权成功'){
      ElNotification({
        title: '授权成功',
        message: data.value,
        type: 'success',
        duration: 5000
      });
    }else if(data.value == '授权失败'){
      ElNotification({
        title: '授权失败',
        message: '登录的店铺和之前店铺的绑定信息不一致，请登录和绑定店铺一致的账号，如果是新店铺，请点击添加店铺，然后再点击新店铺的授权进行绑定',
        type: 'error',
        duration: 10000
      });
    }else{
      ElNotification({
        title: '消息',
        message: data.value,
        type: 'success',
        duration: 3000
      });
    }
    data.value = null;
  });
};
