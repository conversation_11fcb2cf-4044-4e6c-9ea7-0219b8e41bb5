<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="违规类型" prop="type">
              <el-select v-model="queryParams.type" placeholder="请选择违规类型" clearable >
                <el-option v-for="dict in t_violation_type" :key="dict.value" :label="dict.label" :value="dict.value"/>
              </el-select>
            </el-form-item>
            <el-form-item label="审核状态" prop="review">

              <el-select v-model="queryParams.review" placeholder="请选择审核状态" clearable >
                <el-option v-for="dict in t_review" :key="dict.value" :label="dict.label" :value="dict.value"/>
              </el-select>


            </el-form-item>
            <el-form-item label="状态" prop="status">
              <el-select v-model="queryParams.status" placeholder="请选择状态" clearable >
                <el-option v-for="dict in sys_normal_disable" :key="dict.value" :label="dict.label" :value="dict.value"/>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain @click="handleAdd" v-hasPermi="['zhishu:violation:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain :disabled="single" @click="handleUpdate()" v-hasPermi="['zhishu:violation:edit']">修改</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain  :disabled="multiple" @click="handleDelete()" v-hasPermi="['zhishu:violation:remove']">删除</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain  :disabled="multiple" @click="submitType()" v-hasPermi="['zhishu:violation:editType']">提交</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="primary" plain  :disabled="multiple" @click="chongShen()" v-hasPermi="['zhishu:violation:editType']">重审</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="violationList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="主键" align="center" prop="id" v-if="false" />
        <el-table-column label="违规平台" align="center" prop="sort">
          <template #default="scope">
             <span v-for="fruit in scope.row.sort.split(',')" :key="fruit"  >
        
                 <dict-tag :options="t_violation_sort" :value="fruit" style="display:contents"/>
              </span>
          </template>
        </el-table-column>
        
        <el-table-column label="违规类型" align="center" prop="type">
          <template #default="scope">
            <dict-tag :options="t_violation_type" :value="scope.row.type"/>
          </template>
        </el-table-column>
        <el-table-column label="违规内容" align="center" prop="name" />
        <el-table-column label="违规原因" align="center" prop="content" />
        <el-table-column label="审核状态" align="center" prop="review" >
          <template #default="scope">
            <dict-tag :options="t_review" :value="scope.row.review"/>
          </template>
        </el-table-column>
        <el-table-column label="违规原因" align="center" prop="remark" />
        <el-table-column label="状态" align="center" prop="status">
          <template #default="scope">
            <dict-tag :options="sys_normal_disable" :value="scope.row.status"/>
          </template>
        </el-table-column>
        <!-- <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['zhishu:violation:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['zhishu:violation:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column> -->
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改违规对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body>
      <el-form ref="violationFormRef" :model="form" :rules="rules" label-width="80px">

        <el-form-item label="违规平台" prop="sort">
          <el-checkbox-group v-model="form.sort">
            <el-checkbox v-for="dict in t_violation_sort" :key="dict.value" :label="dict.value">
              {{ dict.label }}
            </el-checkbox>
          </el-checkbox-group>
        </el-form-item>

        
        <el-form-item label="审核类型" prop="type">
          <el-select v-model="form.type" placeholder="请选择审核类型">
            <el-option
                v-for="dict in t_violation_type"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="违规内容" prop="name">
          <el-input v-model="form.name" placeholder="请输入违规内容" />
        </el-form-item>


        <el-form-item label="违规原因" prop="content">
          <el-input v-model="form.content" placeholder="请输入违规原因" />
        </el-form-item>


        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio
              v-for="dict in sys_normal_disable"
              :key="dict.value"
              :value="dict.value"
            >{{dict.label}}</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Violation" lang="ts">
import { listViolation, getViolation, delViolation, addViolation, updateViolation,editType } from '@/api/zhishu/violation';
import { ViolationVO, ViolationQuery, ViolationForm } from '@/api/zhishu/violation/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { t_violation_type, sys_normal_disable,t_review,t_violation_sort } = toRefs<any>(proxy?.useDict('t_violation_type', 'sys_normal_disable','t_review','t_violation_sort'));

const violationList = ref<ViolationVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const names = ref<Array<string>>([]);
const reviews = ref<Array<string>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const violationFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: ViolationForm = {
  id: undefined,
  type: undefined,
  content: undefined,
  userid: undefined,
  review: undefined,
  status: '0',
  remark: undefined,
  name:undefined,
  sort:undefined
}
const data = reactive<PageData<ViolationForm, ViolationQuery>>({
  form: {...initFormData},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    type: undefined,
    content: undefined,
    userid: undefined,
    review: undefined,
    status: '0',
    remark: undefined,
    name:undefined,
    sort:undefined,
    params: {
    }
  },
  rules: {
    id: [
      { required: true, message: "主键不能为空", trigger: "blur" }
    ],
    sort: [
      { required: true, message: "违规平台不能为空", trigger: "blur" }
    ],
    type: [
      { required: true, message: "审核类型不能为空", trigger: "blur" }
    ],
    name: [
      { required: true, message: "审核内容不能为空", trigger: "blur" }
    ],
    content: [
      { required: true, message: "审核原因不能为空", trigger: "blur" }
    ]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询违规列表 */
const getList = async () => {
  loading.value = true;
  const res = await listViolation(queryParams.value);
  violationList.value = res.rows;
  total.value = res.total;
  loading.value = false;
}

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
}

/** 表单重置 */
const reset = () => {
  form.value = {...initFormData};
  violationFormRef.value?.resetFields();
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
}

/** 多选框选中数据 */
const handleSelectionChange = (selection: ViolationVO[]) => {
  ids.value = selection.map(item => item.id);
  names.value = selection.map(item => item.name);
  reviews.value = selection.map(item => item.review);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = "添加违规";
}

/** 修改按钮操作 */
const handleUpdate = async (row?: ViolationVO) => {

  for(var i=0;i<reviews.value.length;i++){
    if(reviews.value[i] != '0' && reviews.value[i] != '3' && reviews.value[i] != '4'){
      proxy?.$modal.msgError('无法修改正在审核的数据！！！');
      return;
    }
  }

  reset();
  const _id = row?.id || ids.value[0]
  const res = await getViolation(_id);
  Object.assign(form.value, res.data);

  if (form.value.sort != null && form.value.sort != undefined) {
    form.value.sort = form.value.sort.split(',');
  } else {
    form.value.sort = [];
  }

  dialog.visible = true;
  dialog.title = "修改违规";
}

/** 提交按钮 */
const submitForm = () => {
  violationFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      form.value.sort = form.value.sort.join(',');
      if (form.value.id) {
        await updateViolation(form.value).finally(() =>  buttonLoading.value = false);
      } else {
        await addViolation(form.value).finally(() =>  buttonLoading.value = false);
      }
      proxy?.$modal.msgSuccess("操作成功");
      dialog.visible = false;
      await getList();
    }
  });
}

/** 删除按钮操作 */
const handleDelete = async (row?: ViolationVO) => {
  for(var i=0;i<reviews.value.length;i++){
    if(reviews.value[i] == '1'){
      proxy?.$modal.msgError('无法删除正在审核中的商品');
      return;
    }else if(reviews.value[i] == '2'){
      proxy?.$modal.msgError('无法删除审核通过的商品');
      return;
    }
  }
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除违规编号为"' + _ids + '"的数据项？').finally(() => loading.value = false);
  await delViolation(_ids);
  proxy?.$modal.msgSuccess("删除成功");
  await getList();
}

/** 提交审核 */
const submitType = async(row?: ViolationVO) =>{

  for(var i=0;i<reviews.value.length;i++){
    if(reviews.value[i] != '0' && reviews.value[i] != '3'){
      proxy?.$modal.msgError('只能提交 "待提交" 和 "已撤回" 的数据 ！！！');
      return;
    }
  }
  
  const _ids = row?.id || ids.value;
  const _names = row?.name || names.value;
  await proxy?.$modal.confirm('是否确认提交审核违规内容为"' + _names + '"的数据项？').finally(() => loading.value = false);
  await editType(_ids,'1');
  proxy?.$modal.msgSuccess("提交成功");
  await getList();
}

/** 重审按钮 */
const chongShen = async(row?: ViolationVO) =>{

  for(var i=0;i<reviews.value.length;i++){
    if(reviews.value[i] != '4' ){
      proxy?.$modal.msgError('只针对 "审核失败" 的数据！！！');
      return;
    }
  }
  
  const _ids = row?.id || ids.value;
  const _names = row?.name || names.value;
  await proxy?.$modal.confirm('是否确认提交审核违规内容为"' + _names + '"的数据项？').finally(() => loading.value = false);
  await editType(_ids,'1');
  proxy?.$modal.msgSuccess("提交成功");
  await getList();
}

onMounted(() => {
  getList();
});
</script>
