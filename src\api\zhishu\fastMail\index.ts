import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { FastMailVO, FastMailForm, FastMailQuery } from '@/api/zhishu/fastMail/types';

/**
 * 查询快递打单账号管理列表
 * @param query
 * @returns {*}
 */

export const listFastMail = (query?: FastMailQuery): AxiosPromise<FastMailVO[]> => {
  return request({
    url: '/zhishu/fastMail/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询快递打单账号管理详细
 * @param id
 */
export const getFastMail = (id: string | number): AxiosPromise<FastMailVO> => {
  return request({
    url: '/zhishu/fastMail/' + id,
    method: 'get'
  });
};

/**
 * 新增快递打单账号管理
 * @param data
 */
export const addFastMail = (data: FastMailForm) => {
  return request({
    url: '/zhishu/fastMail',
    method: 'post',
    data: data
  });
};

/**
 * 修改快递打单账号管理
 * @param data
 */
export const updateFastMail = (data: FastMailForm) => {
  return request({
    url: '/zhishu/fastMail',
    method: 'put',
    data: data
  });
};

/**
 * 删除快递打单账号管理
 * @param id
 */
export const delFastMail = (id: string | number | Array<string | number>) => {
  return request({
    url: '/zhishu/fastMail/' + id,
    method: 'delete'
  });
};
