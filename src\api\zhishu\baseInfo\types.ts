import { set } from 'js-cookie';

export interface BaseInfoVO {
  /**
   * id
   */
  id: string | number;

  /**
   * 书名
   */
  bookName: string;

  /**
   * 书图片
   */
  bookPic: string;

  /**
   * ibsn
   */
  isbn: string;

  /**
   * 作者
   */
  author: string;

  /**
   * 编辑
   */
  editor: string;
  //targetDSN := "proxy_main:Long6166@@!@tcp(127.0.0.1:6033)/main?charset=utf8mb4&parseTime=True&loc=Local"

  /**
   * 装帧
   */
  bindingLayout: string;

  /**
   * 出版社
   */
  publisher: string;

  /**
   * 版次
   */
  edition: string;

  /**
   * 开本
   */
  format: string;

  /**
   * 语种
   */
  languages: string;

  /**
   * 出版时间
   */
  publicationTime: string;

  /**
   * 印刷时间
   */
  printTime: string;

  /**
   * 纸张
   */
  paper: string;

  /**
   * 页数
   */
  pages: string;

  /**
   * 字数
   */
  wordage: string;

  /**
   * 定价
   */
  fixPrice: string;

  /**
   * 已售
   */
  buyCount:string;

  /**
   * 在售
   */
  sellCount:string;

  /**
   * 内容
   */
  content: string;

  /**
   * 备注
   */
  remark: string;

  /**
   * 已售大于min1
   */
  min1:number|string;

  /**
   * 已售小于max1
   */
  max1:number|string;

  /**
   *在售大于min1
   */
  min2:number|string;

  /**
   *在售小于max1
   */
  max2:number|string;


  /**
   * 已售数量
   */
  buy_counts:number|string;

  /**
   * 在售数量
   */
  sell_counts:number|string;

  /**
   *出版时间
   */
  publiction_times:number|string;

  /**
   * 违规书号
   */
  vioBook : number;

  /**
   * 套装书
   */
  bookSet : number;

  /**
   * 一号多书
   */
  onenumMbooks:number;

  /**
   * 违规出版社
   */
  ill_publisher:number;

  //销量7天
  daySale7 :number;
  //销量15天
  daySale15 :number;
  //销量30天
  daySale30 :number;
  //销量60天
  daySale60 :number;
  //销量90天
  daySale90 :number;
  //销量180天
  daySale180 :number;
  //销量365天
  daySale365 :number;
  //今年销量
  thisYearSale :number;
  //去年销量
  lastYearSale :number;
  //总销量
  totalSale :number;
  salesData:AdvSalesSearch,

  //分类
  category : string;



}

export interface BaseInfoForm extends BaseEntity {
  /**
   * id
   */
  id?: string | number;

  /**
   * 书名
   */
  bookName?: string;

  /**
   * 书图片
   */
  bookPic?: string;

  /**
   * ibsn
   */
  isbn?: string;

  /**
   * 作者
   */
  author?: string;

  /**
   * 编辑
   */
  editor?: string;

  /**
   * 装帧
   */
  bindingLayout?: string;

  /**
   * 出版社
   */
  publisher?: string;

  /**
   * 版次
   */
  edition?: string;

  /**
   * 开本
   */
  format?: string;

  /**
   * 语种
   */
  languages?: string;

  /**
   * 出版时间
   */
  publicationTime?: string;

  /**
   * 印刷时间
   */
  printTime?: string;

  /**
   * 纸张
   */
  paper?: string;

  /**
   * 页数
   */
  pages?: string;

  /**
   * 字数
   */
  wordage?: string;

  /**
   * 定价
   */
  fixPrice?: string;

  /**
   * 已售
   */
  buyCount?:string;

  /**
   * 在售
   */
  sellCount?:string;

  /**
   * 内容
   */
  content?: string;

  /**
   * 备注
   */
  remark?: string;
  /**
   * 违规书号
   */
  vioBook:number,
  /**
   * 套装书
   */
  bookSet:number,
  /**
   * 一号多书
   */
  onenumMbooks:number,
  /**
   * 违规出版社
   */
  illPublisher:number,

}

export interface BaseInfoQuery extends PageQuery {

  /**
   * 书名
   */
  bookName?: string;

  /**
   * 书图片
   */
  bookPic?: string;

  /**
   * ibsn
   */
  isbn?: string;

  /**
   * 作者
   */
  author?: string;

  /**
   * 编辑
   */
  editor?: string;

  /**
   * 装帧
   */
  bindingLayout?: string;

  /**
   * 出版社
   */
  publisher?: string;

  /**
   * 版次
   */
  edition?: string;

  /**
   * 开本
   */
  format?: string;

  /**
   * 语种
   */
  languages?: string;

  /**
   * 出版时间
   */
  publicationTime?: string;
  /**
   * 出版时间
   */
  publication?: string;

  /**
   * 印刷时间
   */
  printTime?: string;

  /**
   * 纸张
   */
  paper?: string;

  /**
   * 页数
   */
  pages?: string;

  /**
   * 字数
   */
  wordage?: string;

  /**
   * 定价
   */
  fixPrice?: string;

  /**
   * 已售
   */
  buyCount?:string;

  /**
  * 在售
 */
  sellCount?:string;

  /**
   * 内容
   */
  content?: string;

    /**
     * 日期范围参数
     */
    params?: any;

  /**
   * 出版开始时间
   */
  publicationStartTime?: string,
  /**
   * 出版结束时间
   */
  publicationEndTime?: string,

}

export interface IllDataUpSet{
   ids?: number[];
   vioBook?:number;
   bookSet?:number;
   onenumMbooks?:number;
   illPublisher?:number;
   illAuthor?:number;
};


export interface AdvSalesSearch{
  isbn?:number|string;
  daySale7?: number;
  daySale15?: number;
  daySale30?: number;
  daySale60?: number;
  daySale90?: number;
  daySale180?: number;
  daySale365?: number;
  thisYearSale?: number;
  lastYearSale?: number;
  sale?: number;
  soldOut1?: string;
  soldOut2?: string;
  soldOut3?: string;
  soldOut4?: string;
  soldOut5?: string;
  soldOut6?: string;
  soldOut7?: string;
  soldOut8?: string;
  shipmentCycle?: string;
}
export type userVoMap = Record<string, object>;
