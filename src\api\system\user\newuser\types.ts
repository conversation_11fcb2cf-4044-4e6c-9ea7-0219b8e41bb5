// export interface UserForm {
//   id?: string;
//   userId?: string;
//   deptId?: number;
//   userName: string;
//   nickName?: string;
//   password: string;
//   phonenumber?: string;
//   email?: string;
//   sex?: string;
//   status: string;
//   remark?: string;
//   postIds: string[];
//   roleIds: string[];
// }
export  interface  NewUserForm{
  /**
   * 企业名称
   */
  companyName?: string,
  /**
   * 联系人
   */
  contactPerson?: string,
  /**
   * 联系电话
   */
  contactPhone?: string,
  /**
   * 电子邮箱
   */
  email?: string,
  /**
   * 企业类型
   */
  companyType?: string,

  /**
   * 身份证
   */
  cardIdentity?:string,

  /**
   * 营业执照名称
   */

  licenseName?:string,


  /**
   * 营业执照注册号
   */
  licenseNumber?:string,

  /**
   * 营业执照
   */
  license?: string,
  /**
   * 备注
   */
  remark?: string,

  /**
   * 地址
   */
  adress?:string,

  /**
   * 经营许可证
   */
  businessLicense?:string,

  /**
   * 营业执照过期时间
   */
  licenseTime?:string,


  /**
   * 经营许可证过期时间
   */
  businessLicenseTime?:string,
}
