import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { UserAuditVO, UserAuditForm, UserAuditQuery } from '@/api/zhishu/userAudit/types';
import { AuditVO } from '@/api/zhishu/audit/types';

/**
 * 查询列表列表
 * @param query
 * @returns {*}
 */

export const listUserAudit = (query?: UserAuditQuery): AxiosPromise<UserAuditVO[]> => {
  return request({
    url: '/zhishu/userAudit/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询列表详细
 * @param id
 */
export const getUserAudit = (id: string | number): AxiosPromise<UserAuditVO> => {
  return request({
    url: '/zhishu/userAudit/' + id,
    method: 'get'
  });
};

/**
 * 新增列表
 * @param data
 */
export const addUserAudit = (data: UserAuditForm) => {
  return request({
    url: '/zhishu/userAudit',
    method: 'post',
    data: data
  });
};

/**
 * 修改列表
 * @param data
 */
export const updateUserAudit = (data: UserAuditForm) => {
  return request({
    url: '/zhishu/userAudit',
    method: 'put',
    data: data
  });
};

/**
 * 删除列表
 * @param id
 */
export const delUserAudit = (id: string | number | Array<string | number>) => {
  return request({
    url: '/zhishu/userAudit/' + id,
    method: 'delete'
  });
};

export const getUAudit = (id: string | number): AxiosPromise<UserAuditVO> => {
  return request({
    url: '/zhishu/userAudit/' + id,
    method: 'get'
  });
};

export const getAuditLog = (): AxiosPromise<UserAuditVO> => {
  return request({
    url: '/zhishu/userAudit/log',
    method: 'get'
  });
};
