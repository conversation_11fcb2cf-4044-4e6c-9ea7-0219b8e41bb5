export interface ShopImgVO {
  /**
   * 主键
   */
  id: string | number;

  /**
   * 父id
   */
  pid: string | number;

  /**
   * 相对路径
   */
  relativePath: string;

  /**
   * 绝对路径
   */
  absolutePath: string;

  /**
   * 类型  1封面水印  2商详水印 3 商详头图  4 商详尾图 5 轮播尾图
   */
  type: string;

  /**
   * 图片顺序
   */
  imgOrder: number;

  /**
   * 状态（0正常 1停用）
   */
  status: string;

}

export interface ShopImgForm extends BaseEntity {
  /**
   * 主键
   */
  id?: string | number;

  /**
   * 父id
   */
  pid?: string | number;

  /**
   * 相对路径
   */
  relativePath?: string;

  /**
   * 绝对路径
   */
  absolutePath?: string;

  /**
   * 类型  1封面水印  2商详水印 3 商详头图  4 商详尾图 5 轮播尾图
   */
  type?: string;

  /**
   * 图片顺序
   */
  imgOrder?: number;

  /**
   * 状态（0正常 1停用）
   */
  status?: string;

}

export interface ShopImgQuery extends PageQuery {

  /**
   * 父id
   */
  pid?: string | number;

  /**
   * 相对路径
   */
  relativePath?: string;

  /**
   * 绝对路径
   */
  absolutePath?: string;

  /**
   * 类型  1封面水印  2商详水印 3 商详头图  4 商详尾图 5 轮播尾图
   */
  type?: string;

  /**
   * 图片顺序
   */
  imgOrder?: number;

  /**
   * 店铺状态（0正常 1停用）
   */
  status?: string;

    /**
     * 日期范围参数
     */
    params?: any;
}



