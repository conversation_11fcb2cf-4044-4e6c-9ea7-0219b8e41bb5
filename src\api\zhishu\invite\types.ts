/**
 * 邀请码对象
 */
export interface InviteCodeVO {
  /** 邀请码ID */
  id: string | number;
  /** 邀请码 */
  code: string;
  /** 邀请链接 */
  inviteUrl: string;
  /** 过期时间 */
  expireTime: string;
  /** 使用状态（0未使用 1已使用） */
  used: boolean;
  /** 创建时间 */
  createdAt: string;
  /** 创建用户 */
  createBy: string;
}

/**
 * 邀请码查询参数
 */
export interface InviteCodeQuery {
  /** 页码 */
  pageNum: number;
  /** 每页条数 */
  pageSize: number;
}

/**
 * 生成邀请码参数
 */
export interface InviteCodeForm {
  /** 有效期(天) */
  expireDays: number;
} 