import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { FilterSetVO, FilterSetForm, FilterSetQuery } from '@/api/zhishu/filterSet/types';

/**
 * 查询过滤设置列表
 * @param query
 * @returns {*}
 */

export const listFilterSet = (query?: FilterSetQuery): AxiosPromise<FilterSetVO[]> => {
  return request({
    url: '/zhishu/filterSet/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询过滤设置详细
 * @param id
 */
export const getFilterSet = (id: string | number): AxiosPromise<FilterSetVO> => {
  return request({
    url: '/zhishu/filterSet/' + id,
    method: 'get'
  });
};

/**
 * 新增过滤设置
 * @param data
 */
export const addFilterSet = (data: FilterSetForm) => {
  return request({
    url: '/zhishu/filterSet',
    method: 'post',
    data: data
  });
};

/**
 * 修改过滤设置
 * @param data
 */
export const updateFilterSet = (data: FilterSetForm) => {
  return request({
    url: '/zhishu/filterSet',
    method: 'put',
    data: data
  });
};

/**
 * 删除过滤设置
 * @param id
 */
export const delFilterSet = (id: string | number | Array<string | number>) => {
  return request({
    url: '/zhishu/filterSet/' + id,
    method: 'delete'
  });
};
