<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">

            <el-form-item label="编号" prop="id">
              <el-input v-model="queryParams.id" placeholder="请输入编号" clearable @keyup.enter="handleQuery" />
            </el-form-item>

            <el-form-item label="三方平台编号" prop="orderSn" label-width="120">
              <el-input v-model="queryParams.orderSn" placeholder="请输入订单编号" clearable @keyup.enter="handleQuery" />
            </el-form-item>


            <el-form-item label="创建时间" prop="timeRange"  style="width:400px">
              <el-date-picker
                v-model="queryParams.confirmTimeList"
                type="datetimerange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                value-format="YYYY-MM-DD mm:hh:ss"
              />
            </el-form-item>

            <el-form-item label="店铺" prop="shopId">
              <el-select v-model="queryParams.shopIdList" clearable filterable placeholder="请选择店铺">
                <el-option v-for="item in shopList" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </el-form-item>

            <el-form-item label="订单类型" prop="OrderST" label-width="100">
              <el-select
                v-model="selectOrderST"
                clearable
                placeholder="请选择"
                style="width: 240px"
                @change="handleSelectStChange"
              >
                <el-option
                  v-for="item in orderSt"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>

              <el-form-item label="订单异常状态" prop="OrderEt" label-width="100">
                <el-select
                  v-model="selectOrderEt"
                  multiple
                  clearable
                  collapse-tags
                  placeholder="请选择"
                  popper-class="custom-header"
                  :max-collapse-tags="1"
                  style="width: 240px"
                  @change="handleSelectEtChange"
                >
                  <el-option
                    v-for="item in orderEt"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>

            <el-form-item label="订单状态" prop="depotIds">
              <el-select v-model="queryParams.orderStatus"  value-key="id" placeholder="请选择订单状态内容" :reserve-keyword="false"  clearable filterable :loading="loading">
                <el-option v-for="item in t_order_status" :key="item.value" :label="item.label"  :value="item.value"  />
              </el-select>
            </el-form-item>


            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['zhishu:shopOrder:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="View" :disabled="single" @click="handleUpdate()" v-hasPermi="['zhishu:shopOrder:edit']">查看</el-button>
          </el-col>

          <el-col :span="1.5">
            <el-button type="warning" plain icon="CollectionTag" :disabled="single" @click="btnSH">售后</el-button>
          </el-col>

          <el-col :span="1.5">
            <el-button type="primary" plain icon="Refresh" @click="genNewOrder" v-hasPermi="['zhishu:shopOrder:syncKfzHistoryOrder']" >重新同步订单</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>
<!--v-hasPermi="['zhishu:shopOrder:syncKfzHistoryOrder']"-->
      <el-table v-loading="loading"  :default-sort="{ prop: 'confirmTime', order: 'descending' }" style="height: 600px" :data="shopOrderList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="编号" align="center" prop="id" :show-overflow-tooltip="true" width="170">
          <template #default="{ row }">
            <div class="truncate-cell">  <!-- 文本截断容器 -->
              {{ row.id }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="店铺名称" align="center" prop="shopName" width="150px"/>
        <el-table-column label="三方平台编号" align="center" prop="orderSn" :show-overflow-tooltip="true" width="170">
          <template #default="{ row }">
            <div class="truncate-cell">  <!-- 文本截断容器 -->
              {{ row.orderSn }}
            </div>
          </template>
        </el-table-column>



        <el-table-column label="订单备注" align="left" prop="remark" width="130">
          <template #default="scope" >
            <!--            <el-icon :color="scope.row.remarkTag  === '1' ? 'red' : scope.row.remarkTag==='2'?'yellow'-->
            <!--                            :scope.row.remarkTag  === '3'?'green': scope.row.remarkTag==='4'?'blue'-->
            <!--                            :scope.row.remarkTag  === '5'?'purple':''"><Flag /></el-icon>-->

            <el-icon :color="getTagColor(scope.row.remarkTag)"><Flag /></el-icon>
            <span>{{scope.row.remark}}</span><br>
            <!--            标记名称:<span>{{scope.row.remarkTagName}}</span>-->
          </template>
        </el-table-column>


        <el-table-column label="平台商品详情" align="center" prop="orderSn" width="140px" :show-overflow-tooltip="true">
          <template #default="scope">
            <span @click="openDetailDialog(scope.row)">{{scope.row.orderItemList[0].goodsName}} </span>
          </template>
        </el-table-column>

<!--        <el-table-column label="分销商品详情" align="center" prop="orderSn" width="140px" :show-overflow-tooltip="true">-->
<!--          <template #default="scope">-->
<!--            <span @click="openDetailDialog(scope.row)">{{scope.row.orderItemList[0].goodsName}} </span>-->
<!--          </template>-->
<!--        </el-table-column>-->
        <el-table-column label="快递单号" align="center" prop="trackingNumber"/>

        <el-table-column label="订单状态" align="center" prop="orderStatus"  width="120px">
          <template #default="scope">
            <dict-tag :options="t_order_status" :value="scope.row.orderStatus" />
          </template>
        </el-table-column>
        <el-table-column label="售后状态" align="center" prop="afterSalesStatus" width="120px">
          <template #default="scope">
            <dict-tag :options="order_after_sales_status" :value="scope.row.afterSalesStatus" />
          </template>
        </el-table-column>
        <el-table-column label="成交状态"  align="center" prop="confirmStatus" width="120px">
          <template #default="scope">
            <dict-tag :options="t_confirm_status" :value="scope.row.confirmStatus" />
          </template>
        </el-table-column>
        <el-table-column label="成交时间" sortable align="center" prop="confirmTime" width="160px" />
        <el-table-column label="支付金额(元)" align="center" prop="payAmount" width="100px"/>
<!--        <el-table-column label="仓库金额(元)" align="center" prop="payAmount" width="100px"/>-->
<!--        <el-table-column label="利润(元)" align="center" prop="payAmount" width="100px"/>-->

        <el-table-column label="订单来源" align="center" prop="orderSourceType">
          <template #default="scope">
            <span v-if="scope.row.orderSourceType===1">拼多多</span>
            <span v-else-if="scope.row.orderSourceType===2">孔夫子</span>
          </template>
        </el-table-column>


        <el-table-column label="订单创建时间" align="center" prop="createdTime" width="150"/>




        <el-table-column label="订单类型" align="center" prop="tradeType">
          <template #default="scope">
           <span v-if="scope.row.tradeType===0">普通订单</span>
            <span v-else-if="scope.row.tradeType===1">定金订单</span>
          </template>
        </el-table-column>


        <el-table-column label="订单审核状态" align="center" prop="riskControlStatus" width="120px">
          <template #default="scope">
            <dict-tag :options="t_risk_control_status" :value="scope.row.riskControlStatus" />
          </template>
        </el-table-column>






<!--        <el-table-column label="货号" align="center" prop="artNo" width="150"/>-->


        <el-table-column label="买家留言信息" align="center" prop="buyerMemo" width="150px"/>

<!--        <el-table-column label="是否当日发货" align="center" prop="deliveryOneDay">-->
<!--          <template #default="scope">-->
<!--            <span v-if="scope.row.deliveryOneDay===0">否</span>-->
<!--            <span v-else-if="scope.row.deliveryOneDay===1">是</span>-->
<!--            <span v-else>未设置</span>-->
<!--          </template>-->
<!--        </el-table-column>-->


        <el-table-column label="折扣金额" align="center" prop="discountAmount"/>
<!--        <el-table-column label="多多支付立减金额" align="center" prop="duoDuoPayReduction"/>-->

<!--        <el-table-column label="是否多多批发" align="center" prop="duoduoWholesale"/>-->
        <el-table-column label="支付详情" align="left" prop="duoduoWholesale" width="200">
          <template #default="scope">
            单号: <span>{{scope.row.payNo}}</span><br>
            方式: <span>{{scope.row.payType}}</span><br>
            金额: <span>{{scope.row.payAmount}}</span><br>
            时间: <span>{{scope.row.payTime}}</span>
          </template>
        </el-table-column>
        <el-table-column label="平台优惠金额" align="center" prop="platformDiscount"/>
        <el-table-column label="邮费" align="center" prop="postage"/>
        <el-table-column label="预售时间" align="center" prop="preSaleTime"/>
        <el-table-column label="承诺送达时间" align="center" prop="promiseDeliveryTime"/>
<!--        <el-table-column label="确认收货时间" align="center" prop="receiverPhone"/>-->

        <el-table-column label="退货包运费" align="center" prop="returnFreightPayer">
          <template #default="scope">
            <span v-if="scope.row.returnFreightPayer===0">否</span>
            <span v-else-if="scope.row.returnFreightPayer===1">是</span>
            <span v-else>未设置</span>
          </template>
        </el-table-column>

<!--        <el-table-column label="是否门店自提" align="center" prop="selfContained"/>-->
<!--        <el-table-column label="商家优惠金额" align="center" prop="sellerDiscount"/>-->
<!--        <el-table-column label="缺货处理状态" align="center" prop="stockOutHandleStatus"/>-->

<!--        <el-table-column label="以旧换新国家补贴金额" align="center" prop="tradeInNationalSubsidyAmount"/>-->
        <el-table-column label="订单的更新时间" align="center" prop="updatedAt"/>
        <el-table-column label="催发货时间" align="center" prop="urgeShippingTime"/>
        <el-table-column label="预约配送日期" align="center" prop="yypsDate"/>
        <el-table-column label="预约配送时段" align="center" prop="yypsTime"/>



        <el-table-column label="仓库信息" align="left" prop="shopName" width="150px">
          <template #default="scope">
            名称：<span>{{scope.row.depotName}}</span>
            <br>
            类型：<span v-if="scope.row.depotType===1">自有仓</span>
            <span v-else-if="scope.row.depotType===2">订阅仓</span>
            <span v-else>订阅仓</span>
          </template>
        </el-table-column>








<!--        <el-table-column label="订单异常状态" align="center" prop="orderExceptionTypeList"  width="120px">-->
<!--          <template #default="{ row }">-->
<!--            &lt;!&ndash; 方案一：使用计算属性 &ndash;&gt;-->
<!--            <span v-for="label in getExceptionLabels(row.orderExceptionTypeList)" :key="label" class="mr-2">-->
<!--             {{ label }}-->
<!--            </span>-->
<!--          </template>-->
<!--        </el-table-column>-->


        <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="160px" fixed="right">
          <template #default="scope">
            <el-button type="primary" :disabled="scope.row.orderStatus!==1||displayStatus" @click="handleShipments(scope.row)">下发</el-button>
            <el-tooltip content="查看" placement="top">
              <el-button link type="primary" icon="View" @click="handleUpdate(scope.row)" v-hasPermi="['zhishu:shopOrder:edit']"></el-button>
            </el-tooltip>
             <el-tooltip content="售后" placement="top">
              <el-button link type="primary" icon="CollectionTag" @click="btnSH(scope.row)"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>
      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改订单对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="1000px" append-to-body>
      <el-form ref="shopOrderFormRef" :model="form" :rules="rules" label-width="160px">
        <el-row>
          <el-col :span="10">
            <el-form-item label="店铺名称" prop="shopName">
              <el-input v-model="form.shopName" placeholder="" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="10">
            <el-form-item label="订单编号" prop="orderSn">
              <el-input v-model="form.orderSn" placeholder="" disabled/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="10">
            <el-form-item label="收件人姓名" prop="receiverNameMask">
              <el-input v-model="form.receiverNameMask" placeholder="" disabled/>
            </el-form-item>
          </el-col>
          <el-col :span="10">
            <el-form-item label="详细地址" prop="addressMask">
              <el-input v-model="form.addressMask" placeholder="" disabled/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="10">
            <el-form-item label="支付单号" prop="payNo">
              <el-input v-model="form.payNo" placeholder="" disabled/>
            </el-form-item>
          </el-col>
          <el-col :span="10">
           <el-form-item label="支付金额(元)" prop="payAmount">
              <el-input v-model="form.payAmount" placeholder="" disabled/>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="10">
            <el-form-item label="支付时间" prop="payTime">
              <el-input v-model="form.payTime" placeholder="" disabled/>
            </el-form-item>
          </el-col>
          <el-col :span="10">
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="10">
            <el-form-item label="成交时间" prop="confirmTime">
              <el-input v-model="form.confirmTime" placeholder="" disabled/>
            </el-form-item>
          </el-col>
          <el-col :span="10">
            <el-form-item label="订单创建时间" prop="createdTime">
              <el-input v-model="form.createdTime" placeholder="" disabled/>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="10">
            <el-form-item label="是否当日发货" prop="deliveryOneDay">
              <el-input v-model="form.deliveryOneDay" placeholder="" disabled/>
            </el-form-item>
          </el-col>
          <el-col :span="10">
            <el-form-item label="折扣金额(元)" prop="discountAmount">
              <el-input v-model="form.discountAmount" placeholder="" disabled/>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="10">
            <el-form-item label="多多支付立减金额(元)" prop="duoDuoPayReduction">
              <el-input v-model="form.duoDuoPayReduction" placeholder="" disabled/>
            </el-form-item>
          </el-col>
          <el-col :span="10">
            <el-form-item label="是否多多批发" prop="duoduoWholesale">
              <el-input v-model="form.duoduoWholesale" placeholder="" disabled/>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="10">
            <el-form-item label="商品金额(元)" prop="goodsAmount">
              <el-input v-model="form.goodsAmount" placeholder="" disabled/>
            </el-form-item>
          </el-col>
          <el-col :span="10">
            <el-form-item label="支付申报订单号" prop="depotName">
              <el-input v-model="form.depotName" placeholder="" disabled/>
            </el-form-item>
          </el-col>
        </el-row>
         <el-row>
          <el-col :span="10">
            <el-form-item label="平台优惠金额(元)" prop="platformDiscount">
              <el-input v-model="form.platformDiscount" placeholder="" disabled/>
            </el-form-item>
          </el-col>
          <el-col :span="10">
            <el-form-item label="邮费" prop="postage">
              <el-input v-model="form.postage" placeholder="" disabled/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="10">
            <el-form-item label="预售时间" prop="preSaleTime">
              <el-input v-model="form.preSaleTime" placeholder="" disabled/>
            </el-form-item>
          </el-col>
          <el-col :span="10">
            <el-form-item label="预售时间" prop="preSaleTime">
              <el-input v-model="form.preSaleTime" placeholder="" disabled/>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="10">
            <el-form-item label="承诺送达时间" prop="promiseDeliveryTime">
              <el-input v-model="form.promiseDeliveryTime" placeholder="" disabled/>
            </el-form-item>
          </el-col>
          <el-col :span="10">

            <el-form-item label="确认收货时间" prop="receiveTime">
          <el-input v-model="form.receiveTime" placeholder="" disabled/>
        </el-form-item>
          </el-col>
        </el-row>


        <el-row>
          <el-col :span="10">
            <el-form-item label="省份编码" prop="provinceId">
              <el-input v-model="form.provinceId" placeholder="" disabled/>
            </el-form-item>
          </el-col>
          <el-col :span="10">
            <el-form-item label="省份" prop="province">
              <el-input v-model="form.province" placeholder="" disabled/>
            </el-form-item>

          </el-col>
        </el-row>
        <el-row>
          <el-col :span="10">
            <el-form-item label="订单备注" prop="remark">
              <el-input v-model="form.remark" placeholder="" disabled/>
          </el-form-item>
          </el-col>
          <el-col :span="10">
            <el-form-item label="退货包运费" prop="returnFreightPayer">
          <el-input v-model="form.returnFreightPayer" placeholder="" disabled/>
        </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="10">
             <el-form-item label="退货包运费" prop="returnFreightPayer">
              <el-input v-model="form.returnFreightPayer" placeholder="" disabled/>
            </el-form-item>
          </el-col>
          <el-col :span="10">

        <el-form-item label="是否门店自提" prop="selfContained">
          <el-input v-model="form.selfContained" placeholder="" disabled/>
        </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="10">
            <el-form-item label="商家优惠金额(元)" prop="sellerDiscount">
              <el-input v-model="form.sellerDiscount" placeholder="" disabled/>
            </el-form-item>
          </el-col>
          <el-col :span="10">

        <el-form-item label="全国联保" prop="supportNationwideWarranty">
          <el-input v-model="form.supportNationwideWarranty" placeholder="" disabled/>
        </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="10">
            <el-form-item label="区，乡镇" prop="town">
              <el-input v-model="form.town" placeholder="" disabled/>
            </el-form-item>
          </el-col>
          <el-col :span="10">
            <el-form-item label="区县编码" prop="townId">
              <el-input v-model="form.townId" placeholder="" disabled/>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="10">
            <el-form-item label="快递单号" prop="trackingNumber">
              <el-input v-model="form.trackingNumber" placeholder="" disabled/>
            </el-form-item>
          </el-col>
          <el-col :span="10">

        <el-form-item label="以旧换新补贴金额(元)" prop="tradeInNationalSubsidyAmount">
          <el-input v-model="form.tradeInNationalSubsidyAmount" placeholder="" disabled/>
        </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="10">
            <el-form-item label="订单的更新时间" prop="updatedAt">
              <el-input v-model="form.updatedAt" placeholder="" disabled/>
            </el-form-item>
          </el-col>
          <el-col :span="10">
            <el-form-item label="催发货时间" prop="urgeShippingTime">
              <el-input v-model="form.urgeShippingTime" placeholder="" disabled/>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="10">
            <el-form-item label="预约配送日期" prop="yypsDate">
              <el-input v-model="form.yypsDate" placeholder="" disabled/>
            </el-form-item>
          </el-col>
          <el-col :span="10">

        <el-form-item label="预约配送时段" prop="yypsTime">
          <el-input v-model="form.yypsTime" placeholder="" disabled/>
        </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="买家留言信息" prop="buyerMemo">
          <el-input v-model="form.buyerMemo" placeholder="" disabled/>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 售后弹框 -->
    <el-dialog :title="dialog.titleSH" v-model="dialog.visibleSH" width="800px" append-to-body >
      <div class="demo-collapse">
        <el-collapse v-model="activeNames" @change="handleChange">
          <el-collapse-item title="退款申请单" name="1">
            <div style="width:500px;margin-left:20px;margin-top:5px;float:left">
              订单编号: {{pidData.orderSn}}
              <br>
              商品名称: {{itemList.goodsName}}
              <br>
              商品价格: {{itemList.goodsPrice}}元
              <br>
              实际支付: {{pidData.payAmount}}元
              <br>
              商品数量: {{itemList.goodsCount}}
              <br>
              订单确认时间:{{pidData.updatedAt}}
              <br>
              退货包运费:{{pidData.returnFreightPayer == '0' ? '否' : '是'}}
            </div>
            <div style="width:150px;height:150px;float:left" @click="handleClick">
               <el-image
                  style="width: 100px; height: 100px"
                  :src="itemList.goodsImg"
                  :preview-src-list="srcList"
                  fit="cover"
                  show-progress
                >

                </el-image>

            </div>
          </el-collapse-item>

          <el-collapse-item title="物流轨迹" name="2">

          </el-collapse-item>

           <el-collapse-item title="售后状态" name="3">

          </el-collapse-item>

          <el-collapse-item title="协商详情" name="4">

          </el-collapse-item>

          <el-collapse-item title="聊天记录" name="5">

          </el-collapse-item>

          <el-collapse-item title="备注" name="6">

          </el-collapse-item>
        </el-collapse>
      </div>



      <template #footer>
        <div class="dialog-footer">
          <el-button type="success" disabled>同 意</el-button>
          <el-button type="danger" disabled>驳 回</el-button>
          <el-button type="warning" @click="handlePrint(true)">打 印</el-button>
        </div>
      </template>
    </el-dialog>



    <!--商品详细信息弹窗-->
    <el-dialog :title="dialogShop.title" v-model="dialogShop.visible" width="800px" append-to-body >
      <el-table v-loading="loading" :data="OrderItemList">
        <el-table-column label="货号" align="center" prop="artNo" />

        <el-table-column label="商品名称" align="center" prop="goodsName" />

        <el-table-column label="商品图片" align="center" prop="goodsImg" width="150px">
          <template #default="scope">
            <el-image
              :src="scope.row.goodsImg"
              :preview-src-list="[scope.row.goodsImg]"
              show-progress
              :preview-teleported="true"
              fit="cover"
            >
              <template #error>
                <div class="image-error">加载失败</div>
              </template>
            </el-image>
          </template>
        </el-table-column>
        <el-table-column label="商品价格" align="center" prop="goodsPrice"  width="120px"/>
        <el-table-column label="商品数量" align="center" prop="goodsCount"  width="120px"/>
      </el-table>
    </el-dialog>



    <!--发货信息弹窗-->
      <el-dialog :title="dialogShipments.title" v-model="dialogShipments.visible" width="800px" append-to-body>
        <el-form ref="formRef" :model="formShops" :rules="rulesShops" label-width="90px" style="height:170px">
          <el-form-item label="快递方式" prop="methodId">
            <el-select
            v-model="formShops.methodId"
            filterable
            placeholder="请选择快递"
            style="width: 240px"
            @change="shippingChage"
          >
            <el-option
              v-for="item in shiMethodList"
              :key="item.methodId"
             :label="item.methodName"
             :value="item.methodId"
            />
          </el-select>
          </el-form-item>
           <el-form-item  v-show="show" label="快递单号1" prop="trackingNumber">
            <el-input v-model="formShops.trackingNumber" />
          </el-form-item>
            <el-form-item v-show="!show" label="快递单号" prop="trackingNumber11">
            <el-input v-model="formShops.trackingNumber" :disabled="!show"/>
          </el-form-item>

          <el-form-item label="自定义" prop="userDefined">
            <el-input v-model="formShops.userDefined" :disabled="showCustomInput" />
          </el-form-item>
          <el-form-item label="多个运单号" prop="moreShipmentNum">
            <el-input v-model="formShops.moreShipmentNum" :disabled="formShops.orderSourceType!==2||!show" />
          </el-form-item>
        </el-form>

        <template #footer>
          <div class="dialog-footer">
            <el-button  type="primary" @click="shipmentSubmit">确 定</el-button>
            <el-button @click="cancel">取 消</el-button>
          </div>
        </template>
      </el-dialog>

    <!--重新同步订单弹窗-->
    <el-dialog :title="dialog.newOrderTitle" v-model="dialog.newOrderVisible" @close="handleDialogClose" width="800px" append-to-body style="height: 400px;">
      <el-form ref="formRef" :model="formShops" :rules="rulesShops" label-width="90px" style="height:170px">
        <el-form-item label="店铺选择" prop="shopId" label-width="100" >
          <el-form-item label="孔夫子:" prop="shopIds" label-width="100px" style="width: 100%">
            <!-- 孔夫子（整行） -->
            <el-select v-model="pddShopData"  multiple clearable placeholder="请选择孔夫子店铺" style="width: 200px">
              <el-option v-for="item in kfzShopList" :key="item.value" :label="item.label" :value="item.value"/>
            </el-select>
          </el-form-item>
          <el-form-item label="拼多多:" prop="shopIds" label-width="100px" style="width: 100%;margin-top: 10px;" >
            <!-- 拼多多（整行） -->
            <el-select v-model="kfzShopData"  multiple clearable placeholder="请选择拼多多店铺" style="width: 200px">
              <el-option v-for="item in pddShopList" :key="item.value" :label="item.label" :value="item.value"/>
            </el-select>
          </el-form-item>
        </el-form-item>
        <el-form-item label="同步历史订单天数" prop="shopId" label-width="130" style="margin-top: 10px">
          <el-select v-model="numbersDay"  clearable placeholder="同步天数" style="width: 200px">
            <el-option v-for="item in dayNumbers" :key="item.value" :label="item.label" :value="item.value"/>
          </el-select>
        </el-form-item>
      </el-form>
      <div style="text-align: center;">
        <el-button @click="startSy" style="font-size: 20px;margin-top: 40px">开始同步</el-button>
      </div>
    </el-dialog>
    <el-dialog  v-model="dialog.startSyVisible" width="600px" append-to-body>
      <div>
           <span style="display: block; text-align: center; color: red; font-size: 20px">
             注意：重新同步订单会删除掉历史订单
          </span>
      </div>
      <div class="dialog-footer" style="text-align: center; margin-top: 40px">
          <el-button  type="primary" @click="orderSySubmit" style="margin-right: 30px;">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
    </el-dialog>


  </div>
</template>

<script setup name="ShopOrder" lang="ts">
import {
  listShopOrder,
  getShopOrder,
  delShopOrder,
  addShopOrder,
  updateShopOrder, getShopShi, ShopShipments,
  checkInfo, syncKfzHistoryOrder
} from '@/api/zhishu/shopOrder';
import {
  ShopOrderVO,
  ShopOrderQuery,
  ShopOrderForm,
  OrderItem,
  ShippingMethod,
  ShopsShipments, ShopListIds
} from '@/api/zhishu/shopOrder/types';
import { getListShop } from '@/api/zhishu/shop';
import { createPrintTask } from '@/api/zhishu/daYin';
import { ref } from 'vue';
import { DepotVO } from '@/api/zhishu/depot/types';
import { ShopGoodsForm } from '@/api/zhishu/shopGoods/types';
import { BookAuditForm } from '@/api/zhishu/bookAudit/types';
import { ElMessage, FormInstance } from 'element-plus';
import { addBaseInfo, updateBaseInfo } from '@/api/zhishu/baseInfo';
const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const {
  t_order_status,
  order_after_sales_status,
  t_confirm_status,
  t_risk_control_status,
  t_yes_no
} = toRefs<any>(
  proxy?.useDict(
    't_order_status',
    'order_after_sales_status',
    't_confirm_status',
    't_risk_control_status',
    't_yes_no'
  )
);



const shopOrderList = ref<ShopOrderVO[]>([]) || null;

const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const shopOrderFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});




const initFormData: ShopOrderForm = {
  id: undefined,
  shopId: undefined,
  shopName: undefined,
  address: undefined,
  addressMask: undefined,
  afterSalesStatus: undefined,
  buyerMemo: undefined,
  confirmStatus: undefined,
  confirmTime: undefined,
  createdTime: undefined,
  deliveryOneDay: undefined,
  discountAmount: undefined,
  duoDuoPayReduction: undefined,
  duoduoWholesale: undefined,
  goodsAmount: undefined,
  depotCode: undefined,
  depotId: undefined,
  depotName: undefined,
  depotType: undefined,
  wareId: undefined,
  wareName: undefined,
  wareSn: undefined,
  wareType: undefined,
  orderSn: undefined,
  orderStatus: undefined,
  payAmount: undefined,
  payNo: undefined,
  payTime: undefined,
  payType: undefined,
  platformDiscount: undefined,
  postage: undefined,
  preSaleTime: undefined,
  promiseDeliveryTime: undefined,
  province: undefined,
  provinceId: undefined,
  receiveTime: undefined,
  receiverAddress: undefined,
  receiverAddressMask: undefined,
  receiverName: undefined,
  receiverNameMask: undefined,
  receiverPhone: undefined,
  receiverPhoneMask: undefined,
  refundStatus: undefined,
  remark: undefined,
  remarkTag: undefined,
  remarkTagName: undefined,
  returnFreightPayer: undefined,
  riskControlStatus: undefined,
  selfContained: undefined,
  sellerDiscount: undefined,
  stockOutHandleStatus: undefined,
  supportNationwideWarranty: undefined,
  town: undefined,
  townId: undefined,
  trackingNumber: undefined,
  tradeInNationalSubsidyAmount: undefined,
  tradeType: undefined,
  updatedAt: undefined,
  urgeShippingTime: undefined,
  yypsDate: undefined,
  yypsTime: undefined,
  openAddressId2: undefined,
  status: undefined,
  orderExceptionTypeList:undefined,
  orderSourceType:undefined,
  orderItemList:undefined,
  artNo:undefined,
}
const data = reactive<PageData<ShopOrderForm, ShopOrderQuery>>({
  form: {...initFormData},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    shopId: undefined,
    shopName: undefined,
    address: undefined,
    addressMask: undefined,
    afterSalesStatus: undefined,
    buyerMemo: undefined,
    confirmStatus: undefined,
    confirmTime: undefined,
    createdTime: undefined,
    deliveryOneDay: undefined,
    discountAmount: undefined,
    duoDuoPayReduction: undefined,
    duoduoWholesale: undefined,
    goodsAmount: undefined,
    depotCode: undefined,
    depotId: undefined,
    depotName: undefined,
    depotType: undefined,
    wareId: undefined,
    wareName: undefined,
    wareSn: undefined,
    wareType: undefined,
    orderSn: undefined,
    orderStatus: undefined,
    payAmount: undefined,
    payNo: undefined,
    payTime: undefined,
    payType: undefined,
    platformDiscount: undefined,
    postage: undefined,
    preSaleTime: undefined,
    promiseDeliveryTime: undefined,
    province: undefined,
    provinceId: undefined,
    receiveTime: undefined,
    receiverAddress: undefined,
    receiverAddressMask: undefined,
    receiverName: undefined,
    receiverNameMask: undefined,
    receiverPhone: undefined,
    receiverPhoneMask: undefined,
    refundStatus: undefined,
    remarkTag: undefined,
    remarkTagName: undefined,
    returnFreightPayer: undefined,
    riskControlStatus: undefined,
    selfContained: undefined,
    sellerDiscount: undefined,
    stockOutHandleStatus: undefined,
    supportNationwideWarranty: undefined,
    town: undefined,
    townId: undefined,
    trackingNumber: undefined,
    tradeInNationalSubsidyAmount: undefined,
    tradeType: undefined,
    updatedAt: undefined,
    urgeShippingTime: undefined,
    yypsDate: undefined,
    yypsTime: undefined,
    openAddressId2: undefined,
    status: undefined,
    orderExceptionTypeList:undefined,
    orderExceptionType:undefined,
    shopIdList:undefined,
    params: {
    },
    confirmTimeList:undefined,
    createdTimeStart:undefined,
    createdTimeEnd:undefined,

  },
  rules: {

  }
});

const { queryParams, form, rules } = toRefs(data);


const OrderItemList = ref<OrderItem>();


const checkAll = ref(false)
const indeterminate = ref(false)
const selectOrderEt =ref([])
const selectOrderST=ref()
const shopList = [];

const colorOptions = [
  { value: '1', label: 'red' },
  { value: '2', label: 'yellow' },
  { value: '3', label: 'green' },
  { value: '4', label: 'blue' },
  { value: '5', label: 'purple' }
];
const getTagColor = (tag) => {
  return colorOptions.find(item  => item.value  === String(tag))?.label || 'gray';
};


// 订单类型枚举
const orderSt=[
  { value: '1', label: '拼多多' },
  { value: '2', label: '孔夫子' }
]
// 订单状态选中的
const handleSelectStChange=async =>{
  queryParams.value.orderSourceType=selectOrderST.value;
}

// 订单异常类型枚举
const orderEt = [
  { value: 'inventoryException', label: '库存异常' },
  { value: 'goodsSourceUnknownException', label: '未知商品来源' }
];

const handleSelectEtChange=async =>{
  queryParams.value.orderExceptionTypeList=selectOrderEt.value;
}
// 获取店铺列表
// const getShopList = async () => {
//   const res = await getListShop();
//   if (res.length == 0) {
//     shopList.length = 0;
//   } else {
//     for (var i = 0; i < res.length; i++) {
//       shopList.push({
//         value: res[i].id,
//         label: res[i].shopName,
//          type: res[i].shopType
//       });
//     }
//   }
// }



/** 获取店铺信息 */
const pddShopList=[];
const kfzShopList=[];
const getShopList = async () => {
  const res = await getListShop();
  console.log(res)
  if (res.length == 0) {
    shopList.length = 0;
  } else {
    for (var i = 0; i < res.length; i++) {
      shopList.push({
        value: res[i].id,
        label: res[i].shopName
      });
     if(res[i].shopType==="1"){
        pddShopList.push(
          {
            value: res[i].id,
            label: res[i].shopName,
          }
        )
      }else if(res[i].shopType==="2"){
        kfzShopList.push(
          {
            value: res[i].id,
            label: res[i].shopName,
          }
        )
      }
    }
  }
}


const shopIdData=ref([]);
const pddShopData=ref([]);
const kfzShopData=ref([]);
const numbersDay=ref("30");
// 同步天数枚举
const dayNumbers=[
  { value: '30', label: '一个月' },
  { value: '60', label: '两个月' },
  { value: '90', label: '三个月' }
]


// 重新同步订单
const genNewOrder=async ()=>{
  dialog.newOrderTitle="重新同步订单信息"
  dialog.newOrderVisible=true;
}
// 提示信息
const startSy=async ()=>{
  shopIdData.value = [...pddShopData.value, ...kfzShopData.value];
  // 验证是否选择了店铺
  if(shopIdData.value.length === 0) {
    ElMessage.warning('请至少选择一个店铺')
    return
  }
  // 验证同步天数（更严谨的写法）
  if(!numbersDay.value || !dayNumbers.some(item => item.value === numbersDay.value)) {
    ElMessage.warning('请选择有效的同步天数')
    return false
  }

  dialog.startSyVisible = true;
  dialog.newOrderVisible = false;
}


const shopLists = reactive<ShopListIds>({
  shopIdList:undefined
});

// await updateBaseInfo(form.value).finally(() =>  buttonLoading.value = false);




//重新同步订单
const orderSySubmit=async ()=>{
  shopLists.shopIdList=shopIdData.value;
  const res = await syncKfzHistoryOrder(numbersDay.value,shopLists.shopIdList).finally(() =>  buttonLoading.value = false);
  if(res.data){
    proxy?.$modal.msgSuccess("操作成功");
  }else{
    proxy?.$modal.msgError("操作失败");
  }
  dialog.startSyVisible=false;
  await getList();
}

const handleDialogClose=()=>{
  pddShopData.value=[];
  kfzShopData.value=[];
  shopLists.shopIdList=null;
}



// 商品详情弹窗
const dialogShop = reactive<DialogOption>({
  visible: false,
  title: '',
  goodsList: [] as OrderItem[] // 存储弹窗表格数据
});


// 发货弹窗
const dialogShipments = reactive<DialogOption>({
  visible: false,
  title: '',
  // goodsList: [] as OrderItem[] // 存储弹窗表格数据
});


// 弹出商品详细信息
const openDetailDialog=async (row)=>{
  dialogShop.title="商品详细信息";
  dialogShop.visible=true;
  OrderItemList.value=row.orderItemList;
  console.log(OrderItemList.value)
}

onMounted(() => {
  // console.log(queryParams.value.)
  // queryParams.value.order_status = '5'
  // queryParams.value.refund_status = '5'
});

/** 查询订单列表 */
const getList = async () => {
  loading.value = true;

  if(queryParams.value.confirmTimeList){
    queryParams.value.createdTimeStart = queryParams.value.confirmTimeList[0]
    queryParams.value.createdTimeEnd = queryParams.value.confirmTimeList[1]
  }

  const res = await listShopOrder(queryParams.value);
  shopOrderList.value = res.rows;


  total.value = res.total;
  loading.value = false;
}

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.startSyVisible=false;
  dialog.visible = false;
  dialogShipments.visible=false;
}

/** 表单重置 */
const reset = () => {
  form.value = {...initFormData};
  shopOrderFormRef.value?.resetFields();
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
const resetQuery = () => {

  selectOrderEt.value=[];
  selectOrderST.value=null;
  queryParams.value.orderStatus=null;
  queryParams.value.orderSourceType=null;
  queryParams.value.orderExceptionTypeList=null;
  queryParams.value.confirmTimeList=null;
  queryParams.value.createdTimeStart=null
  queryParams.value.createdTimeEnd=null;
  queryFormRef.value?.resetFields();
  handleQuery();
}

/** 多选框选中数据 */
const handleSelectionChange = (selection: ShopOrderVO[]) => {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = "添加订单";
}

/** 售后按钮操作 */
let itemList;
let pidData;
const btnSH = async (row) => {
  const _id = row?.id || ids.value[0]
  const res = await getShopOrder(_id);
  const data = res.data;

  itemList = undefined;
  pidData = undefined;
  pidData = data;

  itemList = JSON.parse(pidData.itemList);

  itemList = itemList.orderItems[0];
  dialog.visibleSH = true;
  dialog.titleSH = "订单售后";
}

/** 修改按钮操作 */
const handleUpdate = async (row?: ShopOrderVO) => {
  reset();
  const _id = row?.id || ids.value[0]
  const res = await getShopOrder(_id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = "查看订单详情";
}

/** 提交按钮 */
const submitForm = () => {
  shopOrderFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateShopOrder(form.value).finally(() =>  buttonLoading.value = false);
      } else {
        await addShopOrder(form.value).finally(() =>  buttonLoading.value = false);
      }
      proxy?.$modal.msgSuccess("操作成功");
      dialog.visible = false;
      await getList();
    }
  });
}




const formShops = reactive<ShopsShipments>({
  orderSourceType:undefined,
  shopId: undefined,
  orderSn: undefined,
  methodId: undefined,
  trackingNumber: undefined,
  userDefined:undefined,
  moreShipmentNum:undefined,
})



const shiMethodList=ref<ShippingMethod[]>([]);

const showCustomInput=ref(true);
const show=ref(true);
const  shippingChage=async (row)=>{
  if(row.endsWith('other')){
    showCustomInput.value=false;
    show.value=true;
    console.log(show.value)
  }else if(row==="noLogistics"){
    showCustomInput.value=true;
    show.value=false;
    formShops.trackingNumber=undefined;
    formShops.userDefined=undefined;
    formShops.moreShipmentNum=undefined;
    console.log("无需",show.value)
  }else{
    showCustomInput.value=true;
    show.value=true;
    formShops.userDefined=undefined;
  }
  formShops.methodId=row;
}

const rulesShops=reactive({
    methodId:[
      { required: true, message: '货区名称不能为空', trigger: ['blur','change'] },
    ],
    trackingNumber:[
      { required: true, message: '快递单号不能为空', trigger: 'blur' },
      { min:8, max: 20, message: '快递单号在8到20位之间', trigger: 'blur' },
      {
        pattern: /^[A-Za-z0-9]+$/,
        message: '仅允许字母或数字组合',
        trigger: 'blur'
      }
    ]
}
)

const displayStatus=ref(false);

//下发
const handleShipments=async (row)=>{
  console.log(row,"========---------===========")
  await checkInfo(row.id);
  // try {
  //   const res= await  getShopShi(row.shopId,row.orderSourceType);
  //   shiMethodList.value=res.data;
  //   formShops.orderSourceType=row.orderSourceType;
  //   formShops.shopId=row.shopId;
  //   formShops.orderSn=row.orderSn;
  //   dialogShipments.title="设置发货信息"
  //   dialogShipments.visible=true;
  // } catch (error) {
  //   console.error('数据加载失败:', error);
  // }
}


// 表单引用（关键！）
const formRef = ref<FormInstance>();

// 提交逻辑
const shipmentSubmit = async () => {
  if (!formRef.value) return;
  displayStatus.value=true;
  try {
    await formRef.value.validate(); // 调用表单验证
    buttonLoading.value = true;
    console.log("form:",formShops)
    const mes=await ShopShipments(formShops)
    if(mes.code==200){
      ElMessage.success(mes.msg);
    }else{
      ElMessage.error("操作失败：",mes.msg);
    }
    dialogShipments.visible = false;
    getList();
  } catch (error) {
    console.log('验证失败:', error);
    dialogShipments.visible = false;
  }
 const timer= setTimeout(() => {
    getList()
   displayStatus.value=false;
 }, 12000);
  // 组件销毁时清除定时器
  onBeforeUnmount(() => clearTimeout(timer));
};




/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download('zhishu/shopOrder/export', {
    ...queryParams.value
  }, `shopOrder_${new Date().getTime()}.xlsx`)
}

/** 打印 */
// 打印队列
const printQueue = ref<any[]>([])
//
const isPrinting = ref(false)

// 调用示例
const printParams = {
  topRightStr:"代收贷款",
  title:"这里是大头笔测试",
  priceOne:"2400",
  priceTwo:"24",
  orderTime:"2025-05-26 13:39:45",
  sender: {
    name: "张三",
    phone: "13800138000",
    address: "上海市浦东新区韵达总部大厦1号楼"
  },
  receiver: {
    name: "李四",
    phone: "13900139000",
    address: "北京市海淀区中关村大街1号院5号楼"
  },
  goods:{
    name:"秋水无尘",
    num:"1",
    courierFee:"5"
  },
  moStr:"这里是末",
  jiStr:"这里是集",
  trackingNumber:"4300123456789",
  pageNum:"1",
  totalNum:"1"
}

const handlePrint = async (preview = true) => {

  try {
    const LODOP = await createPrintTask('yunda',printParams)
    if (preview) {
      LODOP.PREVIEW()
    } else {
      LODOP.PRINT()
    }
  } catch (error) {
    alert(error.message);
  }
}

onMounted(() => {
  getList();
  getShopList();
});
</script>

<style>
/* 文本溢出处理 */
.truncate-cell {
  width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>

