<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="支付方式 " prop="rechargType">
              <el-select v-model="queryParams.rechargType" placeholder="请选择支付方式" clearable>
                <el-option v-for="dict in t_recharge_way" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="充值金额" prop="rechargPrice">
              <el-input v-model="queryParams.rechargPrice" placeholder="请输入充值金额" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="创建时间" style="width: 308px">
                  <el-date-picker
                    v-model="dateRange"
                    value-format="YYYY-MM-DD HH:mm:ss"
                    type="daterange"
                    range-separator="-"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]"
                  ></el-date-picker>
                </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <span style="font-size:20px">余额 : {{ Number(balance) / 100 }} 元</span>
            <el-button style="margin-left:10px" type="primary" plain icon="Upload" @click="handleAdd" v-hasPermi="['zhishu:userRecharge:add']">充值</el-button>
          </el-col>

          <el-col :span="1.5">
            <el-button style="margin-left:10px" type="warning" plain icon="Download"  v-hasPermi="['zhishu:userRecharge:add']">提现</el-button>
          </el-col>
          
          <!-- <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['zhishu:userRecharge:export']">导出</el-button>
          </el-col> -->
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="userRechargeList" @selection-change="handleSelectionChange">
        <el-table-column type="expand">
          <template #default="props">

            
            <div m="4">
              <p m="t-0 b-2">支付号:{{ props.row.allDataStr != undefined ? JSON.parse(props.row.allDataStr).out_trade_no : ''}} </p>
            </div>
            <div m="4">
              <p m="t-0 b-2">流水号:{{ props.row.allDataStr != undefined ? JSON.parse(props.row.allDataStr).transaction_id : ''}} </p>
            </div>
            <div m="4">
              <p m="t-0 b-2">商户号:{{ props.row.allDataStr != undefined ? JSON.parse(props.row.allDataStr).mchid : ''}} </p>
            </div>
            <div m="4">
              <p m="t-0 b-2">支付状态:{{ props.row.allDataStr != undefined ? JSON.parse(props.row.allDataStr).trade_state_desc : ''}} </p>
            </div>
            <div m="4">
              <p m="t-0 b-2">银行类型:{{ props.row.allDataStr != undefined ? JSON.parse(props.row.allDataStr).bank_type : ''}} </p>
            </div>
            <div m="4">
              <p m="t-0 b-2">支付时间:{{ props.row.allDataStr != undefined ? parseTime(JSON.parse(props.row.allDataStr).success_time,'{y}-{m}-{d} {h}:{i}:{s}') : ''}} </p>
            </div>
            <div m="4">
              <p m="t-0 b-2">支付人:{{ props.row.allDataStr != undefined ? JSON.parse(props.row.allDataStr).payer.openid : ''}} </p>
            </div>
            <div m="4">
              <p m="t-0 b-2">应支付金额:{{ props.row.allDataStr != undefined ? (Number(JSON.parse(props.row.allDataStr).amount.total) / 100) + ' ' + JSON.parse(props.row.allDataStr).amount.currency : ''}} </p>
            </div>
            <div m="4">
              <p m="t-0 b-2">实际支付金额:{{ props.row.allDataStr != undefined ? (Number(JSON.parse(props.row.allDataStr).amount.payer_total) / 100) + ' ' + JSON.parse(props.row.allDataStr).amount.payer_currency : ''}} </p>
            </div>
          </template>
        </el-table-column>  
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="平台流水号" align="center" prop="id" v-if="true" />
        <el-table-column label="支付流水号" align="center" prop="wxId" v-if="true" />
        <el-table-column label="充值方式" align="center" prop="rechargType" width="150px">
          <template #default="scope">
            <dict-tag :options="t_recharge_way" :value="scope.row.rechargType" />
          </template>
        </el-table-column>

        <el-table-column label="充值金额" align="center" width="150px">
          <template #default="scope">
            {{ scope.row.rechargPrice / 100 }}
          </template>
        </el-table-column>

        <el-table-column label="手续费" align="center" width="150px">
          <template #default="scope">
            {{ scope.row.commission / 100 }}
          </template>
        </el-table-column>

        <el-table-column label="支付状态" align="center" prop="status" width="150px">
          <template #default="scope">
            <dict-tag :options="t_recharge_status" :value="scope.row.status" />
          </template>
        </el-table-column>

        <el-table-column label="创建时间" align="center" prop="createTime" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
          </template>
        </el-table-column>

        <el-table-column label="支付时间" align="center" prop="successTime" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.successTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改充值对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body>
      <el-form ref="userRechargeFormRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="支付方式" prop="rechargType">
          <el-select v-model="form.rechargType" placeholder="请选择支付方式">
            <el-option v-for="dict in t_recharge_way" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item :class="{ isHide: form.rechargType == undefined }" label="充值金额" prop="rechargPrice">
          <el-input v-model="form.rechargPrice" type="number" :min="minRecharge" @input="handleInput" placeholder="请输入充值金额" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <el-dialog :title="dialog.titlePrcode" v-model="dialog.visiblePrcode" width="360px" append-to-body>
      <div>
        <img :src="qrCodeText" alt="Base64 图片" />
      </div>
    </el-dialog>
  </div>
</template>

<script setup name="UserRecharge" lang="ts">
import { getInfo} from '@/api/login';

import { listUserRecharge, getUserRecharge, delUserRecharge, addUserRecharge, updateUserRecharge,userRecharge,checkTask,editRechargeToError,getConfigKey } from '@/api/zhishu/userRecharge';
import { UserRechargeVO, UserRechargeQuery, UserRechargeForm } from '@/api/zhishu/userRecharge/types';



const dateRange = ref<[DateModelType, DateModelType]>(['', '']);
const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const {
  t_recharge_way,
  t_recharge_status,
} = toRefs<any>(
  proxy?.useDict(
    't_recharge_way',
    't_recharge_status'
  )
);




const userRechargeList = ref<UserRechargeVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const minRecharge = ref();

const queryFormRef = ref<ElFormInstance>();
const userRechargeFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: UserRechargeForm = {
  id: undefined,
  wxId: undefined,
  userId: undefined,
  rechargType: undefined,
  rechargPrice: undefined,
  successTime: undefined,
  createTime:undefined,
  allDataStr: undefined,
  status: undefined,
  commission:undefined
}
const data = reactive<PageData<UserRechargeForm, UserRechargeQuery>>({
  form: {...initFormData},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    userId: undefined,
    rechargType: undefined,
    rechargPrice: undefined,
    successTime: undefined,
    allDataStr: undefined,
    status: undefined,
    commission:undefined,
    params: {
    }
  },
  rules: {
    id: [
      { required: true, message: "主键不能为空", trigger: "blur" }
    ],
  }
});

const { queryParams, form, rules } = toRefs(data);
const qrCodeText = ref('');

/** 查询充值列表 */
const getList = async () => {
  loading.value = true;
  const res = await listUserRecharge(proxy?.addDateRange(queryParams.value, dateRange.value));
  userRechargeList.value = res.rows;
  for(var i=0;i<userRechargeList.value.length;i++){
    const jsonObject = JSON.parse(userRechargeList.value[i].allDataStr);
    if(jsonObject){
      userRechargeList.value[i].wxId = jsonObject.transaction_id;
    }

  }
  total.value = res.total;
  loading.value = false;
}

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
}

/** 表单重置 */
const reset = () => {
  form.value = {...initFormData};
  userRechargeFormRef.value?.resetFields();
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
const resetQuery = () => {
  dateRange.value = ['', ''];
  queryFormRef.value?.resetFields();
  handleQuery();
}

/** 多选框选中数据 */
const handleSelectionChange = (selection: UserRechargeVO[]) => {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = "充值";
  buttonLoading.value = false;
}


/** 提交按钮 */
const submitForm = () => {
  if(form.value.rechargPrice == undefined){
    proxy?.$modal.msgError("请填写充值金额");
    return;
  }
  userRechargeFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;

      const res = await userRecharge(form.value);

      if(res.code == 200){
        qrCodeText.value = "data:image/png;base64,"+res.data.img;
        dialog.visiblePrcode = true;
        dialog.titlePrcode = "支付";
        //定时器 2分钟持续获取 支付单据状态
        let count = 0;
        const maxCount = 40; // 两分钟内，每隔三秒打印一次，总共打印40次
        let intervalId = setInterval( async () => {
          if (count < maxCount) {
            const status = await checkTask(res.data.id);
            if(status == '1'){
              count = 80;
            }
            count++;

          } else {
            if(count == 40){
              editRechargeToError(res.data.id);
            }
            dialog.visible = false;
            dialog.visiblePrcode = false;
            qrCodeText.value = undefined;
            buttonLoading.value = false;
            getList();
            getUser();
            clearInterval(intervalId); // 停止定时器
          }
        }, 3000);
      }
    }
  });
}



/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download('zhishu/userRecharge/export', {
    ...queryParams.value
  }, `userRecharge_${new Date().getTime()}.xlsx`)
}

 // 处理输入事件
const handleInput = () => {
    let value = form.value.rechargPrice;
    // 如果输入值小于最小值，直接设置为最小值
    if (value < Number(minRecharge.value)) {
      value = minRecharge.value;
    }
    // 限制小数点后两位
    const regex = /^\d*\.?\d{0,2}$/;
    if (!regex.test(value)) {
      // 如果不符合规则，截取前两位小数
      const parts = value.split('.');
      if (parts.length > 1) {
        value = `${parts[0]}.${parts[1].slice(0, 2)}`;
      }
    }
    // 更新输入框的值
    form.value.rechargPrice = value;
  };

const getMin = async () => {
  const res = await getConfigKey('recharge.min');
  minRecharge.value = res.msg
}

const balance = ref('');
const freeze = ref('');

const getUser = async () => {
    const res = await getInfo();
    balance.value = res.data.user.balance;
    freeze.value = res.data.user.freeze;
}

onMounted(() => {
  getList();
  getMin();
  getUser();
});
</script>

<style scoped>
.isHide {
  display: none;
}
</style>
