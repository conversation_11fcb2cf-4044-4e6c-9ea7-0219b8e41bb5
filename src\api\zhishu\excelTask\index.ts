import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import exp from 'constants';
import { ExcelTaskForm, ExcelTaskQuery, ExcelTaskVO } from '@/api/zhishu/excelTask/types';

/**
 * 查询任务列表列表
 * @param query
 * @returns {*}
 */

export const excelListTask = (query?: ExcelTaskQuery): AxiosPromise<ExcelTaskVO[]> => {
  return request({
    url: '/zhishu/excelTask/list',
    method: 'get',
    params: query
  });
};

export const excelLogsTask = (id) =>{
  return request({
    url: '/zhishu/excelTask/logsList',
    method: 'get',
    params: {
      "id":id
    }
  });
}

export const excelLogsMsg = (id) =>{
  return request({
    url: '/zhishu/excelTask/logsMsg',
    method: 'get',
    params: {
      "id":id
    }
  });
}

export const excelLogsDetailTask = (data) => {
  return request({
    url: '/zhishu/excelTask/logsDetailList/'+data.taskId+"/"+data.shopId,
    method: 'get'
  });
}

export const excelDownloadLog = (fileName) => {
  return request({
    url: '/zhishu/excelTask/downloadLogs/'+fileName,
    method: 'get'
  });
}
/**
 * 暂停线程
 */
export const excelPauseThread = (threadId,id) =>{
  return request({
    url: '/zhishu/excelTask/pauseThread/'+threadId+"/"+id,
    method: 'get'
  });
}

/**
 * 恢复线程
 */
export const excelContinueThread = (threadId,id) =>{
  return request({
    url: '/zhishu/excelTask/continueThread/'+threadId+"/"+id,
    method: 'get'
  });
}

/**
 * 查询任务列表详细
 * @param id
 */
export const excelGetTask = (id: string | number): AxiosPromise<ExcelTaskVO> => {
  return request({
    url: '/zhishu/excelTask/' + id,
    method: 'get'
  });
};

/**
 * 新增任务列表
 * @param data
 */
export const excelAddTask = (data) => {
  return request({
    url: '/zhishu/excelTask',
    method: 'post',
    data: data
  });
};

/**
 * 修改任务列表
 * @param data
 */
export const excelUpdateTask = (data: ExcelTaskForm) => {
  return request({
    url: '/zhishu/excelTask',
    method: 'put',
    data: data
  });
};

/**
 * 删除任务列表
 * @param id
 */
export const excelDelTask = (id: string | number | Array<string | number>) => {
  return request({
    url: '/zhishu/excelTask/' + id,
    method: 'delete'
  });
};
