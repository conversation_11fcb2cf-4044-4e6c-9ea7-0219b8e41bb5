export interface ShopGoodsVO {
  /**
   * id
   */
  id: string | number;

  /**
   * 商品名称
   */
  goodsName: string;

  /**
   * 产品编码
   */
  productId: string;

  /**
   * 仓库编号
   */
  depotId: string

  /**
   * 商品名称
   */
  specifications: string;

  /**
   * isbn
   */
  isbn: string

  /**
   * 货号
   */
  artNo: string;

  /**
   * 期初库存
   */
  openingInventory: number;





  /**
   * 标准售价
   */
  price: number;

  /**
   * 品相
   */
  conditionCode: string;


  /**
   * 单位
   */
  unit: string;

  /**
   * 备注
   */
  remark: string;



  /**
   * 图片
   */
  bookPic:string;

  /**
   * 仓库名称
   */
  depotName: string;
  /**
   * 商品定价
   */
  fixPrice: number;





  // 新增数据
  /**
   * 包册数
   */
  booksNumber:number;
  /**
   * 页数
   */
  pages:number;

  /**
   * 是否是套餐（0套餐 1非套餐）
   */
  isPackage:number;

  /**
   * 出版社名称
   */
  publicationName:string;

  /**
   * 出版时间
   */
  publicationTime:string;

  /**
   * 作者
   */
  author:string;

  /**
   * 地区
   */
  region:string;

  /**
   * 译者
   */
  translator:string;

  /**
   * 编者
   */
  editor:string;

  /**
   * 书籍开本
   */
  booklets:string;

  /**
   * 字数
   */
  wordCount:number;

  /**
   * 装帧类型
   */
  bindType:string;

  /**
   * 包装方式（0礼盒装 1普通包装）
   */
  packing:number;

  /**
   * 模版名称
   */
  templateName:string;

  /**
   * 原始货号
   */
  originalArtNo:string;

  /**
   * 最小价格
   */
  minprice:number;

  /**
   * 最大价格
   */
  maxprice:number;

  /**
   * 最小库存
   */
  mininventory:number;

  /**
   * 最大库存
   */
  maxinventory:number;

  /**
   *  图片是否为空：1-是，0-否
   */
  IsBookPicNull:number;

  /**
   * 运费地址
   */
  deliveryAddress:string;

  /**
   * 模版id
   */
  templateId:string;

  /**
   * 运送方式
   */
  pricingMethod:string;

  /**
   * 运送模版最小值
   */
  templateMinPrice:string;

  /**
   * 入库时间
   */
  createTime:string;


  /**
   * 用户电话号码(打码)
   */
  phoneNumberMark:string;
}

export interface ShopGoodsForm extends BaseEntity {
  /**
   * id
   */
  id?: string | number;

  /**
   * 商品编号
   */
  itemNumber?: string;

  /**
   * 商品名称
   */
  goodsName?: string;

  /**
   * 产品编码
   */
  productId?: string;

  /**
   * isbn
   */
  isbn?: string;

  /**
   * 仓库编号
   */
  depotId?: string


  /**
   * 品相
   */
  conditionCode?: string

  /**
   * 货号
   */
  artNo?: string;

  /**
   * 库存
   */
  inventory?: number;

  /**
   * 商品定价
   */
  fixPrice?: number;

  /**
   * 货位编码
   */
  storageLocation?: string;


  /**
   * 标准售价
   */
  price?: number;

  /**
   * 批发售价
   */
  wholesalePrice?: number;


  /**
   * 单位
   */
  unit?: string;

  /**
   * 备注
   */
  remark?: string;
  /**
   * 图片
   */
  bookPic?:string;

  /**
   * 包册数
   */
  booksNumber?:number;
  /**
   * 页数
   */
  pages?:number;

  /**
   * 是否是套餐（0套餐 1非套餐）
   */
  isPackage?:number;

  /**
   * 出版社名称
   */
  publicationName?:string;

  /**
   * 出版时间
   */
  publicationTime?:string;

  /**
   * 作者
   */
  author?:string;

  /**
   * 地区
   */
  region?:string;

  /**
   * 译者
   */
  translator?:string;

  /**
   * 编者
   */
  editor?:string;

  /**
   * 书籍开本
   */
  booklets?:string;

  /**
   * 字数
   */
  wordCount?:number;

  /**
   * 装帧类型
   */
  bindType?:string;

  /**
   * 包装方式（0礼盒装 1普通包装）
   */
  packing?:number;

  /**
   * 原始货号
   */
  originalArtNo?:undefined;

  /**
   * 最小价格
   */
  minprice?:undefined;

  /**
   * 最大价格
   */
  maxprice?:undefined;

  /**
   * 最小库存
   */
  mininventory?:undefined;

  /**
   * 最大库存
   */
  maxinventory?:undefined;

  /**
   *  图片是否为空：1-是，0-否
   */
  IsBookPicNull?:undefined;
  /**
   * 运费地址
   */
  deliveryAddress?:string;

  /**
   * 模版id
   */
  templateId?:string;

  /**
   * 运送方式
   */
  pricingMethod?:string;

  /**
   * 运送模版最小值
   */
  templateMinPrice?:string;

  /**
   * 是否查寻全部商品
   */
  isQueryAllGoods?:number;


  /**
   * 是否加入分销:1-否 2-是
   */
  isJoinDistribution?:number;

  /**
   * 用户电话号码(打码)
   */
  phoneNumberMark?:string;


}

export interface ShopGoodsQuery extends PageQuery {

  /**
   * 商品编号
   */
  goodsNo?: string;

  /**
   * 商品名称
   */
  goodsName?: string;

  /**
   * 产品编码
   */
  productId?: string;

  /**
   * 商品名称
   */
  specifications?: string;

  /**
   * 货号
   */
  artNo?: string;


  /**
   * 货位编码
   */
  storageLocation?: string;

  /**
   * 标准售价
   */
  standardPrice?: number;

  /**
   * 单位
   */
  unit?: string;


  /**
   * 日期范围参数
   */
  params?: any;
  /**
   * 图片
   */
  bookPic?:string;

  // 新增数据
  /**
   * 包册数
   */
  booksNumber?:number;
  /**
   * 页数
   */
  pages?:number;

  /**
   * 是否是套餐（0套餐 1非套餐）
   */
  isPackage?:number;

  /**
   * 出版社名称
   */
  publicationName?:string;

  /**
   * 出版时间
   */
  publicationTime?:string;

  /**
   * 作者
   */
  author?:string;

  /**
   * 地区
   */
  region?:string;

  /**
   * 译者
   */
  translator?:string;

  /**
   * 编者
   */
  editor?:string;

  /**
   * 书籍开本
   */
  booklets?:string;

  /**
   * 字数
   */
  wordCount?:number;

  /**
   * 装帧类型
   */
  bindType?:string;

  /**
   * 包装方式（0礼盒装 1普通包装）
   */
  packing?:number;
  /**
   * 原始货号
   */
  originalArtNo?:undefined;

  /**
   * 最小价格
   */
  minprice?:undefined;

  /**
   * 最大价格
   */
  maxprice?:undefined;

  /**
   * 最小库存
   */
  mininventory?:undefined;

  /**
   * 最大库存
   */
  maxinventory?:undefined;

  /**
   *  图片是否为空：1-是，0-否
   */
  IsBookPicNull?:undefined;

  /**
   * 运费地址
   */
  deliveryAddress?:string;

  /**
   * 模版id
   */
  templateId?:string;

  /**
   * 运送方式
   */
  pricingMethod?:string;

  /**
   * 运送模版最小值
   */
  templateMinPrice?:string;

  /**
   * 是否查寻全部商品
   */
  isQueryAllGoods?:number;

  /**
   * 是否加入分销:1-否 2-是
   */
  isJoinDistribution?:number;

  /**
   * 仓库Id
   */
  depotId?:number;

  /**
   * 地址
   */
  address?:string;

  /**
   * 货区id列表
   */
  cargoAreaIds?:number[];

  /**
   * 是否有ISBN
   */
  isExistIsbn:number;

}


export interface StockChangeLogVo{
  /**
   * 库存操作Id
   */
  id ?:number;
  /**
   * 初始库存
   */
  beforeInv?:number;
  /**
   * 操作类型
   */
  type?:number;
  /**
   * 修改后库存
   */
  afterInv?:number;
  /**
   * 创建人
   */
  createBy?:string;
  /**
   * 创建时间
   */
  createTime?:string;


}

export interface BatchUpdateCargoVo{
  ids?:number|string;
  areas?:number|string;
  isOnline?:boolean;

}
