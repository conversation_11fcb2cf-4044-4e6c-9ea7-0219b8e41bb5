<template>
  <div class="p-2">
<!--    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">-->
<!--      <div v-show="showSearch" class="mb-[10px]">-->
<!--        <el-card shadow="hover">-->
<!--          <el-form ref="queryFormRef" :model="queryParams" :inline="true">-->
<!--            <el-form-item label="用户ID" prop="userId">-->
<!--              <el-input v-model="queryParams.userId" placeholder="请输入用户ID" clearable @keyup.enter="handleQuery" />-->
<!--            </el-form-item>-->
<!--            <el-form-item label="管理员ID" prop="adminId">-->
<!--              <el-input v-model="queryParams.adminId" placeholder="请输入管理员ID" clearable @keyup.enter="handleQuery" />-->
<!--            </el-form-item>-->
<!--            <el-form-item>-->
<!--              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>-->
<!--              <el-button icon="Refresh" @click="resetQuery">重置</el-button>  -->
<!--            </el-form-item>-->
<!--          </el-form>-->
<!--        </el-card>-->
<!--      </div>-->
<!--    </transition>-->

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
<!--          <el-col :span="1.5">-->
<!--            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['zhishu:userAudit:add']">新增</el-button>-->
<!--          </el-col>-->
<!--          <el-col :span="1.5">-->
<!--            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['zhishu:userAudit:edit']">修改</el-button>-->
<!--          </el-col>-->
<!--          <el-col :span="1.5">-->
<!--            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['zhishu:userAudit:remove']">删除</el-button>-->
<!--          </el-col>-->
<!--          <el-col :span="1.5">-->
<!--            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['zhishu:userAudit:export']">导出</el-button>-->
<!--          </el-col>-->
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="userAuditList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="ID" align="center" prop="id" :show-overflow-tooltip="true" :render-header="renderHeader">
          <template #default="{ row }">
            <div class="truncate-cell" >  <!-- 文本截断容器 -->
              {{ row.id  }}
            </div>
          </template>
        </el-table-column>

        <!--        <el-table-column label="用户ID" align="center" prop="userId" />-->
        <!--        <el-table-column label="用户昵称" align="center" prop="userName" />-->

        <el-table-column label="用户昵称" align="center" prop="userName" width="140" :show-overflow-tooltip="true" :render-header="renderHeader">

          <template #default="scope">
            <div class="truncate-cell">  <!-- 文本截断容器 -->
              <el-button link @click="handleUpdate1(scope.row)" class="apply-btn" title="点击显示详细信息">
                {{ scope.row.userName }}

              </el-button>
            </div>
            <!--@click="handleQueryInfo(scope.row)-->
          </template>
        </el-table-column>

        <!--        <el-table-column label="管理员ID" align="center" prop="adminId" />-->
        <el-table-column label="企业名称" align="center" prop="companyName" />
        <el-table-column label="企业类型" align="center" prop="companyType">
          <template #default="{ row }">
              {{ row.companyType  === '1' ? '有限责任公司' :
                 row.companyType  === '2' ? '股份有限公司' :
                   row.companyType  === '3' ? '个人独资企业' :
                   '合伙企业' }}
          </template>
        </el-table-column>
        <el-table-column label="联系人名" align="center" prop="contactPerson" />
        <el-table-column label="联系方式" align="center" prop="contactPhone" />
        <el-table-column label="邮箱" align="center" prop="email" />
        <!--        <el-table-column label="营业执照" align="center" prop="license" />-->
        <!--        <el-table-column label="备注" align="center" prop="remark" />-->
        <el-table-column label="备注" align="center" prop="remark" width="120" :show-overflow-tooltip="true" :render-header="renderHeader">
          <template #default="{ row }">
            <div class="truncate-cell" >  <!-- 文本截断容器 -->
              {{ row.remark  }}
            </div>
          </template>
        </el-table-column>
        <!--        <el-table-column label="审核状态" align="center" prop="status" />-->

        <el-table-column label="审核状态" align="center">
          <template #default="{ row }">
            <el-tag
              :type="row.status  === '0' ? 'success' : row.status === '1' ? 'danger' : 'info'"
              :effect="row.status  === '0' ? 'dark' : 'light'"
              size="large"
              :title="row.status === '1' ? LogMsg : ''"
            >
              {{ row.status  === '0' ? '通过' : row.status  === '1' ? '未通过' : '待审核' }}
            </el-tag>
          </template>
        </el-table-column>



        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
<!--            <el-tooltip content="重新提交" placement="top">-->
<!--              <el-button link type="success" icon="Check" @click="submitForm(scope.row)" v-hasPermi="['zhishu:userAudit:Resthandle']"></el-button>-->
<!--            </el-tooltip>-->
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate1(scope.row)" v-hasPermi="['zhishu:userAudit:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['zhishu:userAudit:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改列表对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body>
      <el-form ref="userAuditFormRef" :model="form" :rules="rules" label-width="80px">
        <!--        <el-form-item label="用户ID" prop="userId">-->
        <!--          <el-input v-model="form.userId" placeholder="请输入用户ID" />-->
        <!--        </el-form-item>-->
<!--        <el-form-item label="管理员ID" prop="adminId">-->
<!--          <el-input v-model="form.adminId" placeholder="请输入管理员ID" />-->
<!--        </el-form-item>-->

        <el-form-item label="企业名称" prop="userId">
          <el-input v-model="form.companyName" placeholder="请输入企业名称" />
        </el-form-item>
        <el-form-item label="企业类型" prop="companyType">
          <el-input v-model="form.companyType" placeholder="请输入企业类型" />

        </el-form-item>
        <el-form-item label="联系人名" prop="contactPerson">
          <el-input v-model="form.contactPerson" placeholder="请输入联系人名" />
        </el-form-item>
        <el-form-item label="联系方式" prop="contactPhone">
          <el-input v-model="form.contactPhone" placeholder="请输入联系方式" />
        </el-form-item>
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="form.email" placeholder="请输入邮箱" />
        </el-form-item>
        <el-form-item label="营业执照" prop="license">
          <el-upload
            class="upload-demo"
            action="https://jsonplaceholder.typicode.com/posts/"
            :on-success="handleUploadSuccess"
            :before-upload="beforeUpload"
            :limit="1"
          >
            <el-button size="small" type="primary">点击重新上传</el-button>
            <div slot="tip" class="el-upload__tip">请重新上传营业执照扫描件</div>
          </el-upload>
        </el-form-item>


<!--        <el-form-item label="营业执照" prop="license">-->
<!--          <el-input v-model="form.license" placeholder="请输入营业执照" />-->
<!--        </el-form-item>-->
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" placeholder="请输入备注" />
        </el-form-item>
<!--        <el-form-item label="审核状态" prop="status">-->
<!--          <el-radio-group v-model="form.status">-->
<!--            <el-radio :label="0" border size="large" value="0" >通过</el-radio>-->
<!--            <el-radio :label="1" border size="large" value="1">未通过</el-radio>-->
<!--            <el-radio :label="1" border size="large" value="2">待审核</el-radio>-->
<!--          </el-radio-group>-->
<!--        </el-form-item>-->
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">重新提交</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>


    <!-- 申请入驻对话框 -->
    <el-dialog :title="dialog1.title" v-model="dialog1.visible" width="50%">
      <el-form ref="settleFormRef" :model="form" :rules="rules" label-width="120px" >
        <el-form-item label="身份证正反面" prop="cardIdentity" :error="cardInfo">
          <el-upload
            style="margin-right: 40px"
            class="avatar-uploader"
            :action="uploadCardImage"
            :show-file-list="false"
            :headers="headers"
            :on-success="handleCartFrontSuccess"
            :before-upload="beforeAvatarUpload"
          >
            <img v-if="cardFronUrl" :src="cardFronUrl" class="avatar" />
            <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
            <!-- 新增提示文字 -->
            <template #tip>
              <div class="upload-tip">请上传身份证正面</div>
            </template>
          </el-upload>

          <el-upload
            class="avatar-uploader"
            :action="uploadCardImage"
            :show-file-list="false"
            :headers="headers"
            :on-success="handleCartSideSuccess"
            :before-upload="beforeAvatarUpload"
          >
            <img v-if="cartSideUrl" :src="cartSideUrl" class="avatar" />
            <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
            <template #tip>
              <div class="upload-tip">请上传身份证反面</div>
            </template>
          </el-upload>
        </el-form-item>

        <el-form-item label="联系人" prop="contactPerson">
          <el-input v-model="form.contactPerson" disabled="true" placeholder="联系人姓名"></el-input>
        </el-form-item>
        <el-form-item label="联系电话" prop="contactPhone">
          <el-input v-model="form.contactPhone" placeholder="请输入联系电话"></el-input>
        </el-form-item>

        <el-form-item label="电子邮箱" prop="email">
          <el-input v-model="form.email" placeholder="请输入电子邮箱"></el-input>
        </el-form-item>

        <el-form-item label="营业执照" prop="license" :error="licenseInfo">
          <!--          <template>-->
          <el-upload
            class="avatar-uploader"
            :action="uploadiMage"
            :show-file-list="false"
            :headers="headers"
            :on-success="handleAvatarSuccess"
            :before-upload="beforeAvatarUpload"
          >
            <img v-if="imageUrl" :src="imageUrl" class="avatar" />
            <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
            <template #tip>
              <div class="upload-tip">请上传营业执照</div>
            </template>
          </el-upload>
          <!--          </template>-->
        </el-form-item>

        <el-form-item style="display: none" label="营业执照名称:" prop="licenseName">
          <el-input v-model="form.licenseName"  placeholder="请输入营业执照名称"></el-input>
        </el-form-item>
        <el-form-item label="注册号:" prop="licenseNumber">
          <el-input v-model="form.licenseNumber" disabled="true" placeholder="营业执照注册号"></el-input>
        </el-form-item>
        <el-form-item label="企业名称" prop="companyName">
          <el-input v-model="form.companyName" disabled="true" placeholder="企业全称"></el-input>
        </el-form-item>
        <el-form-item label="店铺地址" prop="adress">
          <el-input v-model="form.adress" disabled="true" placeholder="店铺地址"></el-input>
        </el-form-item>
        <el-form-item label="企业类型" prop="companyType">
          <el-select v-model="form.companyType" placeholder="请选择企业类型">
            <el-option label="有限责任公司" value="1"></el-option>
            <el-option label="股份有限公司" value="2"></el-option>
            <el-option label="个人独资企业" value="3"></el-option>
            <el-option label="合伙企业" value="4"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="营业执照过期时间" prop="licenseTime">
          <el-date-picker
            v-model="form.licenseTime"
            date-format="YYYY/MM/DD"
            value-format="YYYY/MM/DD"
            type="date"
            placeholder="date"

          />
        </el-form-item>

        <el-form-item label="经营许可证" prop="businessLicense">
          <el-upload
            class="avatar-uploader"
            :action="imagelicenseUpload"
            :show-file-list="false"
            :headers="headers"
            :on-success="handleLicenseSuccess"
            :before-upload="beforeAvatarUpload"
          >
            <img v-if="licenseImageList" :src="licenseImageList" class="avatar" />
            <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
            <template #tip>
              <div class="upload-tip">请上传经营许可证</div>
            </template>
          </el-upload>
        </el-form-item>

        <el-form-item label="经营许可证过期时间" prop="businessLicenseTime">
          <el-date-picker
            v-model="form.businessLicenseTime"
            date-format="YYYY/MM/DD"
            value-format="YYYY/MM/DD"
            type="date"
            placeholder="date"
          />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input
            type="textarea"
            :rows="3"
            v-model="form.remark"
            placeholder="请输入其他需要说明的信息"
          ></el-input>
        </el-form-item>
      </el-form>

            <span slot="footer" class="dialog-footer">
      <!--        <el-button @click="dialogVisible = false">取 消</el-button>-->
              <el-button :loading="buttonLoading" type="primary" @click="submitForm1">重新提交</el-button>
            </span>
    </el-dialog>

  </div>
</template>

<script setup name="UserAudit" lang="ts">
import {
  listUserAudit,
  getUserAudit,
  delUserAudit,
  addUserAudit,
  updateUserAudit,
  getUAudit, getAuditLog
} from '@/api/zhishu/userAudit';
import { UserAuditVO, UserAuditQuery, UserAuditForm } from '@/api/zhishu/userAudit/types';
import { getAudit, listAudit } from '@/api/zhishu/audit';
import { AuditVO } from '@/api/zhishu/audit/types';
import { ElMessage, UploadProps } from 'element-plus';
import { getImage } from '@/api/zhishu/image';
import { globalHeaders } from '@/utils/request';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const headers = ref(globalHeaders());
const userAuditList = ref<UserAuditVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const LogMsg = ref("");

const queryFormRef = ref<ElFormInstance>();
const userAuditFormRef = ref<ElFormInstance>();
const settleFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const dialog1 = reactive<DialogOption>({
  visible: false,
  title: ''
});

const baseUrl = import.meta.env.VITE_APP_BASE_API;
const imagelicenseUpload=ref(baseUrl+'/zhishu/image/userUpload')
const uploadiMage=ref(baseUrl+'/system/newUser/licenseRedirect')
const uploadCardImage=ref(baseUrl+'/system/newUser/cardRedirect')

// 身份证正面
const cardFronUrl=ref('')
// 身份证反面
const cartSideUrl=ref('')


const cartUrl=ref([])
// 营业执照图片
const imageUrlOld = ref('')
const imageUrl = ref('')

const licenseImageListOld = ref('')
const licenseImageList=ref('')

const cardInfo=ref('')
const licenseinfo=ref('')

// 身份证正面
const handleCartFrontSuccess = async (res, file) => {
  cardInfo.value=res.data.message;
  if(res.data.url!=null){
    const oldUrl = res.data.url;
    const url = await getImage(oldUrl);
    cardFronUrl.value = url;
    cartUrl.value[0] = oldUrl;
  }
}

//身份证反面

const handleCartSideSuccess = async (res, file) => {
  if(res.data.url!=null){
    const oldUrl = res.data.url;
    const url = await getImage(oldUrl);
    cartSideUrl.value=url;
    cartUrl.value = [cartUrl.value[0] || '', oldUrl];
  }
}
// 展示营业执照

const handleAvatarSuccess = async (res, file) => {
  console.log(res);
  imageUrlOld.value = res.data.url;
  imageUrl.value = await getImage(imageUrlOld.value);
  // imageUrl.value=res.data.url;
  form.value.licenseName=res.data.name;
  form.value.licenseNumber=res.data.regNum;
  if(res.data.period!=null){
    form.value.licenseTime=res.data.period;
  }
  licenseinfo.value=res.data.message;
}

const handleLicenseSuccess= async (res, file) => {
  licenseImageListOld.value = res.data.url
  licenseImageList.value=res.data.url
  // licenseImageList.value = await getImage(licenseImageListOld.value);
}

// 上传图片前校验
const beforeAvatarUpload: UploadProps['beforeUpload'] = (rawFile) => {
  if (rawFile.type !== 'image/jpeg') {
    ElMessage.error('Avatar picture must be JPG format!')
    return false
  } else if (rawFile.size / 1024 / 1024 > 2) {
    ElMessage.error('Avatar picture size can not exceed 2MB!')
    return false
  }
  return true
}

const initFormData: UserAuditForm = {
  userId: undefined,
  userName: undefined,
  adminId: undefined,
  status: undefined,
  companyName: undefined,
  companyType: undefined,
  contactPerson: undefined,
  contactPhone: undefined,
  email: undefined,
  cardIdentity:undefined,
  licenseName:undefined,
  licenseNumber:undefined,
  license: undefined,
  remark: undefined,
  adress: undefined,
  businessLicense:undefined,
  licenseTime:undefined,
  businessLicenseTime:undefined,
}
const data = reactive<PageData<UserAuditForm, UserAuditQuery>>({
  form: {...initFormData},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    userId: undefined,
    userName: undefined,
    adminId: undefined,
    companyName : undefined,
    companyType : undefined,
    contactPerson : undefined,
    contactPhone : undefined,
    email : undefined,
    cardIdentity:undefined,
    licenseName:undefined,
    licenseNumber:undefined,
    license : undefined,
    remark : undefined,
    status: undefined,
    adress: undefined,
    businessLicense:undefined,
    licenseTime:undefined,
    businessLicenseTime:undefined,
    params: {
    }
  }
  // rules: {
  // }
});

// 文件上传成功处理
const handleUploadSuccess: UploadProps['onSuccess'] = (response, file) => {
  form.value.license = response.data.url; // 根据实际API返回调整
  ElMessage.success('上传成功');
};

// 文件上传前验证
const beforeUpload: UploadProps['beforeUpload'] = (file) => {
  const isJPG = file.type === 'image/jpeg' || file.type === 'image/png';
  const isLt2M = file.size / 1024 / 1024 < 2;

  if (!isJPG) {
    ElMessage.error('上传图片只能是 JPG/PNG 格式!');
  }
  if (!isLt2M) {
    ElMessage.error('上传图片大小不能超过 2MB!');
  }
  return isJPG && isLt2M;
};



// 表单验证规则
const rules = {
  companyName: [
    { required: true, message: '仓库名称不能为空', trigger: 'blur' },
    { max: 20, message: '仓库名称长度不能超过20位', trigger: 'blur' }
  ],
  contactPerson: [
    { required: true, message: '请输入联系人姓名', trigger: 'blur' },
    { max: 8, message: '联系人姓名长度不能超过8位', trigger: 'blur' }
    // { required: true, message: '请输入联系人姓名', trigger: 'blur' }
  ],
  contactPhone: [
    { required: true, message: '请输入联系电话', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱地址', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱地址', trigger: ['blur', 'change'] }
  ],
  companyType: [
    { required: true, message: '请选择企业类型', trigger: 'change' }
  ],
  // licenseName: [
  //   { required: true, message: '请输入营业执照名称', trigger: 'change' }
  // ],
  licenseNumber: [
    { required: true, message: '请输入营业执照注册号', trigger: 'change' }
  ],
  license: [
    { required: true, message: '请上传营业执照', trigger: 'change' }
  ],
  adress:[
    { required: true, message: '请输入店铺地址', trigger: 'change' }

  ],
  cardIdentity: [
    { required: true },
    {
      validator: (rule, value, callback) => {
        if (cartUrl.value[0] == null && cartUrl.value[1] == null) {
          callback(new Error('请上传身份证正反面照片'));
        } else if (cartUrl.value[0] == null) {
          callback(new Error('请上传身份证正面照片'));
        } else if (cartUrl.value[1] == null) {
          callback(new Error('请上传身份证反面照片'));
        } else {
          callback();
        }
      },
      trigger: 'change'
    }
  ]
}

const { queryParams, form } = toRefs(data);

// 获取审核数据返回的信息
const getLog = async ()=>{
  const res = await getAuditLog();
  try {
    LogMsg.value=res.data.remark;
  }catch (error){

  }
}





/** 查询列表列表 */
const getList = async () => {
  loading.value = true;
  const res = await listUserAudit(queryParams.value);
  userAuditList.value = res.rows;
  total.value = res.total;
  getLog();
  loading.value = false;
}


/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
}

/** 表单重置 */
const reset = () => {
  form.value = {...initFormData};
  userAuditFormRef.value?.resetFields();
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
}

/** 多选框选中数据 */
const handleSelectionChange = (selection: UserAuditVO[]) => {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

const  handleQueryInfo=async (row?:AuditVO)=>{
  reset();
  const _id = row?.id || ids.value[0]
  const res = await getUAudit(_id);
  Object.assign(form.value, res.data);
}

/** 修改按钮操作 */
const handleUpdate1 = async (row?: UserAuditVO) => {
  reset();
  const _id = row?.id || ids.value[0]
  const res = await getUserAudit(_id);
  imageUrlOld.value = res.data.license;
  imageUrl.value= await getImage(res.data.license);
  licenseImageListOld.value = res.data.businessLicense;
  licenseImageList.value=licenseImageListOld.value
  // console.log( licenseImageListOld.value)
  // licenseImageList.value=await getImage(res.data.businessLicense);
    try {
      const urlArray = JSON.parse(res.data.cardIdentity);
      cardFronUrl.value = await getImage(urlArray[0]); // 第一个URL
      cartSideUrl.value = await getImage(urlArray[1]); // 第二个URL
      cartUrl.value = [urlArray[0],urlArray[1]];
    } catch (error) {}
  Object.assign(form.value, res.data);

  dialog1.visible=true;
  dialog1.title = "重新入驻";
}





/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = "添加列表";
}

/** 修改按钮操作 */
// const handleUpdate = async (row?: UserAuditVO) => {
//   reset();
//   const _id = row?.id || ids.value[0]
//   const res = await getUserAudit(_id);
//   imageUrl.value=res.data.license;
//   try {
//     const urlArray = JSON.parse(res.data.cardIdentity);
//     cardFronUrl.value = urlArray[0]; // 第一个URL
//     cartSideUrl.value = urlArray[1]; // 第二个URL
//   } catch (error) {
//
//   }
//   Object.assign(form.value, res.data);
//   dialog1.visible=true;
//   // dialog.visible = true;
//   dialog1.title = "修改列表";
// }

/** 提交按钮 */
const submitForm = () => {
  userAuditFormRef.value?.validate(async (valid: boolean) => {
    // if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateUserAudit(form.value).finally(() =>  buttonLoading.value = false);
      } else {
        await addUserAudit(form.value).finally(() =>  buttonLoading.value = false);
      }
      proxy?.$modal.msgSuccess("操作成功");
      dialog.visible = false;
      await getList();
    // }
  });
}

// 提交按钮
const submitForm1 = () => {
  settleFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        alert(imageUrlOld.value);

        form.value.license=imageUrlOld.value;
        form.value.cardIdentity=JSON.stringify(cartUrl.value);
        form.value.businessLicense=licenseImageListOld.value;
        await updateUserAudit(form.value).finally(() => buttonLoading.value = false);
      } else {
        await addUserAudit(form.value).finally(() => buttonLoading.value = false);
      }
      proxy?.$modal.msgSuccess("操作成功");
      dialog1.visible = false;
      await getList();

    }
  });
}






/** 删除按钮操作 */
const handleDelete = async (row?: UserAuditVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除列表编号为"' + _ids + '"的数据项？').finally(() => loading.value = false);
  await delUserAudit(_ids);
  proxy?.$modal.msgSuccess("删除成功");
  await getList();
}

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download('zhishu/userAudit/export', {
    ...queryParams.value
  }, `userAudit_${new Date().getTime()}.xlsx`)
}

onMounted(() => {
  getList();
});
</script>
<style>
/* 文本溢出处理 */
.truncate-cell {
  width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.avatar-uploader{
  border: 1px dashed var(--el-border-color);
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: var(--el-transition-duration-fast);
}

.avatar-uploader:hover {
  border-color: var(--el-color-primary);
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 140px;
  height: 120px;
  text-align: center;
}
.upload-tip {
  margin-top: 8px;
  width: 100%; /* 与上传区域同宽 */
  text-align: center; /* 文字居中 */
}
</style>
<style scoped>
.avatar-uploader .avatar {
  width: 178px;
  height: 178px;
  display: block;
}
</style>
