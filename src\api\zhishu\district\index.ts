import request from '@/utils/request';
import type { R } from '@/api/types';
import { TDistrictVo, templateData } from '@/api/zhishu/district/types';
import { DepotQuery, DepotVO } from '@/api/zhishu/depot/types';
import { AxiosPromise } from 'axios';

// 获取所有省级数据
export function getProvinces() {
  return request.get<R<TDistrictVo[]>>('/district/provinces');
}

export function getCitiesByProvinceId(provinceId: number) {
  return request.get<R<TDistrictVo[]>>(`/district/cities/${provinceId}`);
}

export function getDistrictsByCityId(cityId: number) {
  return request.get<R<TDistrictVo[]>>(`/district/districts/${cityId}`);
}

/**
 * 获取省级市级
 * @returns
 */
export const getDistrictTree = () => {
  return request({
    url: '/district/getDistrictTree',
    method: 'get'
  });
}

// 新增部门
export const createTemplate = (data: templateData) => {
  return request({
    url: '/logistics/logistics',
    method: 'post',
    data: data
  });
};
export const getFreInfo = (id: string | number): AxiosPromise<templateData> => {
  return request({
    url: '/logistics/logistics/' + id,
    method: 'get'
  });
};
export const UpdateTemplate = (data: templateData) => {
  return request({
    url: '/logistics/logistics',
    method: 'put',
    data: data
  });
};
