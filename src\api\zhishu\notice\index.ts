import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { NoticeVO, NoticeForm, NoticeQuery } from '@/api/zhishu/notice/types';

/**
 * 查询消息通知列表
 * @param query
 * @returns {*}
 */

export const listNotice = (query?: NoticeQuery): AxiosPromise<NoticeVO[]> => {
  return request({
    url: '/zhishu/notice/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询消息通知详细
 * @param id
 */
export const getNotice = (id: string | number): AxiosPromise<NoticeVO> => {
  return request({
    url: '/zhishu/notice/' + id,
    method: 'get'
  });
};

/**
 * 新增消息通知
 * @param data
 */
export const addNotice = (data: NoticeForm) => {
  return request({
    url: '/zhishu/notice',
    method: 'post',
    data: data
  });
};

/**
 * 修改消息通知
 * @param data
 */
export const updateNotice = (data: NoticeForm) => {
  return request({
    url: '/zhishu/notice',
    method: 'put',
    data: data
  });
};

/**
 * 删除消息通知
 * @param id
 */
export const delNotice = (id: string | number | Array<string | number>) => {
  return request({
    url: '/zhishu/notice/' + id,
    method: 'delete'
  });
};
