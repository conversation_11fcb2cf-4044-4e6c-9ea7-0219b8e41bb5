import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { PriceTemplateVO, PriceTemplateForm, PriceTemplateQuery } from '@/api/zhishu/priceTemplate/types';

/**
 * 查询价格模板列表
 * @param query
 * @returns {*}
 */

export const listPriceTemplate = (query?: PriceTemplateQuery): AxiosPromise<PriceTemplateVO[]> => {
  return request({
    url: '/zhishu/priceTemplate/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询价格模板详细
 * @param id
 */
export const getPriceTemplate = (id: string | number): AxiosPromise<PriceTemplateVO> => {
  return request({
    url: '/zhishu/priceTemplate/' + id,
    method: 'get'
  });
};

/**
 * 新增价格模板
 * @param data
 */
export const addPriceTemplate = (data: PriceTemplateForm) => {
  return request({
    url: '/zhishu/priceTemplate',
    method: 'post',
    data: data
  });
};

/**
 * 修改价格模板
 * @param data
 */
export const updatePriceTemplate = (data: PriceTemplateForm) => {
  return request({
    url: '/zhishu/priceTemplate',
    method: 'put',
    data: data
  });
};

/**
 * 删除价格模板
 * @param id
 */
export const delPriceTemplate = (id: string | number | Array<string | number>) => {
  return request({
    url: '/zhishu/priceTemplate/' + id,
    method: 'delete'
  });
};
