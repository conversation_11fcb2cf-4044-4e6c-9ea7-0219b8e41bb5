// 省市区数据接口
export interface TDistrictVo {
  /** ID */
  id: number;
  /** 父ID */
  pid: number;
  /** 名称 */
  name: string;
  /** 层级 0:省 1:市 2:区县 */
  level: number;
  /** 状态 0:正常 */
  status: number;
}

/**
 * 部门表单类型
 */
export interface templateData {
  id?: number;
  template_name?: string;
  delivery_province?: number;
  delivery_city?: number;
  delivery_area?: number;
  pricing_method?: number;
  shipping?: string;
  // 使用 Record 来定义 shipping_range 的结构
  shipping_range?: Record<string, (number | string)[]>; // 考虑到数组中既包含数字也包含字符串
  warehouse_id?: number;
  remark?:string;
}
