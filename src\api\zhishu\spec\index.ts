import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { SpecVO, SpecForm, SpecQuery } from '@/api/zhishu/spec/types';

/**
 * 查询自定义规格设置列表
 * @param query
 * @returns {*}
 */

export const listSpec = (query?: SpecQuery): AxiosPromise<SpecVO[]> => {
  return request({
    url: '/zhishu/spec/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询自定义规格设置详细
 * @param id
 */
export const getSpec = (id: string | number): AxiosPromise<SpecVO> => {
  return request({
    url: '/zhishu/spec/' + id,
    method: 'get'
  });
};

export const getSpecByShopId = (shopId: string | number): AxiosPromise<SpecVO> => {
  return request({
    url: '/zhishu/spec/getInfoByShopId/' + shopId,
    method: 'get'
  });
};

/**
 * 新增自定义规格设置
 * @param data
 */
export const addSpec = (data: SpecForm) => {
  return request({
    url: '/zhishu/spec',
    method: 'post',
    data: data
  });
};

/**
 * 修改自定义规格设置
 * @param data
 */
export const updateSpec = (data: SpecForm) => {
  return request({
    url: '/zhishu/spec',
    method: 'put',
    data: data
  });
};

/**
 * 删除自定义规格设置
 * @param id
 */
export const delSpec = (id: string | number | Array<string | number>) => {
  return request({
    url: '/zhishu/spec/' + id,
    method: 'delete'
  });
};
