<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="店铺" prop="shopId">
              <el-select v-model="queryParams.shopId" placeholder="请选择店铺">
                <el-option v-for="item in shopList" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="	商家编码" prop="itemNumber">
              <el-input v-model="queryParams.itemNumber" placeholder="请输入编码" clearable @keyup.enter="handleQuery" />
            </el-form-item>
<!--            <el-form-item label="品相" prop="conditionCode" >-->
<!--              <el-select v-model="queryParams.conditionCode" value-key="id" placeholder="请选择品相" :reserve-keyword="false"  :loading="loading">-->
<!--                <el-option v-for="item in conditionCodeTypes" :key="item.value" :label="item.label" :value="item.label"/>-->
<!--              </el-select>-->
<!--            </el-form-item>-->
            <!-- <el-form-item label="货区名称" prop="depotId" >
              <el-select v-model="selectedId"  value-key="id" placeholder="请选择一级货区" :reserve-keyword="false"  clearable filterable :loading="loading"  @update:model-value="handleDepotChange">
                <el-option v-for="item in depotList" :key="item.id" :label="item.name+''+item.unit" :value="item"  />
              </el-select>
            </el-form-item> -->
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['zhishu:shopGoodsPublished:remove']">删除</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="shopGoodsPublishedList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
<!--        <el-table-column label="主键" align="center" prop="id" v-if="false" />-->
<!--        <el-table-column label="商品编号" align="left" prop="itemNumber" width="110" />-->
        <el-table-column label="图示" align="center" prop="bookPic" :show-overflow-tooltip="true"  >
          <template #default="{ row }">
            <el-image
              style="width: 50px;height: 50px"
              :src="row.bookPic"
              fit="scale-down"
              :preview-src-list="[row.bookPic]"
              preview-teleported
            >
              <template #error>
                <div class="image-slot">
                  暂无
                </div>
              </template>
            </el-image>
          </template>
        </el-table-column>
        <el-table-column label="商家编码" align="left" prop="itemNumber"/>

        <el-table-column label="线上商品名称" align="left" prop="goodsName" :show-overflow-tooltip="true" >
          <template #default="{ row }">
            <div class="truncate-cell">  <!-- 文本截断容器 -->
              <el-link
                v-if="row.shopType === 1"
                type="primary"
                :href="`https://mobile.yangkeduo.com/goods.html?goods_id=${row.onlineId}`"
                target="_blank"
                :underline="false"
              >
                {{ row.goodsName }}
              </el-link>

              <el-link
                v-else-if="row.shopType === 2"
                type="primary"
                :href="`https://book.kongfz.com/${row.mallId}/${row.onlineId}`"
                target="_blank"
                :underline="false"
              >
                {{ row.goodsName }}
              </el-link>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="店铺" align="left" prop="shopName"/>

        <el-table-column label="线上商品/规格ID" align="left" prop="onlineId"/>

        <el-table-column label="状态" align="left" prop="status">
          <template #default="{ row }">
            <span v-if="row.status==0">在售</span>
            <span v-else>未售</span>
          </template>
        </el-table-column>

      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>

  </div>
</template>

<script setup name="ShopGoodsPublished" lang="ts">
import { listShopGoodsPublished, getShopGoodsPublished, delShopGoodsPublished, addShopGoodsPublished, updateShopGoodsPublished } from '@/api/zhishu/shopGoodsPublished';
import { ShopGoodsPublishedVO, ShopGoodsPublishedQuery, ShopGoodsPublishedForm } from '@/api/zhishu/shopGoodsPublished/types';
import { getListShop } from '@/api/zhishu/shop';
import { ref } from 'vue';
import { DepotVO } from '@/api/zhishu/depot/types';
import { depotNameList } from '@/api/zhishu/shelves';
import Link from '@/layout/components/Sidebar/Link.vue';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const shopGoodsPublishedList = ref<ShopGoodsPublishedVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const shopGoodsPublishedFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: ShopGoodsPublishedForm = {
  id: undefined,
  shopGoodsId: undefined,
  shopId: undefined,
  goodsName: undefined,
  shopName: undefined,
  platformId: undefined,
  status: undefined,
  itemNumber:undefined,
  bookPic:undefined,
  isbn:undefined,
  conditionCode:undefined,
  price:undefined,
  createTime:undefined,
  updateTime:undefined
}
const data = reactive<PageData<ShopGoodsPublishedForm, ShopGoodsPublishedQuery>>({
  form: {...initFormData},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    shopGoodsId: undefined,
    shopId: undefined,
    goodsName: undefined,
    shopName: undefined,
    platformId: undefined,
    status: undefined,
    itemNumber:undefined,
    bookPic:undefined,
    isbn:undefined,
    conditionCode:undefined,
    price:undefined,
    createTime:undefined,
    updateTime:undefined,
    depotId:undefined,
    params: {
    }
  },
  rules: {
    id: [
      { required: true, message: "主键不能为空", trigger: "blur" }
    ],
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询记录发布数据列表 */
const getList = async () => {
  loading.value = true;
  const res = await listShopGoodsPublished(queryParams.value);
  shopGoodsPublishedList.value = res.rows;
  total.value = res.total;
  loading.value = false;
}



const depotList = ref<DepotVO[]>([])
interface DepotOption {
  id: number
  name: string
}

const selectedId = ref<DepotOption | null>(null) // 严格匹配选项类型
const handleDepotChange = (val: DepotOption | null) => {
  queryParams.value.depotId  = val?.id || null
}
const loadData = async () => {
  loading.value  = true
  try {
    const res = await   depotNameList()
    depotList.value=res.rows
  } catch (error) {
  } finally {
    loading.value  = false
  }
}

const conditionCodeTypes = [
  { value: '1', label: '一品' },
  { value: '2', label: '二品' },
  { value: '3', label: '三品' },
  { value: '4', label: '四品' },
  { value: '5', label: '五品' },
  { value: '6', label: '六品' },
  { value: '6.5', label: '六五品' },
  { value: '7', label: '七品' },
  { value: '7.5', label: '七五品' },
  { value: '8', label: '八品' },
  { value: '8.5', label: '八五品' },
  { value: '9', label: '九品' },
  { value: '9.5', label: '九五品' },
  { value: '10', label: '全新' }

];



/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
}

/** 多选框选中数据 */
const handleSelectionChange = (selection: ShopGoodsPublishedVO[]) => {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}


/** 删除按钮操作 */
const handleDelete = async (row?: ShopGoodsPublishedVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除记录发布数据编号为"' + _ids + '"的数据项？').finally(() => loading.value = false);
  await delShopGoodsPublished(_ids);
  proxy?.$modal.msgSuccess("删除成功");
  await getList();
}

//店铺列表
const shopList = [];

const getShopList = async () => {
  const res = await getListShop();
  if (res.length == 0) {
    shopList.length = 0;
  } else {
    for (var i = 0; i < res.length; i++) {
      shopList.push({
        value: res[i].id,
        label: res[i].shopName
      });
    }
  }
}

onMounted(() => {
  getList();
  loadData();
  getShopList();
});
</script>
<style scoped>
/* 调整表头字体大小 */
:deep(.el-table__header) th .cell {
  font-size: 18px; /* 设置你需要的字体大小 */
  font-weight: bold; /* 可选：加粗字体 */
}

/* 调整表格内容字体大小 */
.el-table {
  font-size: 17px !important; /* 与你的行内样式一致 */
}
</style>
